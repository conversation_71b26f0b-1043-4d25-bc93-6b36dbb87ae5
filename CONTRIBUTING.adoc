= CONTRIBUTING
:toc: right
:toclevels: 3
:tip-caption: 💡
:nofooter:

== 编译环境

=== 前置条件

- Node.js

* 安装 Node.js 20.0+
* 安装 pnpm

- Windows 系统

* 打开 Unicode 支持
image:docs/imgs/readme_1.png[1.png]

* 安装 Visual Studio 2022 并安装 `使用C++的桌面开发组件`
+
https://visualstudio.microsoft.com/zh-hans/downloads/[Visual Studio 下载地址]
image:docs/imgs/readme_2.png[2.png]

* 下载与 `pom.xml` 文件版本一致的 GraalVM 并配置环境变量 `GRAALVM_HOME`
+
https://github.com/graalvm/graalvm-ce-builds/releases/[GraalVM 下载地址]

=== 前端编译

[source,powershell]
----
cd ./docs/pages
pnpm install --frozen-lockfile
pnpm run build
----

=== 编译

[source,powershell]
----
mvn clean -Pnative native:compile
----
