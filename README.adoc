= 自动申报
:toc: right
:toclevels: 3
:tip-caption: 💡
:warning-caption: ⚠️
:nofooter:
:!webfonts:
:data-uri:

++++
<link rel="stylesheet" href="file:///R:/Files/Workspace/Mine/Scripts/js/asciidoc/image-modal/index.css">
<script src="file:///R:/Files/Workspace/Mine/Scripts/js/asciidoc/image-modal/index.js"></script>
++++

== 环境搭建

[TIP]
默认安装文件夹位置 `D:\spot\`，如有不同请替换配置文件中安装路径，详细参考配置文件修改

=== 文件列表

|===
|文件名 |描述

|AutoReport.exe |服务状态管理程序
|application.yaml |程序配置文件
|auto-report.exe |服务主体程序
|login.js |用于配置自动登录
|logs |日志文件夹
|spot.db | 数据库文件
|===

== 配置文件修改

[TIP]
如安装目录不是 `D:/spot/` ，需更改以下文件，把 `D:/spot/` 改为实际的文件夹。#注意正反斜杠#。仅列出了需修改的行。

=== application.yml

[source,yaml]
----
logging:
  file:
    name: D:/spot/logs
spring:
  datasource:
    url: ***************************
----

== 数据库配置

=== 配置账号

`system_account_config`

|===
|字段 |Info |获取方式

|`plantId` |场站 ID |保证唯一即可
|`plantName` |场站名称 |暂时不用
|`uKeyCode` |UKey内容 |image:docs/imgs/deploy_1.png[1.png]
|`type` |场站类型 |0: 风电, 1: 光伏
|`userName` |用户登录账号 |-
|`password` |用户登录密码 |-
|`uKeyPrivate` |暂时不用 |-
|`uKeyPassowrd` |暂时不用 |-
|`grayTag` |用户ID |image:docs/imgs/deploy_2.png[2.png]
|`phone` |手机号 |-
|`useUKey` |使用UKey登录 {0: 不使用, 1: 使用} |-
|`useSMS` |使用短信登录 {0: 不使用, 1: 使用} |-
|`smsRegex` |短信验证码提取正则 |-
|===

=== 配置交易单元

`system_unit_config`

|===
|字段 |Info |获取方式

|`unitId` |交易单元 ID |image:docs/imgs/deploy_3.png[3.png]
|`unitName` |交易单元名称 |同上
|`dispatchId` |交易单元调度 ID |同上
|`dispatchName` |交易单元调度名称 |同上
|`plantId` |场站 ID |与账号的 `plantId` 保持一致
|`grayTag` |交易中心场站 ID |与账号的 `grayTag` 保持一致
|`limitPower` |场站装机，省间申报不查询限额时根据装机容量比例 |
|`uName` |场站名称，用于页面显示 |
|`unitIdIlt` |中长期交易单元 ID |
|`unitNameIlt` |中长期交易单元名称 |
|`generatorSetId` |机组 ID |
|===

== 服务管理

双击``AutoReport.exe``启动管理程序
