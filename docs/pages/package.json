{"name": "auto-report", "version": "3.0.0", "description": "", "license": "ISC", "type": "module", "scripts": {"_install": "npm i", "dev": "vite", "build": "vite build"}, "dependencies": {"axios": "1.11.0", "dayjs": "1.11.13", "exceljs": "4.4.0", "lodash": "4.17.21", "naive-ui": "2.42.0", "pinia": "3.0.3", "vue": "3.5.18", "vue-router": "4.5.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz"}, "devDependencies": {"@types/lodash": "4.17.20", "@types/node": "24.2.1", "@vicons/fa": "0.13.0", "@vitejs/plugin-basic-ssl": "2.1.0", "@vitejs/plugin-vue": "6.0.1", "@vitejs/plugin-vue-jsx": "5.0.1", "prettier": "3.6.2", "sass": "1.90.0", "typescript": "5.9.2", "unplugin-auto-import": "20.0.0", "unplugin-vue-components": "29.0.0", "vite": "7.1.1"}}