{
    "compilerOptions": {
        // ========== Type Checking ==========
        "strict": false,
        "noImplicitAny": false,
        // ========== Modules ==========
        "baseUrl": ".",
        "paths": {
            "@/*": ["./src/*"]
        },
        "module": "esnext",
        "moduleResolution": "bundler",
        "types": ["vite/client"],
        // ========== Emit ==========
        "importHelpers": true,
        "sourceMap": true,
        // ========== JavaScript Support ==========
        "allowJs": true,
        "checkJs": false,
        // ========== Interop Constraints ==========
        "allowSyntheticDefaultImports": true,
        "esModuleInterop": true,
        "isolatedModules": true,
        // ========== Language and Environment ==========
        "jsx": "preserve",
        "lib": ["ESNext", "DOM", "DOM.Iterable", "ScriptHost"],
        "target": "esnext",
        // ========== Completeness ==========
        "skipLibCheck": true
    },
    "include": ["src/**/*.js", "src/**/*.ts", "src/**/*.jsx", "src/**/*.tsx", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts"],
    "exclude": ["node_modules"]
}
