i {
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
@font-face {
    font-family: MPlusWoff2;
    font-style: normal;
    font-weight: normal;
    src: url('./mplus.woff2') format('woff2');
}
@font-face {
    font-family: MononokiWoff2;
    font-style: normal;
    font-weight: normal;
    src: url('./mononoki.woff2') format('woff2');
}
* {
    font-family: 'MPlusWoff2', 'Sarasa Mono SC', 'MononokiWoff2', sans-serif;
}
*::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: rgba(214, 214, 214, 0.2);
}
*::-webkit-scrollbar-thumb {
    background-color: rgba(59, 178, 237, 0.22);
    border-radius: 6px;
    transition: background-color 0.3s;
}
*::-webkit-scrollbar-thumb:hover {
    background-color: rgba(75, 175, 225, 1);
}
*::-webkit-scrollbar-thumb:active {
    background-color: rgba(100, 200, 250, 1);
}
*::-webkit-scrollbar-corner {
    background-color: rgba(100, 200, 250, 1);
    border-radius: 2px;
    transition: background-color 0.3s;
}
*::-webkit-scrollbar-corner:hover {
    background-color: rgba(100, 200, 250, 0.5);
}
