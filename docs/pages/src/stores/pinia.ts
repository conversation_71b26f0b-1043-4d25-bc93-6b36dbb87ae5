import { createPinia } from 'pinia';

console.log('pinia init');
const pinia = createPinia().use((context: any) => {
    const storage = context?.options?.always ? localStorage : sessionStorage;
    const key = 'store_' + context.store.$id; // 使用 store 的 id 作为 key
    const storedState = storage.getItem(key);
    if (storedState) {
        context.store.$patch(JSON.parse(storedState));
    }
    context.store.$subscribe((mutation: any, state: any) => {
        storage.setItem(key, JSON.stringify(state));
    });
});

export default pinia;
