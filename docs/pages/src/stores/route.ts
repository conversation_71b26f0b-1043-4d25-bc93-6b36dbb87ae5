import { defineStore } from 'pinia';
import { ref } from 'vue';

const lastVisitRouteKey = 'routeStore_lastVisitRoute';
export const useRouteStore = defineStore('route', () => {
    const lastVisitRoute = ref(localStorage.getItem(lastVisitRouteKey));
    watch(lastVisitRoute, () => {
        if (lastVisitRoute.value) {
            localStorage.setItem(lastVisitRouteKey, lastVisitRoute.value);
        }
    });
    return { lastVisitRoute };
});
