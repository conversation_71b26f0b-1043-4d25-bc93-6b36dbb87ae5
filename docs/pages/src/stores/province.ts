import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useProvinceStore = defineStore('province', () => {
    const provinceCode = ref('');
    const queryLimit = ref('true');
    const provinceList: Ref<{ value: string; label: string }[]> = ref([]);

    function setConfigs(config: { provinceCode: string; queryLimit: string; provinceList: { value: string; label: string }[] }) {
        provinceCode.value = config.provinceCode;
        queryLimit.value = config.queryLimit;
        provinceList.value = config.provinceList;
    }

    function getProvinceList(): { label: string; value: string }[] {
        return provinceList.value.filter((province) => province.value === provinceCode.value);
    }

    return { provinceCode, queryLimit, setConfigs, getProvinceList };
});
