import { defineStore } from 'pinia';
import { ref } from 'vue';
import { debounce } from 'lodash';

export const useCommonStore = defineStore(
    'common',
    () => {
        const windowResize = ref(0);
        const theme = ref('light');

        const onResize = debounce(() => {
            windowResize.value = (windowResize.value + 1) % 10000;
        }, 100);

        return { windowResize, theme, onResize };
    },
    { always: true } as any
);
