import service from '@/api/index';
import { Result } from '@/types';

export function saveConfigList<T>(data: any): Promise<Result<T>> {
    return service.put('/api/v1/opbd/configList', data);
}

export function queryConfigList<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/opbd/configList?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function queryRecordList<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/opbd/recordList?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function removeRecord<T>(data: any): Promise<Result<T>> {
    return service.delete('/api/v1/opbd/recordList', { data });
}

export function importConfigList<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/opbd/importConfigList', data);
}

export function exportRecordList<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/opbd/exportRecordList', data);
}

export function exportConfigTemplate<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/opbd/exportConfigTemplate', data);
}

export function queryRangeEnd<T>(): Promise<Result<T>> {
    return service.get('/api/v1/opbd/rangeEnd');
}

export function saveRangeEnd<T>(data: any): Promise<Result<T>> {
    return service.put('/api/v1/opbd/rangeEnd', data);
}
