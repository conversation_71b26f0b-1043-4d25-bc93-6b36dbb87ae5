import service from '@/api/index';
import { Result } from '@/types';

export function unitListReq<T>(): Promise<Result<T>> {
    return service.get('/api/v1/proxyPurchase/unitList');
}

export function queryConfigListReq<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/proxyPurchase/configList?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function queryRecordListReq<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/proxyPurchase/recordList?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function saveConfigReq<T>(data: any): Promise<Result<T>> {
    return service.put('/api/v1/proxyPurchase/configList', data);
}

export function exportRecordReq<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/proxyPurchase/exportRecordList', data);
}

export function exportAllRecordReq<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/proxyPurchase/exportAllRecordList', data);
}

export function updateByParamsReq<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/job/proxyPurchase/updateByParams?param=' + encodeURIComponent(JSON.stringify(params)));
}
