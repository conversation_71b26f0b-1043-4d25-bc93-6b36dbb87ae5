import service from '@/api/index';
import { Result } from '@/types';

export function queryConfigList<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/dayRolling/configList?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function queryRecordList<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/dayRolling/recordList?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function saveConfigList<T>(data: any): Promise<Result<T>> {
    return service.put('/api/v1/dayRolling/configList', data);
}

export function exportRecordList<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/dayRolling/exportRecordList', data);
}

export function exportAllRecordList<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/dayRolling/exportAllRecordList', data);
}

export function callDetectJobByUnitId<T>(params: any): Promise<Result<T>> {
    return service.get('/job/dayRolling/detectJobByUnitId?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function queryInfoTradeList<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/dayRollingInfo/tradeList?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function queryInfoMarketList<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/dayRollingInfo/marketList?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function queryInfoUnitList<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/dayRollingInfo/unitList?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function importMarketInfo<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/dayRollingInfo/importMarketList', data);
}

export function importUnitInfo<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/dayRollingInfo/importUnitList', data);
}

export function crawlerInfo<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/dayRollingInfoJob/crawler?param=' + encodeURIComponent(JSON.stringify(params)));
}
