import service from '@/api/index';
import { Result } from '@/types';

export function queryConfigList<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/sd-spot/configList?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function queryConfigListByDateRange<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/sd-spot/configList/range?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function saveConfigList<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/sd-spot/configList', data);
}

export function updateReportStatus<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/sd-spot/updateStatus', data);
}

export function deleteConfig<T>(data: any): Promise<Result<T>> {
    return service.delete('/api/v1/sd-spot/configList', { data });
}
