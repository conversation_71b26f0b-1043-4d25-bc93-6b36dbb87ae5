import service from '@/api/index';
import { Result } from '@/types';

export function saveConfigList<T>(data: any): Promise<Result<T>> {
    return service.put('/api/v1/opid/configList', data);
}

export function queryConfigList<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/opid/configList?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function queryRecordList<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/opid/recordList?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function removeRecord<T>(data: any): Promise<Result<T>> {
    return service.delete('/api/v1/opid/recordList', { data });
}

export function importConfigList<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/opid/importConfigList', data);
}

export function exportRecordList<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/opid/exportRecordList', data);
}

export function exportConfigTemplate<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/opid/exportConfigTemplate', data);
}

export function queryTimeConfig<T>(): Promise<Result<T>> {
    return service.get('/api/v1/opid/timeConfig');
}

export function saveTimeConfig<T>(data: any): Promise<Result<T>> {
    return service.put('/api/v1/opid/timeConfig', data);
}

export function triggerSpotNow<T>(data: any): Promise<Result<T>> {
    return service.post('/job/opid/spotNow', data);
}
