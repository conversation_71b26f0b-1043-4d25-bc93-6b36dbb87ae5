import axios from 'axios';

const service = axios.create({
    baseURL: import.meta.env.VITE_BASE_URL,
    timeout: 300000
});

service.interceptors.response.use(
    (response) => {
        if (response.status === 200) {
            if (response.data.code !== 1 && response.data.msg !== undefined) {
                window.$message.error(response.data.msg);
            }
            return response.data;
        } else {
            window.$message.error('请求失败');
            console.warn('请求失败', response);
            return Promise.reject('请求失败');
        }
    },
    (error) => {
        window.$message.error('请求失败');
        console.warn('请求失败', error);
        return Promise.reject('请求失败');
    }
);

export default service;
