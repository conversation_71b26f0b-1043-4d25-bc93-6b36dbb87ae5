import service from '@/api/index';
import { Result } from '@/types';

export function queryConfigList<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/imqpNewEnergyTrade/configList?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function queryRecordList<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/imqpNewEnergyTrade/recordList?param=' + encodeURIComponent(JSON.stringify(params)));
}

export function saveConfigList<T>(data: any): Promise<Result<T>> {
    return service.put('/api/v1/imqpNewEnergyTrade/configList', data);
}

export function exportRecordList<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/imqpNewEnergyTrade/exportRecordList', data);
}

export function exportAllRecordList<T>(data: any): Promise<Result<T>> {
    return service.post('/api/v1/imqpNewEnergyTrade/exportAllRecordList', data);
}

export function callDetectJobByUnitId<T>(params: any): Promise<Result<T>> {
    return service.get('/api/v1/job/imqpNewEnergyTrade/detectJobByUnitId?param=' + encodeURIComponent(JSON.stringify(params)));
}
