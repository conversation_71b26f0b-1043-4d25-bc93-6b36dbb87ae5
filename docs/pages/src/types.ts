import { RouteMeta } from 'vue-router';
import { VNode } from 'vue';

declare global {
    interface Window {
        $message: any;
    }
}

export interface Result<T> {
    code: number;
    msg: string;
    data: T;
}

export interface MenuMeta extends RouteMeta {
    type: MenuType;
    order: number;
    hasPermission: boolean;
    icon: VNode;
    roleType?: RoleType;
}

export enum MenuType {
    INDEX = 1,
    MENU = 2
}

export enum RoleType {
    AUTO_LOGIN = 'AUTO_LOGIN',
    LOGS = 'LOGS',
    OPID = 'OPID',
    OPBD = 'OPBD',
    DAY_ROLLING = 'DAY_ROLLING',
    PROFIT_ANALYSIS = 'PROFIT_ANALYSIS',
    PROXY_PURCHASE = 'PROXY_PURCHASE',
    INTEGRATION_TRADE = 'INTEGRATION_TRADE',
    NEW_ENERGY_TRADE = 'NEW_ENERGY_TRADE',
    IMQP_NEW_ENERGY_TRADE = 'IMQP_NEW_ENERGY_TRADE',
    OTHER = 'OTHER'
}

export interface UnitConfig {
    unitId?: string;
    unitName?: string;
    plantId?: string;
    grayTag?: string;
    dispatchId?: string;
    limitPower?: number;
    uName?: string;
    unitIdIlt?: string;
    unitNameIlt?: string;
    generatorSetId?: string;
}
