<template>
    <div class="index">
        <el-config-provider :empty-values="[undefined, null, '']" :experimental-features="{}" :locale="zhCn" :value-on-clear="() => undefined">
            <n-config-provider
                :date-locale="dateZhCN"
                :hljs="hljs"
                :locale="zhCN"
                :theme="isDark ? darkTheme : undefined"
                abstract
                inline-theme-disabled
            >
                <n-notification-provider>
                    <n-message-provider>
                        <router-view></router-view>
                    </n-message-provider>
                </n-notification-provider>
                <n-global-style />
            </n-config-provider>
        </el-config-provider>
    </div>
</template>
<script lang="ts" setup>
    import { darkTheme, dateZhCN, zhCN } from 'naive-ui';
    import zhCn from 'element-plus/es/locale/lang/zh-cn';
    import { useCommonStore } from '@/stores/common';
    import hljs from 'highlight.js';

    let commonStore = useCommonStore();

    const isDark = ref(false);
    onMounted(async () => {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', onColorSchemeChange);
        onColorSchemeChange();
        window.addEventListener('resize', onResize);
        onResize();
    });
    onUnmounted(() => {
        window.matchMedia('(prefers-color-scheme: dark)').removeEventListener('change', onColorSchemeChange);
        window.removeEventListener('resize', onResize);
    });

    function onColorSchemeChange(event?: MediaQueryListEvent) {
        if (event) {
            isDark.value = event.matches;
        } else {
            isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches;
        }
        console.log('主题设置更改', isDark.value ? 'dark' : 'light');
        commonStore.theme = isDark.value ? 'dark' : 'light';
    }

    const onResize = () => {
        commonStore.onResize();
    };
</script>
<style lang="scss" scoped>
    .index {
        height: 100vh;
        width: 100vw;
        overflow: hidden;
        overflow-x: auto;
    }
</style>
<style lang="scss">
    * {
        box-sizing: border-box;
    }
</style>
