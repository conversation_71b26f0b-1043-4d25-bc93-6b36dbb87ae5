<template>
    <n-config-provider
        :theme-overrides="{ DataTable: { tdPaddingSmall: '3px' }, Select: { peers: { InternalSelectMenu: { height: '100%' } } } }"
        abstract
    >
        <n-form ref="formRef">
            <n-data-table :columns="columns" :data="data" :loading="loading" :max-height="props.height - 80" class="table" size="small" />
        </n-form>
    </n-config-provider>
</template>
<script lang="tsx" setup>
    import { Ref, ref, watch } from 'vue';
    import { DataTableColumns, NFormItem, NInputNumber, NPopover, NSelect } from 'naive-ui';
    import { Query, RowData, timeArray } from '@/views/onProvinceBeforeDay/types';
    import { PlusCircle, TrashAltRegular } from '@vicons/fa';
    import { throttle } from 'lodash';
    import { useProvinceStore } from '@/stores/province';

    const provinceStore = useProvinceStore();

    const loading: Ref<boolean> = ref(false);
    const data: Ref<RowData[]> = ref([]);
    const props = defineProps<{ query: Query; height: number }>();
    let columns: DataTableColumns<RowData> = [
        {
            title: '申报时段',
            key: 'tradeTimeIndex',
            align: 'center',
            rowSpan: (row) => (row.rowSpan === 0 ? 1 : row.rowSpan),
            render: (row) => row.range
        },
        {
            title: '开始时间',
            key: 'startTime',
            align: 'center',
            render: (row) => (
                <NFormItem
                    path={row.range}
                    showLabel={false}
                    showFeedback={false}
                    rule={{
                        trigger: ['input', 'blur'],
                        validator(): Error | undefined {
                            let result = validDate(row);
                            row.popover.time = result?.message ?? null;
                            return result;
                        }
                    }}
                >
                    <NPopover trigger="hover" disabled={row.popover.time == null} keepAliveOnHover={false}>
                        {{
                            default: () => row.popover.time,
                            trigger: () => (
                                <NSelect
                                    value={row.startTime}
                                    options={timeArray
                                        .slice(row.tradeTimeIndex * 8, row.tradeTimeIndex * 8 + 8)
                                        .map(() => row.tradeTimeIndex * 8)
                                        .map((it, index) =>
                                            Object.assign({
                                                label: timeArray[it + index],
                                                value: it + index
                                            })
                                        )}
                                    onUpdate:value={(v: number) => {
                                        row.startTime = v;
                                    }}
                                />
                            )
                        }}
                    </NPopover>
                </NFormItem>
            )
        },
        {
            title: '结束时间',
            key: 'endTime',
            align: 'center',
            render: (row) => (
                <NFormItem
                    path={row.range}
                    showLabel={false}
                    showFeedback={false}
                    rule={{
                        trigger: ['input', 'blur'],
                        validator(): Error | undefined {
                            let result = validDate(row);
                            row.popover.time = result?.message ?? null;
                            return result;
                        }
                    }}
                >
                    <NPopover trigger="hover" disabled={row.popover.time == null} keepAliveOnHover={false}>
                        {{
                            default: () => row.popover.time,
                            trigger: () => (
                                <NSelect
                                    value={row.startTime}
                                    options={timeArray
                                        .slice(row.tradeTimeIndex * 8, row.tradeTimeIndex * 8 + 8)
                                        .map(() => row.tradeTimeIndex * 8)
                                        .map((it, index) =>
                                            Object.assign({
                                                label: timeArray[it + index],
                                                value: it + index
                                            })
                                        )}
                                    onUpdate:value={(v: number) => {
                                        row.startTime = v;
                                    }}
                                />
                            )
                        }}
                    </NPopover>
                </NFormItem>
            )
        },
        {
            title: provinceStore.queryLimit === 'true' ? '有电力限额' : '无电力限额',
            key: 'percent',
            align: 'center',
            render: (row, index) => (
                <NFormItem
                    path={row.range}
                    showLabel={false}
                    showFeedback={false}
                    rule={{
                        trigger: ['input', 'blur'],
                        validator(): Error | undefined {
                            if (row.percent === null) {
                                row.popover.percent = '请输入百分比';
                                return Error('请输入百分比');
                            }
                            row.popover.percent = null;
                        }
                    }}
                >
                    <NPopover trigger="hover" disabled={row.popover.percent == null} keepAliveOnHover={false}>
                        {{
                            default: () => row.popover.percent,
                            trigger: () => (
                                <NInputNumber
                                    value={row.percent}
                                    showButton={false}
                                    min={0}
                                    max={100}
                                    onUpdate:value={(v: number) => {
                                        for (let i = index; i < data.value.length; i++) {
                                            data.value[i].percent = v;
                                        }
                                    }}
                                >
                                    {{
                                        suffix: () => '%'
                                    }}
                                </NInputNumber>
                            )
                        }}
                    </NPopover>
                </NFormItem>
            )
        },
        {
            title: '电价',
            key: 'price',
            align: 'center',
            rowSpan: (row) => (props.query.type === 4 ? 1 : row.rowSpan === 0 ? 1 : row.rowSpan),
            render: (row, index) => (
                <NFormItem
                    path={row.range}
                    showLabel={false}
                    showFeedback={false}
                    rule={{
                        trigger: ['input', 'blur'],
                        validator(): Error | undefined {
                            if (row.price === null) {
                                row.popover.price = '请输入电价';
                                return Error('请输入电价');
                            }
                            row.popover.price = null;
                        }
                    }}
                >
                    <NPopover trigger="hover" disabled={row.popover.price == null} keepAliveOnHover={false}>
                        {{
                            default: () => row.popover.price,
                            trigger: () => (
                                <NInputNumber
                                    value={row.price}
                                    showButton={false}
                                    min={0}
                                    max={3000}
                                    precision={0}
                                    onUpdate:value={(v: number) => {
                                        for (let i = index; i < data.value.length; i++) {
                                            data.value[i].price = v;
                                        }
                                    }}
                                />
                            )
                        }}
                    </NPopover>
                </NFormItem>
            )
        },
        {
            title: '操作',
            key: 'opt',
            width: '70px',
            align: 'center',
            render: (row, index) => (
                <div class="opts">
                    <span onClick={() => addRow(row.tradeTimeIndex, index)}>
                        <PlusCircle />
                    </span>
                    <span onClick={() => removeRow(row.tradeTimeIndex, index)}>
                        <TrashAltRegular />
                    </span>
                </div>
            )
        }
    ];

    const rowsCount: Ref<number[]> = ref(Array(12).fill(0));

    function validDate(row: RowData): Error | undefined {
        /*if (row.startTime > row.endTime) {
return new Error('开始时间需小于结束时间');
} else if (
!data.value
    .filter((r) => row.tradeTimeIndex === r.tradeTimeIndex && row.key !== r.key)
    .every((r) => row.startTime > r.endTime || row.endTime < r.startTime)
) {
return new Error('时间段冲突');
}*/
        if (!data.value.filter((r) => row.tradeTimeIndex === r.tradeTimeIndex && row.key !== r.key).every((r) => row.startTime !== r.startTime)) {
            return new Error('时间段冲突');
        }
    }

    const formRef = ref();

    async function validate(): Promise<boolean | unknown> {
        try {
            await formRef.value.validate();
        } catch (e) {
            return e;
        }
        return true;
    }

    const rowKey = ref(0);

    function removeRow(tradeTimeIndex: number, index: number) {
        if (rowsCount.value[tradeTimeIndex] !== 1) {
            data.value.splice(index, 1);
            rowsCount.value[tradeTimeIndex]--;
            for (let i = 1; i <= rowsCount.value[tradeTimeIndex] + 1; i++) {
                if (data.value[index - i]?.tradeTimeIndex !== tradeTimeIndex) {
                    data.value[index - i + 1].rowSpan = rowsCount.value[tradeTimeIndex];
                    break;
                }
            }
        }
    }

    function addRow(tradeTimeIndex: number, index: number) {
        console.log(tradeTimeIndex, index);
        if (rowsCount.value[tradeTimeIndex] !== 8) {
            addRowData(index + 1, tradeTimeIndex);
            rowsCount.value[tradeTimeIndex]++;
            for (let i = 1; i <= rowsCount.value[tradeTimeIndex]; i++) {
                if (data.value[index + 1 - i]?.tradeTimeIndex !== tradeTimeIndex) {
                    data.value[index + 1 - i + 1].rowSpan = rowsCount.value[tradeTimeIndex];
                    break;
                }
            }
        }
    }

    const updateTable = throttle(async (queryData: any[]) => {
        loading.value = true;
        data.value.splice(0, data.value.length);
        for (let i = 0; i < queryData.length; i++) {
            rowsCount.value[queryData[i].timePart]++;
            addRowData(i, queryData[i].timePart);
            Object.assign(data.value[i], {
                startTime: timeArray.indexOf(queryData[i].startTime),
                endTime: timeArray.indexOf(queryData[i].startTime),
                percent: queryData[i].percent,
                price: queryData[i].price
            });
        }
        let rowSpan = 0;
        let l = data.value[data.value.length - 1].tradeTimeIndex;
        for (let i = data.value.length - 1; i >= -1; i--) {
            if (data.value[i]?.tradeTimeIndex !== l) {
                l = data.value[i]?.tradeTimeIndex;
                data.value[i + 1].rowSpan = rowSpan;
                rowsCount.value[data.value[i + 1].tradeTimeIndex] = rowSpan;
                rowSpan = 0;
            }
            rowSpan++;
        }
        loading.value = false;
    }, 500);

    defineExpose({ data, validate, updateTable });

    watch(props.query, async () => {
        const tradeTimeIndexSet = new Set<number>(props.query.tradeTime);
        data.value = data.value.filter((row) => {
            tradeTimeIndexSet.delete(row.tradeTimeIndex);
            return props.query.tradeTime.includes(row.tradeTimeIndex);
        });
        for (let tradeTimeIndex of tradeTimeIndexSet) {
            rowsCount.value[tradeTimeIndex] = 8;
            for (let i = 0; i < 8; i++) {
                addRowData(data.value.length, tradeTimeIndex, i);
            }
        }
        data.value.sort((a, b) => a.tradeTimeIndex - b.tradeTimeIndex);
    });

    function addRowData(pos: number, tradeTimeIndex: number, i?: number) {
        data.value.splice(pos, 0, {
            key: rowKey.value++,
            tradeTimeIndex: tradeTimeIndex,
            range: timeArray[tradeTimeIndex * 8] + '-' + timeArray[tradeTimeIndex * 8 + 7],
            rowSpan: i === 0 ? rowsCount.value[tradeTimeIndex] : 0,
            startTime: tradeTimeIndex * 8 + (i ?? 0),
            endTime: tradeTimeIndex * 8 + (i ?? 0),
            percent: null,
            price: null,
            popover: { time: null, percent: null, price: null }
        });
    }
</script>
<style scoped lang="scss">
    .table {
        :deep(.n-data-table-th__title) {
            white-space: nowrap;
            word-break: keep-all;
        }
        :deep(.opts) {
            display: flex;
            justify-content: space-between;
            user-select: none;
            margin: 0 15px 0 8px;
            span {
                height: 18px;
                svg {
                    height: 18px;
                    width: 18px;
                }
            }
        }
    }
</style>
