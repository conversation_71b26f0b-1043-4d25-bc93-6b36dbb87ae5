<template>
    <n-data-table
        ref="dataTableRef"
        remote
        :max-height="props.height - dataTableTheadHeight - 30"
        :columns="columns"
        :data="data"
        size="small"
        :loading="loading"
        :pagination="paginationReactive"
    />
</template>
<script setup lang="tsx">
    import { Ref, ref } from 'vue';
    import { DataTableColumns, NButton, NDataTable, NPopover, NTag } from 'naive-ui';
    import { throttle } from 'lodash';
    import { Query } from '@/views/onProvinceBeforeDay/types';
    import { queryRecordList, removeRecord } from '@/api/onProvinceBeforeDay';
    import { useCommonStore } from '@/stores/common';

    const data: Ref<RowData[]> = ref([]);
    const loading: Ref<boolean> = ref(false);
    const props = defineProps<{ query: Query; height: number }>();
    const emits = defineEmits(['delete']);

    const paginationReactive = reactive({
        page: 1,
        pageSize: 10,
        showSizePicker: true,
        pageCount: 1,
        itemCount: 0,
        pageSizes: [5, 10, 20, 50],
        pageSlot: 6,
        onChange: (page: number) => {
            paginationReactive.page = page;
            updateTable();
        },
        onUpdatePageSize: (pageSize: number) => {
            paginationReactive.pageSize = pageSize;
            paginationReactive.page = 1;
            updateTable();
        }
    });

    let columns: DataTableColumns<RowData> = [
        {
            title: '生效时间',
            key: 'strategyDate',
            align: 'center',
            render: (row: RowData) => row.strategyDate
        },
        {
            title: '制定时间',
            key: 'createTime',
            width: '170px',
            align: 'center',
            render: (row: RowData) => row.createTime
        },
        {
            title: '策略记录',
            key: 'detail',
            align: 'center',
            render: (row: RowData) => (
                <NPopover trigger="hover" placement="left-start">
                    {{
                        default: () => (
                            <NDataTable
                                flexHeight={true}
                                style={{ 'height': '60vh', 'width': '60vw', 'max-height': '60vh', 'max-width': '60vw' }}
                                columns={detailColumns}
                                data={row.details}
                            />
                        ),
                        trigger: () => (row.details ? checkStatus(row.details[0]) : '')
                    }}
                </NPopover>
            )
        },
        {
            title: '操作',
            key: 'opt',
            align: 'center',
            render: (row: RowData) => (
                <NButton size="small" type="error" disabled={row.disabled} onClick={() => deleteRow(row)}>
                    删除
                </NButton>
            )
        }
    ];
    let detailColumns: DataTableColumns<RowDetailData> = [
        {
            title: '起始时间',
            key: 'startTime',
            align: 'center',
            render: (row) => row.startTime
        },
        {
            title: '结束时间',
            key: 'endTime',
            align: 'center',
            render: (row) => row.endTime
        },
        {
            title: '电价',
            key: 'price',
            align: 'center',
            render: (row) => row.price
        },
        {
            title: '电力限额',
            key: 'powerLimit',
            align: 'center',
            render: (row) => row.powerLimit
        },
        {
            title: '申报比例',
            key: 'power',
            align: 'center',
            render: (row) => row.percent + '%'
        },
        {
            title: '申报电力值',
            key: 'reportPower',
            align: 'center',
            render: (row) => row.reportPower
        },
        {
            title: '状态',
            key: 'status',
            align: 'center',
            render: (row) => checkStatus(row)
        }
    ];

    const dataTableRef = ref();
    const dataTableTheadHeight = ref(30);

    useCommonStore().$subscribe(() => {
        onResize();
    });

    function onResize() {
        if (dataTableRef.value) {
            dataTableTheadHeight.value = dataTableRef.value.$el.querySelector('.n-data-table-thead').clientHeight;
        }
    }

    const updateTable = throttle(async () => {
        loading.value = true;
        let res = await queryRecordList<any>({
            provinceId: props.query.provinceId,
            unitId: props.query.unitId[0],
            page: paginationReactive
        });
        if (res.code === 1) {
            paginationReactive.pageCount = Math.ceil(res.data.total / paginationReactive.pageSize);
            paginationReactive.itemCount = res.data.total;
            data.value = res.data.data
                .sort((a: any, b: any) => (a.strategyDate > b.strategyDate ? -1 : 1))
                .map((it: any, index: number) => Object.assign(it, { key: index }));
        }
        loading.value = false;
    }, 300);

    function checkStatus(detail: RowDetailData) {
        if (detail.status === 999) {
            return <NTag>待执行</NTag>;
        }
        if ((detail.powerLimit && detail.powerLimit < 1) || detail.status === 1) {
            return <NTag type="success">成功</NTag>;
        }
        if (detail.powerLimit === undefined) {
            return <NTag type="error">失败</NTag>;
        }
        return <NTag>待执行</NTag>;
    }

    const deleteLoading = ref(false);
    const message = useMessage();

    async function deleteRow(row: RowData) {
        deleteLoading.value = true;
        try {
            const res = await removeRecord({ strategyDate: row.strategyDate, unitId: props.query.unitId[0] });
            if (res.code === 1) {
                message.success('删除完成');
            } else {
                message.error('删除失败');
            }
            emits('delete');
        } catch (e) {
            console.error(e);
            message.error('删除失败');
        } finally {
            deleteLoading.value = false;
        }
    }

    defineExpose({ updateTable });

    interface RowData {
        key: number;
        createTime: string;
        strategyDate: string;
        status: number;
        disabled: boolean;
        details: RowDetailData[];
    }

    interface RowDetailData {
        key: number;
        startTime: string;
        endTime: string;
        percent: number;
        reportPower: number;
        powerLimit: number | undefined;
        price: any;
        status: number;
    }
</script>
<style scoped lang="scss">
    :deep(.opts) {
        display: flex;
        justify-content: space-between;
        user-select: none;
        margin-right: 15px;
        span {
            height: 18px;
            svg {
                height: 18px;
                width: 18px;
            }
        }
    }
</style>
