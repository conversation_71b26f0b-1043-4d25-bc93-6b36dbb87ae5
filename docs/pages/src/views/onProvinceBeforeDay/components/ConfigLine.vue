<template>
    <n-config-provider
        :theme-overrides="{ DataTable: { tdPaddingSmall: '3px' }, Select: { peers: { InternalSelectMenu: { height: '100%' } } } }"
        abstract
    >
        <n-form ref="formRef">
            <n-data-table :columns="columns" :data="data" :loading="loading" :max-height="props.height - 80" class="table" size="small" />
        </n-form>
    </n-config-provider>
</template>
<script lang="tsx" setup>
    import { Ref, ref, watch } from 'vue';
    import { DataTableColumns, NFormItem, NInputNumber, NPopover } from 'naive-ui';
    import { Query, RowData, timeArray } from '../types';
    import { throttle } from 'lodash';
    import { useProvinceStore } from '@/stores/province';

    const provinceStore = useProvinceStore();

    const loading: Ref<boolean> = ref(false);
    const data: Ref<RowData[]> = ref([]);
    const props = defineProps<{ query: Query; height: number }>();
    let columns: DataTableColumns<RowData> = [
        {
            title: '开始时间',
            key: 'startTime',
            align: 'center',
            render: (row) => timeArray[row.startTime]
        },
        {
            title: '结束时间',
            key: 'endTime',
            align: 'center',
            render: (row) => timeArray[row.endTime]
        },
        {
            title: provinceStore.queryLimit === 'true' ? '有电力限额' : '无电力限额',
            key: 'percent',
            align: 'center',
            render: (row, index) => (
                <NFormItem
                    path={row.range}
                    showLabel={false}
                    showFeedback={false}
                    rule={{
                        trigger: ['input', 'blur'],
                        validator(): Error | undefined {
                            if (row.percent === null) {
                                row.popover.percent = '请输入百分比';
                                return Error('请输入百分比');
                            }
                            row.popover.percent = null;
                        }
                    }}
                >
                    <NPopover trigger="hover" disabled={row.popover.percent == null} keepAliveOnHover={false}>
                        {{
                            default: () => row.popover.percent,
                            trigger: () => (
                                <NInputNumber
                                    value={row.percent}
                                    showButton={false}
                                    min={0}
                                    max={100}
                                    onUpdate:value={(v: number) => {
                                        for (let i = index; i < data.value.length; i++) {
                                            data.value[i].percent = v;
                                        }
                                    }}
                                >
                                    {{
                                        suffix: () => '%'
                                    }}
                                </NInputNumber>
                            )
                        }}
                    </NPopover>
                </NFormItem>
            )
        },
        {
            title: '电价',
            key: 'price',
            align: 'center',
            render: (row, index) => (
                <NFormItem
                    path={row.range}
                    showLabel={false}
                    showFeedback={false}
                    rule={{
                        trigger: ['input', 'blur'],
                        validator(): Error | undefined {
                            if (row.price === null) {
                                row.popover.price = '请输入电价';
                                return Error('请输入电价');
                            }
                            row.popover.price = null;
                        }
                    }}
                >
                    <NPopover trigger="hover" disabled={row.popover.price == null} keepAliveOnHover={false}>
                        {{
                            default: () => row.popover.price,
                            trigger: () => (
                                <NInputNumber
                                    value={row.price}
                                    showButton={false}
                                    min={0}
                                    max={3000}
                                    precision={0}
                                    onUpdate:value={(v: number) => {
                                        for (let i = index; i < data.value.length; i++) {
                                            data.value[i].price = v;
                                        }
                                    }}
                                />
                            )
                        }}
                    </NPopover>
                </NFormItem>
            )
        }
    ];

    const formRef = ref();

    async function validate(): Promise<boolean | unknown> {
        try {
            await formRef.value.validate();
        } catch (e) {
            return e;
        }
        return true;
    }

    const updateTable = throttle(async (queryData: any[]) => {
        loading.value = true;
        Object.assign(data.value[0], {
            percent: queryData[0].percent,
            price: queryData[0].price
        });
        loading.value = false;
    }, 500);

    defineExpose({ data, validate, updateTable });

    watch(props.query, async () => {
        data.value = [
            {
                key: 0,
                tradeTimeIndex: 0,
                range: '00:15-24:00',
                rowSpan: 0,
                startTime: 0,
                endTime: 95,
                percent: null,
                price: null,
                popover: { time: null, percent: null, price: null }
            }
        ];
    });
</script>
<style scoped lang="scss">
    .table {
        :deep(.n-data-table-th__title) {
            white-space: nowrap;
            word-break: keep-all;
        }
    }
</style>
