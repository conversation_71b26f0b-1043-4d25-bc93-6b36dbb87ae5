<template>
    <div class="logs-page">
        <n-flex align="center" justify="right">
            <n-auto-complete
                v-model:value="filterText"
                :input-props="{ autocomplete: 'disabled' }"
                :options="options"
                clearable
                placeholder="过滤"
                size="small"
                style="width: 400px"
                @update-value="scrollToBottom"
            >
                <template #prefix>
                    <Filter />
                </template>
            </n-auto-complete>
            <n-switch v-model:value="showDebug">
                <template #checked>显示调试日志</template>
                <template #unchecked>不显示调试日志</template>
            </n-switch>
            <n-switch v-model:value="scroll" @on-update:value="scrollToBottom">
                <template #checked>自动滚动到底部</template>
                <template #unchecked>不自动滚动</template>
            </n-switch>
            <n-switch v-model:value="autoUpdate">
                <template #checked>实时刷新</template>
                <template #unchecked>不刷新</template>
            </n-switch>
        </n-flex>
        <div ref="logsWrapperRef" class="logs-wrapper">
            <n-config-provider :theme-overrides="{ DataTable: { tdPaddingSmall: '0', lineHeight: '20px' } }" abstract>
                <n-data-table
                    ref="dataTableRef"
                    :bordered="false"
                    :columns="columns"
                    :data="tableDataFiltered"
                    :max-height="logsWrapperHeight"
                    :row-class-name="(row: LogRow) => '' + row.level.toLowerCase()"
                    :row-key="(row: LogRow) => JSON.stringify(row)"
                    :scroll-x="3000"
                    :single-column="true"
                    size="small"
                ></n-data-table>
            </n-config-provider>
        </div>
    </div>
</template>
<script lang="tsx" setup>
    import { Ref } from 'vue';
    import dayjs from 'dayjs';
    import { Filter } from '@vicons/fa';
    import { SseClient } from '@/utils/SseClient';
    import { LogRow } from '@/views/logs/types';
    import { DataTableColumns } from 'naive-ui';
    import { useCommonStore } from '@/stores/common';

    const commonStore = useCommonStore();
    const tableData: Ref<LogRow[]> = ref([]);
    const columns: DataTableColumns<LogRow> = [
        {
            type: 'expand',
            width: 20,
            fixed: 'left',
            expandable: (rowData) => rowData.throwableStr !== '',
            renderExpand: (rowData) => <pre class={'error-log'}>{rowData.throwableStr}</pre>
        },
        { key: 'time', width: 170, align: 'center', fixed: 'left', ellipsis: true },
        { key: 'thread', width: 75, fixed: 'left', render: (row) => <span>{row.thread}</span> },
        { key: 'level', width: 45, fixed: 'left', render: (row) => <span>{row.level}</span> },
        { key: 'loggerName', width: 370, render: (row) => <span>{row.loggerName}</span> },
        { key: 'message', ellipsis: true }
    ];

    const filterText: Ref<string> = ref('');
    const dataTableRef = ref();
    const scroll = ref(true);
    const autoUpdate = ref(true);
    const showDebug = ref(false);

    const sseClient = new SseClient(import.meta.env.VITE_BASE_URL + 'api/v1/log/sse', (event): void => {
        const { data } = event;
        if (autoUpdate.value) {
            let row = JSON.parse(data);
            if (!showDebug.value && row.level === 'DEBUG') {
                return;
            }
            if (row.thread.length > 8) {
                row.thread = row.thread.substring(row.thread.length - 8, row.thread.length);
            }
            let logRow: LogRow = {
                time: dayjs(row.time).format('YYYY-MM-DD HH:mm:ss.SSS'),
                thread:
                    '[' +
                    (row.thread.length > 8 ? row.thread.substring(row.thread.length - 8, row.thread.length) : row.thread).padStart(8, '\u2002') +
                    ']',
                level: row.level.padStart(5, '\u2002'),
                loggerName: '[' + row.loggerName.padStart(50, '\u2002') + ']',
                message: row.message,
                throwableStr: row.throwableStr,
                ran: Math.random()
            };
            if (tableData.value.length >= 1200) {
                tableData.value.splice(0, 500);
            }
            tableData.value.push(logRow);
        }
    });

    let tableDataFiltered = computed(() => {
        return tableData.value.filter((row) => JSON.stringify(row).toLowerCase().indexOf(filterText.value.toLowerCase()) !== -1);
    });

    let options = computed(() => {
        return [
            ...new Set(
                tableDataFiltered.value.map((row) => JSON.stringify(row)).flatMap((line) => line.replace(/[\[\]]/g, ' ').split(/[ \u2002\u2003]/))
            )
        ]
            .filter((it) => it.toLowerCase().indexOf(filterText.value.toLowerCase()) !== -1)
            .map((it) => ({ label: it, value: it }));
    });

    function scrollToBottom() {
        if (scroll.value) {
            if (dataTableRef.value) {
                nextTick(() => dataTableRef.value.scrollTo({ left: 0, top: 1_000_000_000 }));
            }
        }
    }

    watch(tableData.value, () => {
        scrollToBottom();
    });

    const logsWrapperRef = ref();
    const logsWrapperHeight = ref();
    commonStore.$subscribe(() => {
        if (logsWrapperRef.value) {
            logsWrapperHeight.value = window.innerHeight - Math.ceil(logsWrapperRef.value.getBoundingClientRect().top) - 10;
        }
    });

    onUnmounted(() => {
        sseClient.close();
    });
</script>
<style lang="scss" scoped>
    .logs-page {
        flex: 1 0 100px;
        padding: 10px 0 10px 10px;
        display: flex;
        flex-direction: column;
        grid-gap: 10px;
        .n-flex {
            padding-right: 10px;
            flex: 0 1 30px;
        }
        .logs-wrapper {
            flex: 1;
        }
        :deep(.n-data-table-base-table-header) {
            height: 0;
        }
        :deep(.n-data-table-td) {
            &[data-col-key='time'] {
                color: #c18401;
            }
            &[data-col-key='thread'] {
                color: #0184bb;
            }
            &[data-col-key='level'] {
                color: #a626a4;
            }
            &[data-col-key='loggerName'] {
                color: #0184bb;
            }
        }
        :deep(.n-data-table-tr) {
            &.debug td span {
                opacity: 0.4;
            }
            &.warn td {
                background-color: #ffff0033;
                backdrop-filter: blur(50px);
            }
            &.error td {
                background-color: #ff000033;
                backdrop-filter: blur(50px);
            }
        }
        :deep(pre.error-log) {
            margin: 0 0 0 25px;
        }
    }
</style>
