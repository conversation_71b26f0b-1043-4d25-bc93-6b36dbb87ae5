export interface Query {
    provinceId: string;
    unitIdList: string[];
    strategyDate: string;
    declareDateList: string[];
}

export interface DayRollingConfig {
    unitId: string;
    strategyDate: string;
    declareDate: string;
    timeCode: number;
    tradeSeqId: string | null;
    tradeRole: string | null;
    power: number | null;
    price: number | null;
    reportPower: number | null;
    reportPrice: number | null;
    buyEnergyLimit: number | null;
    sellEnergyLimit: number | null;
    priceLowerLimit: number | null;
    priceUpperLimit: number | null;
    status: number;
    createTime: string;
    updateTime: string | null;
}

export const timeArray = [
    '00:00',
    '01:00',
    '02:00',
    '03:00',
    '04:00',
    '05:00',
    '06:00',
    '07:00',
    '08:00',
    '09:00',
    '10:00',
    '11:00',
    '12:00',
    '13:00',
    '14:00',
    '15:00',
    '16:00',
    '17:00',
    '18:00',
    '19:00',
    '20:00',
    '21:00',
    '22:00',
    '23:00',
    '24:00'
];
