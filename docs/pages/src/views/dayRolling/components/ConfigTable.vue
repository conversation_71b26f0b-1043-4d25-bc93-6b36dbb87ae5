<template>
    <n-config-provider :theme-overrides="{ DataTable: { tdPaddingSmall: '3px' } }" abstract>
        <div class="day-rolling-config-page">
            <n-data-table :columns="columns" :data="data" :loading="loading" :style="{ height: props.height - 35 + 'px' }" flex-height size="small" />
            <n-row justify-content="center">
                <n-button size="small" type="primary" @click="saveConfig">确认</n-button>
            </n-row>
        </div>
    </n-config-provider>
</template>
<script lang="tsx" setup>
    import { Ref } from 'vue';
    import { DataTableColumns, NInputNumber, NRadio, NRadioGroup } from 'naive-ui';
    import { DayRollingConfig, Query, timeArray } from '@/views/dayRolling/types';
    import { saveConfigList } from '@/api/dayRolling';
    import { useDayRollingStore } from '@/stores/dayRolling';

    const props = defineProps<{ query: Query; declareDate: string; height: number }>();
    const emit = defineEmits(['save']);

    const loading: Ref<boolean> = ref(false);
    const data: Ref<DayRollingConfig[]> = ref(
        Array(24)
            .fill(1)
            .map((_, index) => ({
                unitId: '',
                strategyDate: props.query.strategyDate,
                declareDate: props.declareDate,
                timeCode: index + 1,
                tradeSeqId: null,
                tradeRole: null,
                power: null,
                price: null,
                reportPower: null,
                reportPrice: null,
                buyEnergyLimit: null,
                sellEnergyLimit: null,
                priceLowerLimit: null,
                priceUpperLimit: null,
                status: 0,
                createTime: '',
                updateTime: null
            }))
    );
    const message = useMessage();

    const dayRollingStore = useDayRollingStore();
    watch(
        () => dayRollingStore.configs,
        () => updateData()
    );

    onMounted(() => {
        updateData();
    });

    function updateData() {
        if (dayRollingStore.configs[props.declareDate]) {
            Object.keys(dayRollingStore.configs[props.declareDate]).forEach((key) =>
                Object.assign(data.value[Number(key) - 1], dayRollingStore.configs[props.declareDate][key])
            );
        }
    }

    async function saveConfig() {
        console.log(data.value.filter((it) => it.tradeRole !== null && it.power !== null && it.price !== null));
        const resp = await saveConfigList({
            strategyDate: props.query.strategyDate,
            declareDate: props.declareDate,
            unitIdList: props.query.unitIdList,
            type: 1,
            configList: data.value.filter((it) => it.tradeRole !== null && it.power !== null && it.price !== null)
        });
        console.log();
        if (resp.code === 1) {
            message.success('保存成功');
            emit('save');
        }
    }

    let columns: DataTableColumns<DayRollingConfig> = [
        {
            title: '申报时段',
            key: 'timeCode',
            align: 'center',
            render: (row) => timeArray[row.timeCode - 1] + '-' + timeArray[row.timeCode]
        },
        {
            title: '申报方向',
            key: 'tradeRole',
            align: 'center',
            render: (row) => (
                <NRadioGroup v-model:value={row.tradeRole} name="tradeRole">
                    <NRadio value={'1'}>买入</NRadio>
                    <NRadio value={'2'}>卖出</NRadio>
                </NRadioGroup>
            )
        },
        {
            title: '申报电量',
            key: 'power',
            align: 'center',
            render: (row, index) => (
                <NInputNumber
                    value={row.power}
                    showButton={false}
                    min={0}
                    max={row.tradeRole === '1' ? row.buyEnergyLimit : row.tradeRole === '2' ? row.sellEnergyLimit : 0}
                    precision={3}
                    placeholder={
                        '' +
                        (row.buyEnergyLimit === null && row.sellEnergyLimit === null
                            ? '没有限额数据'
                            : row.tradeRole === '1'
                              ? row.buyEnergyLimit ?? '-'
                              : row.tradeRole === '2'
                                ? row.sellEnergyLimit ?? '-'
                                : (row.buyEnergyLimit ?? '-') + ' / ' + (row.sellEnergyLimit ?? '-'))
                    }
                    onUpdate:value={(v: number) => {
                        data.value[index].power = v;
                    }}
                ></NInputNumber>
            )
        },
        {
            title: '申报电价',
            key: 'price',
            align: 'center',
            render: (row, index) => (
                <NInputNumber
                    value={row.price}
                    showButton={false}
                    min={0}
                    max={3000}
                    precision={2}
                    placeholder={
                        '' +
                        (row.priceLowerLimit === null && row.priceUpperLimit === null
                            ? '没有限额数据'
                            : (row.priceLowerLimit ?? '-') + ' ~ ' + (row.priceUpperLimit ?? '-'))
                    }
                    onUpdate:value={(v: number) => {
                        data.value[index].price = v;
                    }}
                />
            )
        }
    ];
</script>
<style scoped lang="scss">
    .day-rolling-config-page {
        flex: 0 0 600px;
        display: flex;
        flex-direction: column;
        row-gap: 10px;
    }
</style>
