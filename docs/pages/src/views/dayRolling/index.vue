<template>
    <div class="day-rolling-page">
        <n-card class="header" size="small">
            <n-form ref="formRef" :model="query" :show-feedback="false" inline label-placement="left" size="small">
                <n-form-item label="省份" path="provinceId">
                    <n-select v-model:value="query.provinceId" :options="provinceList" style="width: 100px" />
                </n-form-item>
                <n-form-item label="交易单元" path="unitIdList">
                    <n-select v-model:value="query.unitIdList" :max-tag-count="1" :options="unitList" multiple style="width: 200px">
                        <template #action>
                            <n-button size="tiny" @click="query.unitIdList = unitList.map((it) => it.value)">全选 </n-button>
                            <n-button size="tiny" @click="query.unitIdList = []">清空</n-button>
                        </template>
                    </n-select>
                </n-form-item>
                <n-form-item label="策略时间" path="date">
                    <n-date-picker
                        v-model:formatted-value="query.strategyDate"
                        :close-on-select="true"
                        style="width: 140px"
                        value-format="yyyy-MM-dd"
                    />
                </n-form-item>
                <n-form-item>
                    <n-flex>
                        <n-button size="small" title="只会查询第一个场站的数据" type="primary" @click="updateData"> 查询 </n-button>
                        <n-button size="small" type="primary" @click="exportRecord(false)">导出</n-button>
                        <n-button size="small" type="primary" @click="exportRecord(true)">一键导出</n-button>
                    </n-flex>
                </n-form-item>
                <n-form-item>
                    <n-button :loading="callDetectJobStatus" size="small" title="只会更新第一个场站的数据" type="warning" @click="callDetectJob"
                        >更新
                    </n-button>
                </n-form-item>
            </n-form>
        </n-card>
        <n-card
            :content-style="{ 'height': '100%', 'display': 'flex', 'justify-content': 'space-between', 'column-gap': '10px' }"
            class="article"
            size="small"
        >
            <div ref="configRef">
                <n-tabs
                    v-show="query.declareDateList.length > 0"
                    :on-update:value="(value: string) => (declareDateTab = value)"
                    :value="declareDateTab"
                    animated
                    pane-wrapper-class="config-wrapper"
                    type="line"
                >
                    <n-tab-pane v-for="declareDate in query.declareDateList" :key="declareDate" :name="declareDate" :tab="declareDate" class="config">
                        <config-table :declare-date="declareDate" :height="configHeight" :query="query" @save="updateData" />
                    </n-tab-pane>
                </n-tabs>
            </div>
            <div v-show="query.declareDateList.length === 0">没有数据</div>
            <div class="record-table">
                <record-table ref="recordTableRef" :query="query" />
            </div>
        </n-card>
    </div>
</template>
<script lang="ts" setup>
    import { Ref } from 'vue';
    import dayjs from 'dayjs';
    import { queryUnitList } from '@/api/unit';
    import { callDetectJobByUnitId, exportAllRecordList, exportRecordList, queryConfigList } from '@/api/dayRolling';
    import { RoleType, UnitConfig } from '@/types';
    import { DayRollingConfig, Query } from '@/views/dayRolling/types';
    import ConfigTable from '@/views/dayRolling/components/ConfigTable.vue';
    import RecordTable from '@/views/dayRolling/components/RecordTable.vue';
    import { useCommonStore } from '@/stores/common';
    import { useProvinceStore } from '@/stores/province';
    import { useDayRollingStore } from '@/stores/dayRolling';
    import { utils, writeFile } from 'xlsx';

    const dateFormat = 'YYYY-MM-DD';

    const commonStore = useCommonStore();
    const provinceStore = useProvinceStore();
    const provinceList = ref(provinceStore.getProvinceList());

    const unitList: Ref<any[]> = ref([]);
    const declareDateTab: Ref<string> = ref('');
    const query: Ref<Query> = ref({
        provinceId: provinceList.value[0].value,
        unitIdList: [],
        strategyDate: dayjs().format(dateFormat),
        declareDateList: []
    });

    const message = useMessage();
    const dayRollingStore = useDayRollingStore();

    async function exportRecord(exportAll: boolean) {
        const res = exportAll
            ? await exportAllRecordList<{ [key: string]: any[] }>({ type: 1 })
            : await exportRecordList<{ [key: string]: any[] }>({
                  unitIdList: query.value.unitIdList,
                  strategyDate: query.value.strategyDate,
                  type: 1
              });
        const workbook = utils.book_new();
        Object.keys(res.data).forEach((sheetName) => {
            const worksheet = utils.json_to_sheet(res.data[sheetName], {
                header: ['timeCode', 'tradeRole', 'power', 'price', 'reportPower', 'reportPrice', 'status']
            });
            worksheet['!cols'] = Array(7).fill({ wch: 15 });
            utils.sheet_add_aoa(worksheet, [['申报时段', '申报方向', '电量', '电价', '申报电量', '申报电价', '当前状态']], { origin: 'A1' });
            utils.book_append_sheet(workbook, worksheet, sheetName);
        });
        writeFile(workbook, `日滚动抢报交易记录${exportAll ? '' : ' ' + query.value.strategyDate}.xlsx`, { compression: true });
    }

    const recordTableRef = ref();

    async function updateData() {
        let res = await queryConfigList<DayRollingConfig[]>({
            unitId: query.value.unitIdList[0],
            strategyDate: query.value.strategyDate,
            type: 1
        });
        if (res.code === 1) {
            let configs = res.data.reduce((prev, curr) => {
                if (!prev[curr.declareDate]) {
                    prev[curr.declareDate] = {};
                }
                prev[curr.declareDate][curr.timeCode] = curr;
                return prev;
            }, {} as any);
            query.value.declareDateList = Object.keys(configs);
            declareDateTab.value = query.value.declareDateList[0];
            nextTick(() => {
                dayRollingStore.setConfigs(configs);
            }).then(() => {});
        } else {
        }
        recordTableRef.value.updateTable();
    }

    const configRef = ref();
    const configHeight = ref(300);
    commonStore.$subscribe(() => {
        if (configRef.value) {
            configHeight.value = window.innerHeight - Math.ceil(configRef.value.getBoundingClientRect().top) - 80;
            console.log(configHeight.value);
        }
    });

    onMounted(async () => {
        let res = await queryUnitList<UnitConfig[]>(RoleType.DAY_ROLLING);
        if (res.code !== 1) {
            message.error(res.msg);
        }
        if (res.code === 1 && res.data.length > 0) {
            unitList.value = res.data.map((it: any) => Object.assign({}, { label: it.uName, value: it.unitId }));
            query.value.unitIdList = [unitList.value[0].value];
            await updateData();
        }
    });

    const callDetectJobStatus = ref(false);

    async function callDetectJob() {
        callDetectJobStatus.value = true;
        try {
            const res = await callDetectJobByUnitId<null>({
                unitId: query.value.unitIdList[0],
                date: query.value.strategyDate
            });
            if (res.code === 1) {
                message.success('刷新完成');
                await updateData();
            } else {
                message.error('任务出错: ' + res.msg);
            }
        } catch (e) {
            message.error('任务出错: ' + (e as Error)?.message);
        } finally {
            callDetectJobStatus.value = false;
        }
    }
</script>
<style lang="scss" scoped>
    .day-rolling-page {
        display: flex;
        height: 100%;
        flex-direction: column;
        padding: 10px;
        row-gap: 10px;
        .header {
            flex: 0 0 20px;
            .n-form.n-form--inline {
                flex-wrap: wrap;
                gap: 10px 0;
            }
        }
        .tabs-wrapper {
            height: 40px;
            display: flex;
            justify-content: flex-start;
            column-gap: 100px;
            align-items: center;
            .upload {
                .n-upload {
                    display: flex;
                    :deep(.n-upload-file-list) {
                        margin-top: 0;
                        .n-upload-file-info {
                            padding: 0;
                        }
                    }
                }
            }
        }
        .article {
            flex: 1;
            overflow: auto;
            :deep(.config-wrapper) {
                flex: 1;
                .config {
                    height: 100%;
                    display: flex;
                    column-gap: 10px;
                }
            }
            .record-table {
                flex: 0 0 400px;
            }
        }
    }
</style>
