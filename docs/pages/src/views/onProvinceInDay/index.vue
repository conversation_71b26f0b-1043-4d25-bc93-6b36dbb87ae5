<template>
    <div class="strategy-wrapper">
        <n-card class="header" size="small">
            <n-form ref="formRef" :model="query" :show-feedback="false" inline label-placement="left" size="small">
                <n-form-item label="省份" path="provinceId">
                    <n-select v-model:value="query.provinceId" :options="provinceList" style="width: 100px" />
                </n-form-item>
                <n-form-item label="交易单元" path="unitId">
                    <n-select v-model:value="query.unitId" :max-tag-count="1" :options="unitList" multiple style="width: 200px">
                        <template #action>
                            <n-button size="tiny" @click="query.unitId = unitList.map((it) => it.value)">全选</n-button>
                            <n-button size="tiny" @click="query.unitId = []">清空</n-button>
                        </template>
                    </n-select>
                </n-form-item>
                <n-form-item label="策略时间" path="dateRange">
                    <n-date-picker
                        v-model:formatted-value="query.dateRange"
                        :close-on-select="true"
                        style="width: 260px"
                        type="daterange"
                        value-format="yyyy-MM-dd"
                    />
                </n-form-item>
                <n-form-item label="申报交易时段" path="tradeTime">
                    <n-config-provider :theme-overrides="{ Select: { peers: { InternalSelectMenu: { height: '100%' } } } }" abstract>
                        <n-select v-model:value="query.tradeTime" :max-tag-count="1" :options="timeList" multiple style="width: 200px">
                            <template #action>
                                <n-button size="tiny" @click="query.tradeTime = timeList.map((it) => it.value)">全选 </n-button>
                                <n-button size="tiny" @click="query.tradeTime = []">清空</n-button>
                            </template>
                        </n-select>
                    </n-config-provider>
                </n-form-item>
                <n-form-item>
                    <n-flex>
                        <n-button size="small" title="只会查询第一个场站和第一天的数据" type="primary" @click="updateData"> 查询 </n-button>
                        <n-button size="small" type="primary" @click="exportRecord">导出</n-button>
                    </n-flex>
                </n-form-item>
                <n-form-item v-if="provinceStore.provinceCode === 'hlj'">
                    <!-- 仅限黑龙江， 每天的这个时间申报 D+1 全部时段 -->
                    定时申报：
                    <n-tooltip placement="bottom" trigger="hover">
                        <template #trigger>
                            <n-time-picker
                                ref="timeConfigRef"
                                v-model:formatted-value="timeConfig"
                                clearable
                                size="small"
                                style="width: 120px"
                                @blur="onTimeConfigChange"
                                @confirm="onTimeConfigChange"
                            ></n-time-picker>
                        </template>
                        每天的这个时间申报 D+1 全部时段，修改后次日生效
                    </n-tooltip>
                </n-form-item>
                <n-form-item v-if="provinceStore.provinceCode === 'hlj'">
                    <n-tooltip placement="bottom" trigger="hover">
                        <template #trigger>
                            <n-button size="small" title="" type="primary" :loading="spotLoading" @click="spotNow">立即申报</n-button>
                        </template>
                        立即申报D+1全部时段
                    </n-tooltip>
                </n-form-item>
            </n-form>
        </n-card>
        <div class="tabs-wrapper">
            <n-radio-group v-model:value="query.reportType">
                <n-radio :value="1">手动策略</n-radio>
                <!-- 仅山西 -->
                <n-radio v-if="provinceStore.provinceCode === 'sx'" :value="2">自动策略</n-radio>
            </n-radio-group>
            <n-flex v-if="query.reportType === 1" :wrap="false" class="upload">
                <n-button size="small" type="info" @click="exportTemplate">模板</n-button>
                <n-upload
                    ref="uploadRef"
                    v-model:file-list="importFileList"
                    :custom-request="importConfig"
                    :default-upload="false"
                    :max="1"
                    accept=".xlsx"
                    @before-upload="beforeUpload"
                >
                    <n-button size="small" type="primary">导入</n-button>
                </n-upload>
                <n-button v-show="importFileList.length === 1" size="small" type="primary" @click="uploadRef?.submit()"> 上传 </n-button>
            </n-flex>
        </div>
        <n-tabs
            :default-value="4"
            :value="query.strategyType"
            animated
            size="small"
            type="line"
            @update:value="(value: number) => (query.strategyType = value)"
        >
            <n-tab :name="4" tab="按时段申报电力电价" />
        </n-tabs>
        <n-card
            :content-style="{ 'height': '100%', 'display': 'flex', 'justify-content': 'space-between', 'column-gap': '10px' }"
            class="article"
            size="small"
        >
            <div class="config">
                <div ref="dataTableWrapperRef" class="data-table-wrapper">
                    <config-table ref="configTableRef" :height="dataTableMaxHeight" :query="query" />
                    <div class="submit-wrapper">
                        <n-button size="small" type="primary" @click="saveData">确认</n-button>
                    </div>
                </div>
            </div>
            <div class="record-table">
                <record-table ref="recordTableRef" :height="dataTableMaxHeight" :query="query" @delete="updateData" />
            </div>
        </n-card>
    </div>
</template>
<script lang="ts" setup>
    import { Ref } from 'vue';
    import dayjs from 'dayjs';
    import { UploadCustomRequestOptions, UploadFileInfo, UploadInst } from 'naive-ui';
    import { queryUnitList } from '@/api/unit';
    import {
        exportConfigTemplate,
        exportRecordList,
        importConfigList,
        queryConfigList,
        queryTimeConfig,
        saveConfigList,
        saveTimeConfig,
        triggerSpotNow
    } from '@/api/onProvinceInDay';
    import { RoleType, UnitConfig } from '@/types';
    import { Query, RowData, timeArray } from '@/views/onProvinceInDay/types';
    import ConfigTable from '@/views/onProvinceInDay/components/ConfigTable.vue';
    import RecordTable from '@/views/onProvinceInDay/components/RecordTable.vue';
    import { useCommonStore } from '@/stores/common';
    import { useProvinceStore } from '@/stores/province';
    import { read, utils, writeFile } from 'xlsx';
    import { debounce } from 'lodash';

    const commonStore = useCommonStore();
    const provinceStore = useProvinceStore();

    const dateFormat = 'YYYY-MM-DD';

    const uploadRef = ref<UploadInst | null>(null);

    const provinceList = ref(provinceStore.getProvinceList());
    const unitList: Ref<any[]> = ref([]);
    const timeList = ref(
        Array(12)
            .fill(0)
            .map((_, index) =>
                Object.assign({
                    label: timeArray[index * 8] + '-' + timeArray[(index + 1) * 8 - 1],
                    value: index
                })
            )
    );
    const query: Ref<Query> = ref({
        provinceId: provinceList.value[0].value,
        unitId: [],
        dateRange: [dayjs().format(dateFormat), dayjs().format(dateFormat)],
        tradeTime: [],
        strategyType: 4,
        reportType: 1
    });

    const notification = useNotification();
    const message = useMessage();
    const configTableRef = ref();
    const recordTableRef = ref();

    async function saveData() {
        let data = configTableRef.value.data;
        if (data.length === 0) {
            message.warning('没有数据');
            return;
        }
        let validResult = await configTableRef.value.validate();
        if (validResult === true) {
            if (query.value.strategyType === 3) {
                let lastPrice = data[0].price;
                for (let i = 0; i < data.length; i++) {
                    console.log(data[i].rowSpan, data[i].price);
                    if (data[i].rowSpan === 0) {
                        data[i].price = lastPrice;
                    } else {
                        lastPrice = data[i].price;
                    }
                }
            }
            await saveConfigList({
                provinceId: query.value.provinceId,
                unitIdList: query.value.unitId,
                strategyType: query.value.strategyType,
                reportType: query.value.reportType,
                startDate: query.value.dateRange[0],
                endDate: query.value.dateRange[1],
                timeList: query.value.tradeTime,
                configList: data.map((it: RowData) =>
                    Object.assign(Object.assign({}, it), {
                        timePart: it.tradeTimeIndex,
                        startTime: timeArray[it.startTime],
                        endTime: timeArray[it.endTime]
                    })
                )
            }).then((res) => {
                if (res.code === 1) {
                    if (res.data === true) {
                        message.success('保存成功，已忽略超出申报时间的数据');
                    } else {
                        message.success('保存成功');
                    }
                    updateData();
                }
            });
        } else {
            console.log('err', validResult);
            notification.error({
                content: '校验失败',
                meta: '时段' + validResult[0][0].field + ': ' + validResult[0][0].message,
                duration: 2500,
                keepAliveOnHover: true
            });
        }
    }

    async function beforeUpload(data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) {
        if (
            data.file.file?.type !== 'application/vnd.ms-excel' &&
            data.file.file?.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ) {
            message.error('只能上传Excel文件，请重新选择');
            return false;
        }
        return true;
    }

    const importFileList = ref([]);

    async function importConfig(options: UploadCustomRequestOptions) {
        let arrayBuffer = await (options.file.file as File).arrayBuffer();
        let workbook = read(arrayBuffer);
        let sheetJson = utils.sheet_to_json(workbook.Sheets[workbook.SheetNames[0]], { header: 1 });
        sheetJson.shift();
        sheetJson = (sheetJson as any[][])
            .filter((row) => row.some((it) => it !== null))
            .map((row) => ({
                strategyDate: typeof row[0] === 'number' ? dayjs(new Date((row[0] - 25569) * 86400 * 1000)).format('YYYY-MM-DD') : row[0],
                startTime: row[1],
                percent: row[2],
                price: row[3]
            }))
            .sort((a, b) => (a.strategyDate + '_' + a.startTime > b.strategyDate + '_' + b.startTime ? 1 : -1));
        if (query.value.strategyType === 3) {
            let priceMap = new Map();
            for (let i = 0; i < sheetJson.length; i++) {
                let row = sheetJson[i] as any;
                let index = Math.ceil((timeArray.indexOf('' + row.startTime) + 1) / 8);
                let key = row.strategyDate + '_' + index;
                if (priceMap.get(key) === undefined) {
                    priceMap.set(key, row.price);
                } else {
                    row.price = index;
                }
            }
        }
        let res = await importConfigList({
            provinceId: query.value.provinceId,
            unitIdList: query.value.unitId,
            strategyType: query.value.strategyType,
            reportType: query.value.reportType,
            startDate: query.value.dateRange[0],
            endDate: query.value.dateRange[1],
            sheetJson: sheetJson
        });
        if (res.code === 1) {
            message.success('导入成功');
            importFileList.value = [];
            await updateData();
        }
    }

    async function exportRecord() {
        const res = await exportRecordList<{ [key: string]: any[] }>({
            provinceId: query.value.provinceId,
            unitIdList: query.value.unitId,
            strategyType: query.value.strategyType,
            reportType: query.value.reportType,
            startDate: query.value.dateRange[0],
            endDate: query.value.dateRange[1]
        });
        const workbook = utils.book_new();
        Object.keys(res.data).forEach((sheetName) => {
            const worksheet = utils.json_to_sheet(res.data[sheetName], {
                header: ['strategyDate', 'startTime', 'powerLimit', 'percent', 'reportPower', 'reportPrice']
            });
            worksheet['!cols'] = Array(6).fill({ wch: 15 });
            let percentCol = /^D\d+$/;
            Object.keys(worksheet)
                .filter((key) => percentCol.test(key))
                .forEach((key) => {
                    const cell = worksheet[key];
                    cell.z = '0.00%';
                    cell.t = 'n';
                });
            utils.sheet_add_aoa(worksheet, [['日期', '时点', '电力限额', '申报比例', '申报电力值', '申报电价']], { origin: 'A1' });
            utils.book_append_sheet(workbook, worksheet, sheetName);
        });
        writeFile(workbook, `${query.value.dateRange[0]} ~ ${query.value.dateRange[1]}省间日内现货申报记录.xlsx`, { compression: true });
    }

    async function exportTemplate() {
        const res = await exportConfigTemplate<any[]>({
            provinceId: query.value.provinceId,
            unitIdList: query.value.unitId,
            strategyType: query.value.strategyType,
            reportType: query.value.reportType,
            startDate: query.value.dateRange[0],
            endDate: query.value.dateRange[1]
        });
        const worksheet = utils.json_to_sheet(res.data, {
            header: ['strategyDate', 'startTime', 'percent', 'price']
        });
        worksheet['!cols'] = Array(4).fill({ wch: 15 });
        let percentCol = /^C\d+$/;
        Object.keys(worksheet)
            .filter((key) => percentCol.test(key))
            .forEach((key) => {
                const cell = worksheet[key];
                cell.z = '0.00%';
                cell.t = 'n';
            });
        utils.sheet_add_aoa(worksheet, [['日期', '时点', '申报比例', '申报电价']], { origin: 'A1' });
        const workbook = utils.book_new();
        utils.book_append_sheet(workbook, worksheet, '');
        writeFile(workbook, `${query.value.dateRange[0]} ~ ${query.value.dateRange[1]}省间日内现货申报导入模板.xlsx`, { compression: true });
    }

    commonStore.$subscribe(() => {
        onResize();
    });

    function onResize() {
        if (dataTableWrapperRef.value) {
            dataTableMaxHeight.value = dataTableWrapperRef.value.clientHeight - 1;
        }
    }

    const dataTableWrapperRef = ref();
    const dataTableMaxHeight = ref(300);
    onMounted(async () => {
        updateTimeConfig();
        let res = await queryUnitList<UnitConfig[]>(RoleType.OPID);
        if (res.code !== 1) {
            message.error(res.msg);
        }
        if (res.code === 1 && res.data.length > 0) {
            unitList.value = res.data.map((it: any) => Object.assign({}, { label: it.uName, value: it.unitId }));
            query.value.unitId = [unitList.value[0].value];
            await updateData();
        }
    });

    async function updateData() {
        recordTableRef.value.updateTable();
        let res = await queryConfigList<any[]>({
            provinceId: query.value.provinceId,
            unitId: query.value.unitId[0],
            startDate: query.value.dateRange[0]
        });
        if (res.code === 1 && res.data.length > 0) {
            query.value.tradeTime = [...res.data.reduce((res: Set<number>, it: any) => res.add(it.timePart), new Set<number>())];
            query.value.reportType = res.data[0].reportType;
            await nextTick(async () => {
                await configTableRef.value.updateTable(res.data);
            });
        } else {
            query.value.tradeTime = [];
            await nextTick(async () => {
                query.value.tradeTime = [0];
            });
        }
    }

    const timeConfigRef = ref();
    const timeConfig: Ref<any> = ref(null);

    async function updateTimeConfig() {
        if (provinceStore.provinceCode !== 'hlj') {
            return;
        }
        try {
            const { data } = await queryTimeConfig<{ timeConfig: string }>();
            timeConfig.value = data.timeConfig;
        } catch (e) {
            console.warn(e);
            message.error('获取申报时间失败');
        }
    }
    const saveTimeConfigFunc = debounce(async () => {
        try {
            await saveTimeConfig({ value: timeConfig.value });
            message.success('保存申报时间成功');
            updateTimeConfig();
        } catch (e) {
            console.warn(e);
            message.error('保存申报时间失败');
        }
    }, 200);
    function onTimeConfigChange() {
        nextTick(() => {
            timeConfigRef.value?.blur();
        });
        saveTimeConfigFunc();
    }

    const spotLoading = ref(false);
    async function spotNow() {
        spotLoading.value = true;
        try {
            const res = await triggerSpotNow({});
            if (res.code === 1) {
                message.success('申报完成');
            } else {
                message.error('申报失败');
            }
        } catch (e) {
            message.error('申报失败');
        } finally {
            spotLoading.value = false;
        }
    }
</script>
<style lang="scss" scoped>
    .strategy-wrapper {
        display: flex;
        height: 100%;
        flex-direction: column;
        padding: 10px;
        row-gap: 10px;
        overflow: auto;
        .header {
            flex: 0 0 20px;
            .n-form.n-form--inline {
                flex-wrap: wrap;
                gap: 10px 0;
            }
        }
        .tabs-wrapper {
            height: 40px;
            display: flex;
            justify-content: flex-start;
            column-gap: 100px;
            align-items: center;
            .upload {
                .n-upload {
                    display: flex;
                    :deep(.n-upload-file-list) {
                        margin-top: 0;
                        .n-upload-file-info {
                            padding: 0;
                        }
                    }
                }
            }
        }
        .article {
            flex: 1;
            overflow: auto;
            .config {
                flex: 0 1 800px;
                display: flex;
                flex-direction: column;
                .data-table-wrapper {
                    flex: 1 0 1%;
                    overflow: auto;
                }
                .submit-wrapper {
                    padding-top: 10px;
                    display: flex;
                    justify-content: center;
                }
            }
            .record-table {
                flex: 0 0 500px;
            }
        }
    }
</style>
