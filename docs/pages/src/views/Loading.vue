<template>
    <div class="loading-page">
        <n-spin :size="150" />
        <h1>Loading</h1>
    </div>
</template>
<script lang="ts" setup>
    import router, { showRoutes, systemRoutes } from '@/router';
    import { RouteRecordRaw } from 'vue-router';
    import { MenuMeta, MenuType, Result, RoleType } from '@/types';
    import { getReq } from '@/api/common';
    import _ from 'lodash';
    import { useRouteStore } from '@/stores/route';
    import { useCommonStore } from '@/stores/common';
    import { useProvinceStore } from '@/stores/province';

    const message = useMessage();
    window.$message = message;

    let vueRouter = useRouter();
    let loadTime = 1000;
    const routerStore = useRouteStore();
    const commonStore = useCommonStore();
    const provinceStore = useProvinceStore();

    type Menu = {
        [key in RoleType]: number;
    };

    let filterRule: Menu = {
        [RoleType.AUTO_LOGIN]: 0,
        [RoleType.OPID]: 0,
        [RoleType.OPBD]: 0,
        [RoleType.DAY_ROLLING]: 0,
        [RoleType.PROFIT_ANALYSIS]: 0,
        [RoleType.PROXY_PURCHASE]: 0,
        [RoleType.INTEGRATION_TRADE]: 0,
        [RoleType.NEW_ENERGY_TRADE]: 0,
        [RoleType.IMQP_NEW_ENERGY_TRADE]: 0,
        [RoleType.LOGS]: 1,
        [RoleType.OTHER]: 0
    };
    onMounted(async () => {
        console.log('loading');
        if (router.currentRoute.value.redirectedFrom?.path) {
            console.log('redirectedFrom:', router.currentRoute.value.redirectedFrom?.path);
            routerStore.lastVisitRoute = router.currentRoute.value.redirectedFrom?.path;
        }
        let startTime = new Date().getTime();
        let menus = await getMenus();
        let provinceCodeRes = await getReq<{
            provinceCode: string;
            provinceList: { value: string; label: string }[];
            queryLimit: string;
        }>('/system/configs');
        if (menus.code === 1 && menus.data) {
            filterRule = menus.data as Menu;
        }
        if (provinceCodeRes.code === 1 && provinceCodeRes.data) {
            provinceStore.setConfigs(provinceCodeRes.data);
        }
        (await filterRoute(showRoutes))?.forEach((route) => router.addRoute(route));
        (await filterRoute(systemRoutes))?.forEach((route) => router.addRoute(route));
        router.beforeEach((to, from) => {
            let redirectedFrom = from?.redirectedFrom;
            console.log('beforeEach', to.fullPath, redirectedFrom?.fullPath);
            let meta = to.meta as MenuMeta;
            if (meta.type === MenuType.INDEX) {
                return false;
            } else if (meta.type === MenuType.MENU && meta.hasPermission) {
                routerStore.lastVisitRoute = to.path;
                return true;
            }
            return { path: '/nop', replace: true };
        });
        router.afterEach(() => {
            nextTick(() => {
                // 每个页面 mounted 后
                commonStore.onResize();
            });
        });
        let allRoute = _.orderBy(
            router.getRoutes().filter((route) => (route.meta as MenuMeta).type === MenuType.MENU && route.meta.order !== Number.MAX_VALUE),
            ['meta.hasPermission', 'meta.order'],
            ['desc', 'asc']
        );
        if (allRoute.length > 0) {
            let firstRoute = allRoute[0];
            if (routerStore.lastVisitRoute) {
                let last = allRoute.filter((route) => route.path === routerStore.lastVisitRoute);
                if (last.length > 0) {
                    firstRoute = last[0];
                    console.log('导航到之前的路由:', firstRoute.path);
                }
            }
            if (firstRoute) {
                console.log('firstRoute', firstRoute);
                let now = new Date().getTime();
                setTimeout(async () => await vueRouter.push(firstRoute), now > startTime + loadTime ? 0 : loadTime - (now - startTime));
            } else {
                message.error('no route');
            }
        } else {
            message.error('no route');
        }
    });

    async function filterRoute(routes: RouteRecordRaw[] | undefined): Promise<RouteRecordRaw[] | undefined> {
        if (routes && routes.length > 0) {
            let arr = [];
            for (let route of routes) {
                let meta = route.meta as MenuMeta;
                if (meta.roleType && filterRule[meta.roleType] > 0) {
                    meta.hasPermission = true;
                }
                route.children = await filterRoute(route.children);
                arr.push(route);
            }
            return arr;
        }
        return undefined;
    }

    function getMenus(): Promise<Result<Menu>> {
        return new Promise(async (resolve) => {
            try {
                resolve(await getReq<Menu>('/system/menus'));
                return;
            } catch (e) {}
            let task = setInterval(async () => {
                try {
                    resolve(await getReq<Menu>('/system/menus'));
                    clearInterval(task);
                } catch (e) {}
            }, 3000);
        });
    }
</script>
<style lang="scss" scoped>
    .loading-page {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        code {
            font-size: 30px;
        }
        background: linear-gradient(-45deg, #ee775222, #e73c7e22, #23a6d522, #23d5ab22);
        background-size: 400% 400%;
        animation: gradient 15s ease infinite;
        @keyframes gradient {
            0% {
                background-position: 0 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0 50%;
            }
        }
    }
</style>
