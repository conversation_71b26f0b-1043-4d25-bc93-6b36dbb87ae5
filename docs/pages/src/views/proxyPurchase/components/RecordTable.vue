<template>
    <n-data-table
        :columns="columns1"
        :data="data"
        :loading="loading"
        :max-height="props.height"
        :pagination="paginationReactive"
        remote
        size="small"
    />
</template>
<script lang="tsx" setup>
    import { Ref } from 'vue';
    import { DataTableColumns, NDataTable, NPopover, NTag } from 'naive-ui';
    import { throttle } from 'lodash';
    import { ProxyPurchaseConfig, Query, RowData } from '../types';
    import { queryRecordListReq } from '@/api/proxyPurchase';

    const data: Ref<RowData[]> = ref([]);
    const loading: Ref<boolean> = ref(false);
    const props = defineProps<{ query: Query; height: number }>();

    const paginationReactive = reactive({
        page: 1,
        pageSize: 10,
        showSizePicker: true,
        pageCount: 1,
        itemCount: 0,
        pageSizes: [5, 10, 20, 50],
        pageSlot: 6,
        onChange: (page: number) => {
            paginationReactive.page = page;
            updateTable();
        },
        onUpdatePageSize: (pageSize: number) => {
            paginationReactive.pageSize = pageSize;
            paginationReactive.page = 1;
            updateTable();
        }
    });

    let columns1: DataTableColumns<RowData> = [
        { title: '制定时间', key: 'createTime', width: 160, align: 'center' },
        { title: '申报月份', key: 'strategyDate', align: 'center' },
        { title: '申报电量', key: 'power', align: 'center' },
        { title: '最低电价', key: 'priceLimit', align: 'center' },
        { title: '申报电价', key: 'price', align: 'center' },
        {
            title: '策略记录',
            key: 'detail',
            align: 'center',
            render: (row: RowData) => (
                <NPopover trigger="hover" placement="left-start">
                    {{
                        default: () => (
                            <NDataTable
                                maxHeight={'60vh'}
                                style={{ 'width': '60vw', 'max-width': '60vw' }}
                                columns={detailColumns}
                                data={row.details}
                            />
                        ),
                        trigger: () => (row.details.length > 0 ? checkStatus(row.details[0]) : '')
                    }}
                </NPopover>
            )
        }
    ];
    let detailColumns: DataTableColumns<ProxyPurchaseConfig> = [
        { title: '制定时间', key: 'createTime', width: 160, align: 'center' },
        { title: '申报电量', key: 'power', align: 'center' },
        { title: '最低电价', key: 'priceLimit', align: 'center' },
        { title: '申报电价', key: 'price', align: 'center' },
        { title: '状态', key: 'status', align: 'center', render: (row: ProxyPurchaseConfig) => checkStatus(row) }
    ];

    const updateTable = throttle(async () => {
        loading.value = true;
        let res = await queryRecordListReq<any>({
            unitId: props.query.unitId,
            page: paginationReactive
        });
        if (res.code === 1) {
            paginationReactive.pageCount = Math.ceil(res.data.total / paginationReactive.pageSize);
            paginationReactive.itemCount = res.data.total;
            data.value = res.data.data
                .sort((a: any, b: any) => (a.strategyDate > b.strategyDate ? -1 : 1))
                .map((it: any, index: number) => Object.assign(it, { key: index }));
        }
        loading.value = false;
    }, 300);

    function checkStatus(detail: RowData | ProxyPurchaseConfig) {
        if (detail.status === 1) {
            return <NTag type="success">成功</NTag>;
        }
        if (detail.status === 2) {
            return <NTag type="error">失败</NTag>;
        }
        if (detail.status === 3) {
            return <NTag type="warning">电价过低</NTag>;
        }
        return <NTag>待执行</NTag>;
    }

    defineExpose({ updateTable });
</script>
<style lang="scss" scoped>
    :deep(.opts) {
        display: flex;
        justify-content: space-between;
        user-select: none;
        margin-right: 15px;
        span {
            height: 18px;
            svg {
                height: 18px;
                width: 18px;
            }
        }
    }
</style>
