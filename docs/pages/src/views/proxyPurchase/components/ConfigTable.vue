<template>
    <div v-if="firstData" class="proxy-purchase-config-page">
        <n-form label-align="right" label-placement="left" label-width="200">
            <n-form-item label="申报月份:">
                <div>{{ firstData.strategyDate }}</div>
            </n-form-item>
            <n-form-item label="挂牌电量(MWh):">
                <n-input-number
                    v-model:value="firstData.power"
                    :precision="0"
                    :show-button="false"
                    size="small"
                    style="width: 100px"
                ></n-input-number>
            </n-form-item>
            <n-form-item label="最低电价(元/MWh):">
                <n-input-number
                    v-model:value="firstData.priceLimit"
                    :precision="2"
                    :show-button="false"
                    size="small"
                    style="width: 100px"
                ></n-input-number>
            </n-form-item>
            <n-form-item label=" ">
                <n-button :disabled="canConfirm" size="small" type="primary" @click="saveConfig()">确认</n-button>
            </n-form-item>
        </n-form>
    </div>
    <n-card v-else>
        <n-empty description="暂无数据" size="large" style="width: 100%"></n-empty>
    </n-card>
</template>
<script lang="tsx" setup>
    import { Ref } from 'vue';
    import { NButton, NInputNumber } from 'naive-ui';
    import { ProxyPurchaseConfig, Query } from '../types';
    import { queryConfigListReq, saveConfigReq } from '@/api/proxyPurchase';
    import dayjs from 'dayjs';

    const props = defineProps<{ query: Query; declareDate: string; height: number }>();
    const emit = defineEmits(['save']);

    const loading: Ref<boolean> = ref(false);
    const firstData: Ref<ProxyPurchaseConfig | null> = ref(null);
    const message = useMessage();

    onMounted(() => {
        updateConfig();
    });

    const now = ref(dayjs().format('YYYY-MM-DD HH:mm:ss'));
    const cantConfirm = ref(false);

    setInterval(() => {
        now.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
    }, 3000);

    const canConfirm = computed(() => {
        if (cantConfirm.value) {
            return true;
        }
        if (firstData.value?.startTime && firstData.value?.endTime) {
            return now.value >= firstData.value.endTime;
        }
        return true;
    });

    async function updateConfig(): Promise<ProxyPurchaseConfig | null> {
        loading.value = true;
        try {
            let res = await queryConfigListReq<ProxyPurchaseConfig[]>({
                unitId: props.query.unitId,
                strategyDate: props.query.strategyDate
            });
            if (res.code === 1) {
                firstData.value = res.data[0] ?? null;
            }
        } finally {
            loading.value = false;
        }
        return firstData.value;
    }

    async function saveConfig() {
        const resp = await saveConfigReq({
            unitId: props.query.unitId,
            strategyDate: props.query.strategyDate,
            power: firstData.value?.power,
            priceLimit: firstData.value?.priceLimit
        });
        if (resp.code === 1) {
            message.success('保存成功');
            emit('save');
        } else if (resp.code === -1 && resp.msg.includes('保存失败，距离首次申报成功已超过')) {
            cantConfirm.value = true;
        }
    }

    defineExpose({ updateConfig });
</script>
<style lang="scss" scoped>
    .proxy-purchase-config-page {
        flex: 0 1 600px;
        display: flex;
        flex-direction: column;
        row-gap: 10px;
        :deep(.n-data-table .n-data-table-td.n-data-table-td--summary) {
            background-color: var(--n-merged-td-color);
            padding-top: 10px;
            padding-bottom: 10px;
        }
        :deep(.n-data-table-tr--summary td) {
            position: sticky;
            bottom: 0;
        }
    }
</style>
