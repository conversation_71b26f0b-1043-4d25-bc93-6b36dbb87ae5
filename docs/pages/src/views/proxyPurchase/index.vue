<template>
    <div class="proxy-purchase-page">
        <n-card class="header" size="small">
            <n-form ref="formRef" :model="query" :show-feedback="false" inline label-placement="left" size="small">
                <n-form-item label="省份" path="provinceId">
                    <n-select v-model:value="query.provinceId" :options="provinceList" style="width: 100px" />
                </n-form-item>
                <n-form-item label="交易单元" path="unitId">
                    <n-select v-model:value="query.unitId" :options="unitList" style="width: 200px"></n-select>
                </n-form-item>
                <n-form-item label="策略时间" path="strategyDate">
                    <n-date-picker
                        v-model:formatted-value="query.strategyDate"
                        :close-on-select="true"
                        input-readonly
                        style="width: 140px"
                        type="month"
                        update-value-on-close
                        value-format="yyyy-MM"
                    />
                </n-form-item>
                <n-form-item>
                    <n-flex>
                        <n-button size="small" type="primary" @click="updateData">查询</n-button>
                        <n-button size="small" type="primary" @click="exportRecord(false)">导出</n-button>
                        <n-button size="small" type="primary" @click="exportRecord(true)">一键导出</n-button>
                    </n-flex>
                </n-form-item>
                <n-form-item>
                    <n-button :loading="callDetectJobStatus" size="small" type="warning" @click="callDetectJob">更新</n-button>
                </n-form-item>
            </n-form>
        </n-card>
        <n-card class="main" size="small">
            <div class="title">{{ dayjs(first?.strategyDate).format('YYYY年M月') }}月度电网代理购电挂牌电力直接交易</div>
            <div class="title-sub" style="text-align: center">
                摘牌方申报阶段:{{ first?.startTime ? `${first.startTime} 至 ${first.endTime}` : '暂无' }}
            </div>
            <div class="wrapper">
                <div ref="configWrapperRef" class="config-table">
                    <config-table ref="configRef" :declare-date="''" :height="configHeight" :query="query" @save="updateData" />
                </div>
                <div class="record-table">
                    <record-table ref="recordTableRef" :height="configHeight" :query="query" />
                </div>
            </div>
        </n-card>
    </div>
</template>
<script lang="ts" setup>
    import { Ref } from 'vue';
    import dayjs from 'dayjs';
    import { exportAllRecordReq, exportRecordReq, unitListReq, updateByParamsReq } from '@/api/proxyPurchase';
    import { UnitConfig } from '@/types';
    import { ProxyPurchaseConfig, Query } from './types';
    import ConfigTable from './components/ConfigTable.vue';
    import RecordTable from './components/RecordTable.vue';
    import { useCommonStore } from '@/stores/common';
    import { useProvinceStore } from '@/stores/province';
    import ExcelJS from 'exceljs';

    const dateFormat = 'YYYY-MM';

    const commonStore = useCommonStore();
    const provinceStore = useProvinceStore();
    const provinceList = ref(provinceStore.getProvinceList());

    const unitList: Ref<any[]> = ref([]);
    const query: Ref<Query> = ref({
        provinceId: provinceList.value[0].value,
        unitId: '',
        strategyDate: dayjs().add(1, 'month').format(dateFormat)
    });
    const first: Ref<ProxyPurchaseConfig | null> = ref(null);

    const message = useMessage();



    async function exportRecord(exportAll: boolean) {
        let resp = exportAll
            ? await exportAllRecordReq<{ [key: string]: any[] }>({ type: 1 })
            : await exportRecordReq<{ [key: string]: any[] }>({
                  unitId: query.value.unitId,
                  strategyDate: query.value.strategyDate,
                  type: 1
              });
        const res = resp.data;
        const workbook = new ExcelJS.Workbook();
        Object.keys(res).forEach((sheetName) => {
            const worksheet = workbook.addWorksheet(sheetName);
            // 设置工作表列宽和行高
            worksheet.columns = [25, 15, 15, 15, 15].map((width) => ({ width }));
            worksheet.addRow(['制定时间', '申报时间', '申报电量', '申报电价', '状态']);
            const keyMap = ['createTime', 'strategyDate', 'power', 'price', 'status'];
            // 添加数据
            res[sheetName]
                .sort((a: any, b: any) => (a.strategyDate > b.strategyDate ? -1 : 1))
                .map((pointRow) => Object.assign(pointRow, { status: pointRow.status === 1 ? '成功' : pointRow.status === 2 ? '失败' : '待执行' }))
                .forEach((pointRow) => worksheet.addRow(keyMap.map((key) => pointRow[key])));

            function setStyle(cell: any) {
                cell.alignment = { vertical: 'middle', horizontal: 'center', wrapText: true };
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            }

            for (let rowIndex = 1; rowIndex <= res[sheetName].length + 1; rowIndex++) {
                const row = worksheet.getRow(rowIndex);
                for (let colIndex = 1; colIndex <= keyMap.length; colIndex++) {
                    let cell = row.getCell(colIndex);
                    setStyle(cell);
                }
            }
        });
        workbook.xlsx.writeBuffer().then((buffer) => {
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            let unitName = unitList.value.filter((it) => it.value === query.value.unitId)[0].label;
            a.download = `${(exportAll ? '' : unitName + ' ') + query.value.strategyDate}月度电网代理购电挂牌电力直接交易记录.xlsx`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });
        return;
    }

    const configRef = ref();
    const recordTableRef = ref();

    async function updateData() {
        configRef.value.updateConfig().then((res: ProxyPurchaseConfig | null) => {
            first.value = res;
            if (first.value === null) {
                first.value = { unitId: query.value.unitId, strategyDate: query.value.strategyDate } as ProxyPurchaseConfig;
            }
        });
        recordTableRef.value.updateTable();
    }

    const configWrapperRef = ref();
    const configHeight = ref(300);

    commonStore.$subscribe(() => {
        if (configWrapperRef.value) {
            configHeight.value = window.innerHeight - Math.ceil(configWrapperRef.value.getBoundingClientRect().top) - 105;
            console.log(configHeight.value);
        }
    });

    onMounted(async () => {
        let res = await unitListReq<UnitConfig[]>();
        if (res.code !== 1) {
            message.error(res.msg);
        }
        if (res.code === 1 && res.data.length > 0) {
            unitList.value = res.data.map((it: any) => Object.assign({}, { label: it.unitName, value: it.unitId }));
            query.value.unitId = unitList.value[0].value;
            await updateData();
        }
    });

    const callDetectJobStatus = ref(false);

    async function callDetectJob() {
        callDetectJobStatus.value = true;
        try {
            const res = await updateByParamsReq<boolean>({
                unitId: query.value.unitId,
                strategyDate: query.value.strategyDate
            });
            if (res.code === 1) {
                message.success('刷新完成');
                await updateData();
            } else {
                message.error('任务出错: ' + res.msg);
            }
        } catch (e) {
            message.error('任务出错: ' + ((e as Error)?.message ?? '未知错误'));
        } finally {
            callDetectJobStatus.value = false;
        }
    }
</script>
<style lang="scss" scoped>
    .proxy-purchase-page {
        display: flex;
        height: 100%;
        flex-direction: column;
        padding: 10px;
        row-gap: 10px;
        .header {
            flex: 0 0 20px;
            .n-form.n-form--inline {
                flex-wrap: wrap;
                gap: 10px 0;
            }
        }
        .main {
            flex: 1;
            overflow: auto;
            --title-height: 50px;
            --title-sub-height: 40px;
            .title {
                text-align: center;
                color: red;
                font-size: 22px;
                font-family: serif;
                height: var(--title-height);
                padding-bottom: 20px;
            }
            .title-sub {
                text-align: center;
                height: var(--title-sub-height);
            }
            .wrapper {
                height: calc(100% - var(--title-height) - var(--title-sub-height));
                display: flex;
                justify-content: space-between;
                column-gap: 10px;
                .config-table {
                    flex: 0 1 500px;
                }
                .record-table {
                    flex: 0 1 600px;
                }
            }
        }
    }
</style>
