export interface Query {
    provinceId: string;
    unitId: string;
    strategyDate: string;
}

export interface ProxyPurchaseConfig {
    unitId: string;
    strategyDate: string;
    unitName: string | undefined;
    tradeseqId: string | undefined;
    tradeseqName: string | undefined;
    listedId: string | undefined;
    startTime: string | undefined;
    endTime: string | undefined;
    price: number | undefined;
    power: number | undefined;
    priceLimit: number | undefined;
    status: number | undefined;
    createTime: string | undefined;
    updateTime: string | undefined;
}

export interface RowData extends ProxyPurchaseConfig {
    details: ProxyPurchaseConfig[];
}
