<template>
    <div class="auto-login-wrapper">
        <n-table :single-line="false">
            <thead>
                <tr>
                    <th>账号</th>
                    <th>名称</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="unit in accountConfigs" :key="unit.plantId">
                    <template v-if="unit.plantId">
                        <td>{{ unit.userName }}</td>
                        <td>{{ unit.plantName }}</td>
                        <td style="width: 200px">
                            <n-button :loading="loadingMap[unit.plantId]" size="small" type="primary" @click="tryLogin(unit)">登录</n-button>
                        </td>
                    </template>
                </tr>
            </tbody>
        </n-table>
    </div>
</template>
<script lang="ts" setup>
    import { onMounted, ref, Ref } from 'vue';
    import { queryAccountList } from '@/api/account';
    import { AccountConfig } from '@/views/autoLogin/types';
    import { throttle } from 'lodash';
    import { getCookies, login } from '@/api/login';
    import { RoleType } from '@/types';

    const loadingMap: Ref<any> = ref({});

    const tryLogin = throttle(async (config: AccountConfig) => {
        loadingMap.value[config.plantId ?? ''] = true;
        try {
            if (config.plantId) {
                console.log('login, plantId:', config.plantId);
                window.$message.success('正在登录, 用户: ' + config.userName);
                let res = await login(config.plantId);
                if (res.code === 1) {
                }
                await getCookies(config.userName ?? '');
                loadingMap.value[config.plantId ?? ''] = false;
            }
        } catch (e) {
            loadingMap.value[config.plantId ?? ''] = false;
        }
    }, 1000);

    const accountConfigs: Ref<AccountConfig[]> = ref([]);
    onMounted(async () => {
        let result = await queryAccountList<AccountConfig[]>(RoleType.AUTO_LOGIN);
        console.log(result);
        if (result.code === 1) {
            accountConfigs.value = result.data;
            accountConfigs.value.forEach((unit) => (loadingMap.value[unit.plantId ?? ''] = false));
        }
    });
</script>
<style scoped lang="scss">
    .auto-login-wrapper {
        padding: 10px;
    }
</style>
