export interface Query {
    date: string;
    unitId: string | null;
    tradeseqId: string | null;
}

export interface DayRollingInfoMarket {
    strategyDate: string;
    declareDate: string;
    timeCode: number;
    tradeseqId: string | null;
    tradeseqName: string | null;
    total: string | null;
    maxPrice: string | null;
    minPrice: string | null;
    weightedPrice: string | null;
    midPrice: string | null;
    createTime: string | null;
}
export interface DayRollingInfoUnit {
    unitId: string;
    strategyDate: string;
    tradeSeqId: string;
    timeCode: number;
    declareDate: string | null;
    tradeRole: string | null;
    power: string | null;
    price: string | null;
    detail: string | null;
    createTime: string | null;
}
export interface DayRollingInfoUnitDetail {
    tradeDirection: number;
    [key: string]: any;
}

export const timeArray = [
    '00:00',
    '01:00',
    '02:00',
    '03:00',
    '04:00',
    '05:00',
    '06:00',
    '07:00',
    '08:00',
    '09:00',
    '10:00',
    '11:00',
    '12:00',
    '13:00',
    '14:00',
    '15:00',
    '16:00',
    '17:00',
    '18:00',
    '19:00',
    '20:00',
    '21:00',
    '22:00',
    '23:00',
    '24:00'
];
