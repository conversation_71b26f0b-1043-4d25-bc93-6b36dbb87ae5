<template>
    <div class="day-rolling-page">
        <n-card class="header" size="small">
            <n-form ref="formRef" :model="query" :show-feedback="false" inline label-placement="left" size="small">
                <n-form-item label="交易日期" path="date">
                    <n-date-picker v-model:formatted-value="query.date" :close-on-select="true" style="width: 140px" value-format="yyyy-MM-dd" />
                </n-form-item>
                <n-form-item v-if="activeTab === 'unit'" label="交易单元" path="unitId">
                    <n-select v-model:value="query.unitId" :options="unitList" style="width: 200px"></n-select>
                </n-form-item>
                <n-form-item label="交易名称" path="tradeseqId">
                    <n-select
                        v-model:value="query.tradeseqId"
                        :options="Object.keys(tradeList).map((key:string) => ({ label: tradeList[key], value: key }))"
                        style="width: 300px"
                    ></n-select>
                </n-form-item>
                <n-form-item>
                    <n-flex>
                        <n-button
                            v-if="activeTab === 'market'"
                            :disabled="!query.tradeseqId"
                            :title="query.tradeseqId ? '' : '请选择交易名称'"
                            size="small"
                            type="primary"
                            @click="updateMarketTable"
                        >
                            查询
                        </n-button>
                        <n-button v-else :disabled="!query.tradeseqId" size="small" type="primary" @click="updateUnitTable">查询 </n-button>
                        <n-button
                            :disabled="!query.tradeseqId"
                            :title="query.tradeseqId ? '' : '请选择交易名称'"
                            size="small"
                            type="primary"
                            @click="exportRecord"
                            >导出
                        </n-button>
                    </n-flex>
                </n-form-item>
                <n-form-item>
                    <n-button :loading="crawlerInfoStatus" size="small" type="warning" @click="callCrawlerInfo"> 更新 </n-button>
                </n-form-item>
                <n-upload
                    v-if="activeTab === 'market'"
                    ref="uploadRef"
                    v-model:file-list="importFileList"
                    :custom-request="importMarket"
                    :default-upload="true"
                    :max="1"
                    :style="{ opacity: uploadOpacity }"
                    accept=".xlsx"
                    style="width: 60px; position: absolute; right: 12px; top: 12px"
                    @before-upload="beforeUpload"
                >
                    <n-button size="small" type="primary">导入</n-button>
                </n-upload>
                <n-upload
                    v-else
                    ref="uploadRef"
                    v-model:file-list="importFileList"
                    :custom-request="importUnit"
                    :default-upload="true"
                    :max="1"
                    :style="{ opacity: uploadOpacity }"
                    accept=".xlsx"
                    style="width: 60px; position: absolute; right: 12px; top: 12px"
                    @before-upload="beforeUpload"
                >
                    <n-button size="small" type="primary">导入</n-button>
                </n-upload>
            </n-form>
        </n-card>
        <n-card
            :content-style="{ 'height': '100%', 'width': '100%', 'display': 'flex', 'justify-content': 'space-between', 'column-gap': '10px' }"
            class="article"
            size="small"
        >
            <n-tabs
                :on-update:value="(value: string) => (activeTab = value)"
                :value="activeTab"
                animated
                pane-wrapper-class="config-wrapper"
                type="line"
            >
                <n-tab-pane ref="tabRef" name="market" style="height: 100%" tab="市场交易信息">
                    <n-data-table
                        :columns="marketTableColumns"
                        :data="marketInfoTableData"
                        :loading="tableLoading"
                        :max-height="tableHeight"
                        remote
                        size="small"
                    />
                </n-tab-pane>
                <n-tab-pane name="unit" tab="我的交易结果">
                    <n-data-table
                        :columns="unitTableColumns"
                        :data="unitInfoTableData"
                        :loading="tableLoading"
                        :max-height="tableHeight"
                        remote
                        size="small"
                    />
                </n-tab-pane>
            </n-tabs>
        </n-card>
    </div>
</template>
<script lang="tsx" setup>
    import { DataTableColumns, NButton, NDataTable, NPopover, UploadCustomRequestOptions, UploadFileInfo } from 'naive-ui';
    import { Ref } from 'vue';
    import dayjs from 'dayjs';
    import { queryUnitList } from '@/api/unit';
    import { crawlerInfo, importMarketInfo, importUnitInfo, queryInfoMarketList, queryInfoTradeList, queryInfoUnitList } from '@/api/dayRolling';
    import { RoleType, UnitConfig } from '@/types';
    import { DayRollingInfoMarket, DayRollingInfoUnit, DayRollingInfoUnitDetail, Query, timeArray } from '@/views/dayRollingInfo/types';
    import { useCommonStore } from '@/stores/common';
    import { read, utils, writeFile } from 'xlsx';

    const unitList: Ref<any[]> = ref([]);
    const activeTab: Ref<string> = ref('market');
    const importFileList = ref([]);
    watch(
        () => activeTab.value,
        (newVal) => {
            importFileList.value = [];
            if (newVal === 'market') {
                nextTick(() => {
                    updateMarketTable();
                });
            } else {
                nextTick(() => {
                    updateUnitTable();
                });
            }
        },
        { immediate: true }
    );
    const query: Ref<Query> = ref({
        unitId: null,
        date: dayjs().format('YYYY-MM-DD'),
        tradeseqId: null
    });

    const tradeList: Ref<{ [key: string]: string }> = ref({});

    watch(
        () => query.value.date,
        () => {
            getTradeList();
        },
        { immediate: true }
    );

    async function getTradeList() {
        tradeList.value = {};
        query.value.tradeseqId = null;
        let res = await queryInfoTradeList<DayRollingInfoMarket[]>({
            date: query.value.date
        });
        if (res.code === 1) {
            tradeList.value = res.data as any;
        }
    }

    const tableLoading: Ref<boolean> = ref(false);

    const marketTableColumns: DataTableColumns<DayRollingInfoMarket> = [
        {
            title: '时段类型',
            key: 'timeCode',
            align: 'center',
            render: (row) => timeArray[Number(row.timeCode) - 1] + '-' + timeArray[Number(row.timeCode)]
        },
        { title: '总交易量(日均)', key: 'total', align: 'center' },
        { title: '最高价', key: 'maxPrice', align: 'center' },
        { title: '最低价', key: 'minPrice', align: 'center' },
        { title: '加权价格', key: 'weightedPrice', align: 'center' },
        { title: '中位数价格', key: 'midPrice', align: 'center' }
    ];
    const marketInfoTableData: Ref<DayRollingInfoMarket[]> = ref([]);

    async function updateMarketTable() {
        tableLoading.value = true;
        let res = await queryInfoMarketList<DayRollingInfoMarket[]>({
            date: query.value.date,
            tradeseqId: query.value.tradeseqId
        });
        if (res.code === 1) {
            marketInfoTableData.value = res.data?.filter((it) => it.timeCode !== -1);
        } else {
        }
        tableLoading.value = false;
    }

    const unitDetailTableColumns: DataTableColumns<DayRollingInfoUnitDetail> = [
        { title: '交易单元名称', key: 'unitName', align: 'center' },
        {
            title: '时段类型',
            key: 'timeCode',
            align: 'center',
            render: (row) => timeArray[Number(row.timeCode) - 1] + '-' + timeArray[Number(row.timeCode)]
        },
        {
            title: '交易方向',
            key: 'tradeDirection',
            align: 'center',
            width: 90,
            render: (row) => (row.tradeDirection === 1 ? '买入' : '卖出')
        },
        { title: '成交电量', key: 'dealEnergy', align: 'center' },
        { title: '成交电价', key: 'dealPrice', align: 'center' },
        { title: '申报时间', key: 'bidTime', align: 'center', width: 180, render: (row) => row.bidTime.substring(0, 19) },
        { title: '成交时间', key: 'dealTime', align: 'center', width: 180, render: (row) => row.bidTime.substring(0, 19) }
    ];

    const unitTableColumns: DataTableColumns<DayRollingInfoUnit> = [
        {
            title: '时段类型',
            key: 'timeCode',
            align: 'center',
            render: (row) => timeArray[Number(row.timeCode) - 1] + '-' + timeArray[Number(row.timeCode)]
        },
        {
            title: '交易单元',
            key: 'unitId',
            align: 'center',
            render: (row) => unitList.value.filter((unit) => unit.value === row.unitId)[0]?.label ?? ''
        },
        { title: '买卖方向', key: 'tradeRole', align: 'center', render: (row) => (row.tradeRole === '1' ? '买方' : '卖方') },
        { title: '成交电量(日均)', key: 'power', align: 'center' },
        { title: '成交均价', key: 'price', align: 'center' },
        {
            title: '成交明细',
            key: 'detail',
            align: 'center',
            render: (row: DayRollingInfoUnit) => (
                <NPopover trigger="click" placement="left-start">
                    {{
                        default: () => (
                            <NDataTable
                                flexHeight={true}
                                style={{ 'height': '60vh', 'width': '70vw', 'max-height': '60vh', 'max-width': '70vw' }}
                                columns={unitDetailTableColumns}
                                data={JSON.parse(row.detail || '[]')}
                            />
                        ),
                        trigger: () => <NButton text>查看</NButton>
                    }}
                </NPopover>
            )
        }
    ];
    const unitInfoTableData: Ref<DayRollingInfoUnit[]> = ref([]);

    async function updateUnitTable() {
        tableLoading.value = true;
        let res = await queryInfoUnitList<DayRollingInfoUnit[]>({
            unitId: query.value.unitId,
            date: query.value.date,
            tradeseqId: query.value.tradeseqId
        });
        if (res.code === 1) {
            unitInfoTableData.value = res.data?.filter((it) => it.timeCode !== -1);
        } else {
        }
        tableLoading.value = false;
    }

    const commonStore = useCommonStore();

    const message = useMessage();

    async function exportRecord() {
        if (activeTab.value === 'market') {
            let res = await queryInfoMarketList<DayRollingInfoMarket[]>({
                date: query.value.date,
                tradeseqId: query.value.tradeseqId
            });
            if (res.code === 1) {
                let list = res.data?.filter((it) => it.timeCode !== -1);
                const workbook = utils.book_new();
                let header = ['timeCode', 'total', 'maxPrice', 'minPrice', 'weightedPrice', 'midPrice'];
                const worksheet = utils.json_to_sheet(
                    list
                        .map((it: any) => header.reduce((acc, key) => ({ ...acc, [key]: it[key] }), {}))
                        .map((it: any) => {
                            it.timeCode = timeArray[Number(it.timeCode) - 1] + '-' + timeArray[Number(it.timeCode)];
                            return it;
                        }),
                    { header: header }
                );
                worksheet['!cols'] = Array(6).fill({ wch: 15 });
                utils.sheet_add_aoa(worksheet, [['分时段类型', '总交易量', '最高价', '最低价', '加权价格', '中位数价格']], { origin: 'A1' });
                utils.book_append_sheet(workbook, worksheet, '市场交易信息');
                writeFile(workbook, `市场交易信息-${tradeList.value[query.value.tradeseqId ?? ''] ?? undefined}.xlsx`, { compression: true });
            } else {
                message.error(res.msg);
            }
        } else {
            let res = await queryInfoUnitList<DayRollingInfoUnit[]>({
                unitId: query.value.unitId,
                date: query.value.date,
                tradeseqId: query.value.tradeseqId
            });
            if (res.code === 1) {
                let list = res.data?.filter((it) => it.timeCode !== -1);
                const workbook = utils.book_new();
                let header = ['timeCode', 'unitId', 'tradeRole', 'power', 'price'];
                const worksheet = utils.json_to_sheet(
                    list
                        .map((item: any) => header.reduce((acc, key) => ({ ...acc, [key]: item[key] }), {}))
                        .map((it: any) => {
                            console.log(it.unitId, unitList.value);
                            it.timeCode = timeArray[Number(it.timeCode) - 1] + '-' + timeArray[Number(it.timeCode)];
                            it.unitId = unitList.value.filter((unit) => unit.value === it.unitId)[0]?.label ?? '';
                            it.tradeRole = it.tradeRole === '1' ? '买入' : '卖出';
                            return it;
                        }),
                    { header: header }
                );
                worksheet['!cols'] = Array(5).fill({ wch: 15 });
                utils.sheet_add_aoa(worksheet, [['分时段类型', '交易单元', '买卖方向', '成交电量', '成交均价']], { origin: 'A1' });
                utils.book_append_sheet(workbook, worksheet, '我的交易结果');
                writeFile(workbook, `我的交易结果-${tradeList.value[query.value.tradeseqId ?? ''] ?? undefined}.xlsx`, { compression: true });
            } else {
                message.error(res.msg);
            }
        }
    }

    const tabRef = ref();
    const tableHeight = ref(300);
    commonStore.$subscribe(() => {
        if (tabRef.value) {
            tableHeight.value = window.innerHeight - Math.ceil(tabRef.value.$el.getBoundingClientRect().top) - 80;
        }
    });

    onMounted(async () => {
        let res = await queryUnitList<UnitConfig[]>(RoleType.DAY_ROLLING);
        if (res.code !== 1) {
            message.error(res.msg);
        }
        if (res.code === 1 && res.data.length > 0) {
            unitList.value = res.data.map((it: any) => Object.assign({}, { label: it.uName, value: it.unitId }));
            query.value.unitId = unitList.value[0].value;
            await updateMarketTable();
        }
    });

    const uploadOpacity = ref(0);

    async function beforeUpload(data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) {
        if (!query.value.tradeseqId) {
            message.error('请选择要导入的交易名称');
            return false;
        }
        if (activeTab.value === 'unit' && !query.value.unitId) {
            message.error('请选择要导入的交易名称');
            return false;
        }
        if (
            data.file.file?.type !== 'application/vnd.ms-excel' &&
            data.file.file?.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ) {
            message.error('只能上传Excel文件，请重新选择');
            return false;
        }
        return true;
    }

    async function importMarket(options: UploadCustomRequestOptions) {
        try {
            let arrayBuffer = await (options.file.file as File).arrayBuffer();
            let workbook = read(arrayBuffer);
            if (workbook.SheetNames[0] !== '市场交易信息') {
                message.warning('请选择市场交易信息文件');
                throw new Error('');
            }
            let sheetJson = utils.sheet_to_json(workbook.Sheets[workbook.SheetNames[0]], { header: 1 });
            sheetJson.shift();
            sheetJson = (sheetJson as any[][])
                .filter((row) => row.some((it) => it !== null))
                .map((row) => ({
                    timeCode: Number(row[0].substring(0, 2)) + 1,
                    strategyDate: query.value.date,
                    tradeseqId: query.value.tradeseqId,
                    tradeseqName: tradeList.value[query.value.tradeseqId ?? ''] ?? undefined,
                    total: row[1],
                    maxPrice: row[2],
                    minPrice: row[3],
                    weightedPrice: row[4],
                    midPrice: row[5]
                }))
                .filter((row) => row.tradeseqName !== undefined);

            let res = await importMarketInfo({
                list: sheetJson
            });
            if (res.code === 1) {
                message.success('导入成功');
                await updateMarketTable();
            } else {
                message.error(res.msg);
            }
        } finally {
            importFileList.value = [];
        }
    }

    async function importUnit(options: UploadCustomRequestOptions) {
        try {
            let arrayBuffer = await (options.file.file as File).arrayBuffer();
            let workbook = read(arrayBuffer);
            let sheetJson = utils.sheet_to_json(workbook.Sheets[workbook.SheetNames[0]], { header: 1 });
            sheetJson.shift();
            sheetJson = (sheetJson as any[][])
                .filter((row) => row.some((it) => it !== null))
                .map((row) => ({
                    unitId: query.value.unitId,
                    strategyDate: query.value.date,
                    tradeSeqId: query.value.tradeseqId,
                    timeCode: Number(row[0].substring(0, 2)) + 1,
                    tradeRole: row[2] === '买入' ? 1 : row[2] === '卖出' ? 2 : 3,
                    power: row[3],
                    price: row[4]
                }));

            let res = await importUnitInfo({
                list: sheetJson
            });
            if (res.code === 1) {
                message.success('导入成功');
                await updateUnitTable();
            } else {
                message.error(res.msg);
            }
        } finally {
            importFileList.value = [];
        }
    }

    const crawlerInfoStatus = ref(false);

    async function callCrawlerInfo() {
        crawlerInfoStatus.value = true;
        try {
            const res = await crawlerInfo<null>({
                unitId: query.value.unitId,
                date: query.value.date
            });
            if (res.code === 1) {
                message.success('刷新完成');
                await updateMarketTable();
            } else {
                message.error('任务出错: ' + res.msg);
            }
        } catch (e) {
            message.error('任务出错: ' + (e as Error)?.message);
        } finally {
            crawlerInfoStatus.value = false;
        }
    }
</script>
<style lang="scss" scoped>
    .day-rolling-page {
        display: flex;
        height: 100%;
        flex-direction: column;
        padding: 10px;
        row-gap: 10px;
        .header {
            flex: 0 0 20px;
            .n-form.n-form--inline {
                flex-wrap: wrap;
                gap: 10px 0;
            }
        }
        .tabs-wrapper {
            height: 40px;
            display: flex;
            justify-content: flex-start;
            column-gap: 100px;
            align-items: center;
            .upload {
                .n-upload {
                    display: flex;
                    :deep(.n-upload-file-list) {
                        margin-top: 0;
                        .n-upload-file-info {
                            padding: 0;
                        }
                    }
                }
            }
        }
        .article {
            flex: 1;
            overflow: auto;
            :deep(.config-wrapper) {
                flex: 1;
                .config {
                    height: 100%;
                    display: flex;
                    column-gap: 10px;
                }
            }
            .record-table {
                flex: 0 0 400px;
            }
        }
    }
</style>
