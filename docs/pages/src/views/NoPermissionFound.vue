<template>
    <div class="no-permission-page">
        <n-result description="" size="huge" status="418" title="没有权限!">
            <template #footer>
                <div style="width: 50vw; margin: 0 auto; text-indent: 2rem; text-align: left"></div>
            </template>
        </n-result>
        <n-button v-if="hasPermission" type="primary" @click="router.push({ path: '/' })">刷新</n-button>
    </div>
</template>
<script lang="ts" setup>
    import router, { baseRoutes } from '@/router/index.js';
    import { MenuMeta, MenuType } from '@/types.js';

    const hasPermission = ref(true);
    onMounted(() => {
        const routes = router
            .getRoutes()
            .filter((route) => (route.meta as MenuMeta).type === MenuType.MENU && (route.meta as MenuMeta).hasPermission)
            .filter((route) => baseRoutes.map((route) => route.path).slice(0,1).indexOf(route.path) === -1);
        if (routes.length === 0) {
            hasPermission.value = false;
        }
    });
</script>
<style lang="scss" scoped>
    .no-permission-page {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        code {
            font-size: 30px;
        }
    }
</style>
