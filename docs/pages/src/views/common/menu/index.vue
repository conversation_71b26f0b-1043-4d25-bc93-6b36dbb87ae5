<template>
    <n-layout has-sider style="height: 100%">
        <n-layout-sider
            :collapsed="menuStore.collapsed"
            :collapsed-width="58"
            :width="'16rem'"
            bordered
            collapse-mode="width"
            content-class="sider-content"
            show-trigger
            @collapse="menuStore.switchCollapse()"
            @expand="menuStore.switchCollapse()"
        >
            <!--<div class="logo">
                    <transition name="slide-fade">
                        <img v-show="menuStore.collapsed" class="img" height="20" src="/favicon.ico" />
                    </transition>
                    <transition name="slide-fade">
                        <svg v-show="!menuStore.collapsed" class="text">
                            <text
                                v-for="i in 5"
                                :key="i"
                                :class="'text-anime-' + i"
                                class="text-anime"
                                dominant-baseline="central"
                                text-anchor="middle"
                                x="50%"
                                y="50%"
                            >
                                (╯°□°）╯
                            </text>
                        </svg>
                    </transition>
                </div>-->
            <n-menu
                ref="menuRef"
                v-model:value="defaultActive"
                :collapsed="menuStore.collapsed"
                :collapsed-icon-size="18"
                :collapsed-width="58"
                :icon-size="18"
                :indent="20"
                :options="menuOptions"
                accordion
            />
        </n-layout-sider>
        <n-layout class="right-wrapper">
            <div class="route-name">{{ currRouteName }}</div>
            <router-view></router-view>
        </n-layout>
    </n-layout>
</template>
<script lang="tsx" setup>
    import { RouteRecordRaw, RouterLink, useRoute } from 'vue-router';
    import router, { showRoutes } from '@/router';
    import { MenuOption } from 'naive-ui';
    import { Ref, VNodeChild } from 'vue';
    import { useCommonStore } from '@/stores/common';
    import { useMenuStore } from '@/stores/menu';
    import { useRouteStore } from '@/stores/route';
    import { SseClient } from '@/utils/SseClient';

    const commonStore = useCommonStore();
    const menuStore = useMenuStore();
    const routeStore = useRouteStore();
    const menuOptions: Ref<MenuOption[]> = ref([]);
    const defaultActive = ref('/logs');
    const menuRef = ref();

    let sseClient: SseClient;
    onMounted(async () => {
        defaultActive.value = router.currentRoute.value.path;
        menuOptions.value = convertMenu(showRoutes) ?? [];
        nextTick(() => {
            menuRef.value.showOption(defaultActive.value);
        }).then();

        if (menuOptions.value.some((path) => path.key === '/dr')) {
            sseClient = new SseClient(import.meta.env.VITE_BASE_URL + 'api/v1/dayRolling/sse', (event): void => {
                const { data } = event;
                window.$message.info(data);
            });
        }
    });
    menuStore.$subscribe(() => {
        // 折叠有动画，多更新几次
        for (let i = 0; i < 3; i++) {
            setTimeout(
                () => {
                    commonStore.onResize();
                },
                i * 120 + 100
            );
        }
    });
    const currRouteName: Ref<string> = ref('');
    const route = useRoute();
    watch(route, (route) => (currRouteName.value = route.matched.map((route) => route.name).join(' / ')), { immediate: true });
    watch(
        () => routeStore.lastVisitRoute,
        () => {
            if (routeStore.lastVisitRoute) {
                defaultActive.value = routeStore.lastVisitRoute;
                menuRef.value.showOption(defaultActive.value);
            }
        }
    );

    function convertMenu(menuArr: RouteRecordRaw[] | undefined): MenuOption[] | undefined {
        return menuArr
            ?.filter((menu) => menu.meta?.hasPermission)
            .sort((a, b) => ((a.meta?.order as number) ?? 1) - ((b.meta?.order as number) ?? 1))
            .map((menu) => ({
                children: convertMenu(menu.children),
                key: menu.path,
                label: () => <RouterLink to={{ path: menu.path }}>{menu.name}</RouterLink>,
                icon: () => (menu.meta?.icon as VNodeChild) ?? false
            }))
            .filter((menu) => menu.children === undefined || menu.children.length > 0);
    }

    onUnmounted(() => {
        sseClient?.close();
    });
</script>
<style lang="scss" scoped>
    .right-wrapper > :deep(.n-layout-scroll-container) {
        display: flex;
        flex-direction: column;
        .route-name {
            padding: 10px 12px 0;
        }
    }
    .sider-content {
        .logo {
            background: var(--n-color);
            height: 50px;
            line-height: 50px;
            position: sticky;
            top: 0;
            z-index: 2;
            margin-right: 1px;

            display: flex;
            justify-content: center;
            align-items: center;
            grid-gap: 10px;

            background: linear-gradient(-45deg, #ee775244, #e73c7e44, #23a6d544, #23d5ab44);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;

            .text {
                width: 100%;
                height: 50px;

                $time: 1.5s;
                $shadow: 6px;
                $shadow-offset: 2px;
                .text-anime {
                    font-size: 28px;
                    font-style: italic;
                    fill: none;
                    stroke-width: 1px;
                    stroke-dasharray: 90 310;
                    animation: stroke $time * 10 infinite linear;
                }
                .text-anime-1 {
                    $color: #ee775299;
                    stroke: $color;
                    text-shadow: -$shadow-offset $shadow-offset $shadow darken(opacify($color, 1), 10%);
                    animation-delay: $time;
                }
                .text-anime-2 {
                    $color: #24ac8399;
                    stroke: $color;
                    text-shadow: -$shadow-offset $shadow-offset $shadow darken(opacify($color, 1), 10%);
                    animation-delay: -$time;
                }
                .text-anime-3 {
                    $color: #a86aea99;
                    stroke: $color;
                    text-shadow: -$shadow-offset $shadow-offset $shadow darken(opacify($color, 1), 10%);
                    animation-delay: -$time * 3;
                }
                .text-anime-4 {
                    $color: #23a6d599;
                    stroke: $color;
                    text-shadow: -$shadow-offset $shadow-offset $shadow darken(opacify($color, 1), 10%);
                    animation-delay: -$time * 5;
                }
                .text-anime-5 {
                    $color: #e73c7e99;
                    stroke: $color;
                    text-shadow: -$shadow-offset $shadow-offset $shadow darken(opacify($color, 1), 10%);
                    animation-delay: -$time * 7;
                }
                @keyframes stroke {
                    100% {
                        stroke-dashoffset: -400;
                    }
                }
            }

            .slide-fade-enter-active {
                transition: all 0.3s ease-out;
            }

            .slide-fade-leave-active {
                transition: all 0.2s cubic-bezier(1, 0.5, 0.8, 1);
            }

            .slide-fade-enter-from,
            .slide-fade-leave-to {
                transform: translateX(-50px);
                opacity: 0;
            }

            @keyframes gradient {
                0% {
                    background-position: 0 50%;
                }
                50% {
                    background-position: 100% 50%;
                }
                100% {
                    background-position: 0 50%;
                }
            }
        }
    }
</style>
