export interface Query {
    provinceId: string;
    unitId: string[];
    dateRange: [string, string];
    type: number;
}

export interface RowData {
    unitId: string;
    date: string;
    time: string;
    ipDayAheadPrice: number;
    ipRealTimePrice: number;
    opbdReportPower: number;
    opbdBidPower: number;
    opbdBidPercent: number;
    opbdDayAheadBidPrice: number;
    opbdBidProfit: number;
    opidReportPower: number;
    opidBidPower: number;
    opidBidPercent: number;
    opidRealTimeBidPrice: number;
    opidBidProfit: number;
    totalBidProfit: number;
    createTime: string;
}
