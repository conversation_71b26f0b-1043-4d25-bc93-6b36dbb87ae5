<template>
    <n-config-provider :theme-overrides="{ DataTable: { thPaddingSmall: '3px', tdPaddingSmall: '3px' } }" abstract>
        <div class="profit-analysis-wrapper">
            <n-card class="header" size="small">
                <n-form ref="formRef" :model="query" :show-feedback="false" inline label-placement="left" size="small">
                    <n-form-item label="省份" path="provinceId">
                        <n-select v-model:value="query.provinceId" :options="provinceList" style="width: 100px" />
                    </n-form-item>
                    <n-form-item label="交易单元" path="unitId">
                        <n-select v-model:value="query.unitId" :max-tag-count="1" :options="unitList" style="width: 200px"></n-select>
                    </n-form-item>
                    <n-form-item label="时间" path="dateRange">
                        <n-date-picker
                            v-model:formatted-value="query.dateRange"
                            :close-on-select="true"
                            style="width: 260px"
                            type="daterange"
                            value-format="yyyy-MM-dd"
                        />
                    </n-form-item>
                    <n-flex>
                        <n-button size="small" type="primary" @click="queryList">查询</n-button>
                        <n-button size="small" type="primary" @click="exportFile(false)">导出</n-button>
                        <n-button size="small" type="primary" @click="exportFile(true)">一键导出</n-button>
                        <n-button
                            v-show="query.dateRange[0] === query.dateRange[1]"
                            :loading="updateLoading"
                            size="small"
                            type="info"
                            @click="updateData"
                            >更新
                        </n-button>
                    </n-flex>
                </n-form>
            </n-card>
            <div ref="tableWrapperRef" class="table-wrapper">
                <n-tabs
                    :default-value="1"
                    :value="query.type"
                    animated
                    class="tab-wrapper"
                    size="small"
                    type="card"
                    @update:value="(value: number) => (query.type = value)"
                >
                    <n-tab-pane :name="1" tab="96点">
                        <div>
                            <n-data-table
                                :bordered="true"
                                :bottom-bordered="true"
                                :columns="pointColumns"
                                :data="pointList"
                                :min-height="100"
                                :row-key="(rowData: RowData) => rowData.date + rowData.time"
                                :single-line="false"
                                :style="{ height: tableHeight + 'px' }"
                                flex-height
                                size="small"
                                virtual-scroll
                            ></n-data-table>
                        </div>
                    </n-tab-pane>
                    <n-tab-pane :name="2" tab="日">
                        <div>
                            <n-data-table
                                :bordered="true"
                                :bottom-bordered="true"
                                :columns="dayColumns"
                                :data="dayList"
                                :min-height="100"
                                :row-key="(rowData: RowData) => rowData.date + rowData.time"
                                :single-line="false"
                                :style="{ height: tableHeight + 'px' }"
                                flex-height
                                size="small"
                                virtual-scroll
                            ></n-data-table>
                        </div>
                    </n-tab-pane>
                </n-tabs>
            </div>
        </div>
    </n-config-provider>
</template>
<script lang="tsx" setup>
    import { DataTableColumns } from 'naive-ui';
    import { useCommonStore } from '@/stores/common';
    import { useProvinceStore } from '@/stores/province';
    import { Ref } from 'vue';
    import dayjs from 'dayjs';
    import { Query, RowData } from '@/views/profitAnalysis/types';
    import { queryUnitList } from '@/api/unit';
    import { RoleType, UnitConfig } from '@/types';
    import { getReq, postReq } from '@/api/common';
    import ExcelJS from 'exceljs';

    const commonStore = useCommonStore();
    const provinceStore = useProvinceStore();
    const message = useMessage();

    const dateFormat = 'YYYY-MM-DD';
    const provinceList = ref(provinceStore.getProvinceList());
    const unitList: Ref<any[]> = ref([]);
    const query: Ref<Query> = ref({
        provinceId: provinceList.value[0].value,
        unitId: [],
        dateRange: [dayjs().add(-1, 'day').format(dateFormat), dayjs().add(-1, 'day').format(dateFormat)],
        type: 1
    });

    const pointColumns: DataTableColumns<RowData> = [
        { title: '日期', key: 'date', align: 'center', colSpan: (_, rowIndex) => (rowIndex === 0 ? 2 : 1) },
        { title: '时点', key: 'time', align: 'center' },
        {
            title: '省内',
            key: 'ip',
            align: 'center',
            children: [
                { title: '日前电价(元/MWh)', key: 'ipDayAheadPrice', align: 'center' },
                { title: '实时电价(元/MWh)', key: 'ipRealTimePrice', align: 'center' }
            ]
        },
        {
            title: '省间日前',
            key: 'opbd',
            align: 'center',
            children: [
                { title: '申报出力(MW)', key: 'opbdReportPower', align: 'center' },
                { title: '中标出力(MW)', key: 'opbdBidPower', align: 'center' },
                {
                    title: '中标率',
                    key: 'opbdBidPercent',
                    align: 'center',
                    render: (row: RowData) => (isNaN(Number(row.opbdBidPercent)) ? '' : (row.opbdBidPercent * 100).toFixed(2) + '%')
                },
                { title: '日前中标电价(元/MWh)', key: 'opbdDayAheadBidPrice', align: 'center' },
                {
                    title: '中标收益(元)',
                    key: 'opbdBidProfit',
                    align: 'center',
                    className: 'blue-bg',
                    render: (row: RowData) => <span style={{ color: row.opbdBidProfit > 0 ? 'red' : '#00e400' }}>{row.opbdBidProfit}</span>
                }
            ]
        },
        {
            title: '省间日内',
            key: 'opbd',
            align: 'center',
            children: [
                { title: '申报出力(MW)', key: 'opidReportPower', align: 'center' },
                { title: '中标出力(MW)', key: 'opidBidPower', align: 'center' },
                {
                    title: '中标率',
                    key: 'opidBidPercent',
                    align: 'center',
                    render: (row: RowData) => (isNaN(Number(row.opidBidPercent)) ? '' : (row.opidBidPercent * 100).toFixed(2) + '%')
                },
                { title: '实时中标电价(元/MWh)', key: 'opidRealTimeBidPrice', align: 'center' },
                {
                    title: '中标收益(元)',
                    key: 'opidBidProfit',
                    align: 'center',
                    className: 'blue-bg',
                    render: (row: RowData) => <span style={{ color: row.opidBidProfit > 0 ? 'red' : '#00e400' }}>{row.opidBidProfit}</span>
                }
            ]
        },
        {
            title: '省间现货',
            key: 'op',
            align: 'center',
            children: [{ title: '总收益(元)', key: 'totalBidProfit', className: 'blue-bg', align: 'center' }],
            render: (row: RowData) => <span style={{ color: row.totalBidProfit > 0 ? 'red' : '#00e400' }}>{row.totalBidProfit}</span>
        }
    ];
    const dayColumns: DataTableColumns<RowData> = [
        { title: '日期', key: 'date', align: 'center' },
        ...pointColumns.filter((col: any) => col.key !== 'date' && col.key !== 'time')
    ];

    onMounted(async () => {
        let res = await queryUnitList<UnitConfig[]>(RoleType.PROFIT_ANALYSIS);
        if (res.code === 1 && res.data.length > 0) {
            unitList.value = res.data.map((it: any) => Object.assign({}, { label: it.uName, value: it.unitId }));
            query.value.unitId = unitList.value[0].value;
            await queryList();
        }
    });
    const pointList: Ref<RowData[]> = ref([]);
    const dayList: Ref<RowData[]> = ref([]);

    async function queryList() {
        const resp = await getReq<{ [key: string]: { pointList: RowData[]; dayList: RowData[] } }>('/profitAnalysis/list', {
            unitId: query.value.unitId,
            startDate: query.value.dateRange[0],
            endDate: query.value.dateRange[1]
        });
        pointList.value = Object.values(resp.data)[0].pointList;
        dayList.value = Object.values(resp.data)[0].dayList;
    }

    const updateLoading = ref(false);

    async function updateData() {
        updateLoading.value = true;
        try {
            const resp = await postReq<boolean>(
                '/profitAnalysis/update',
                { a: 1 },
                {
                    unitId: query.value.unitId,
                    date: query.value.dateRange[0]
                }
            );
            if (resp.code === 1) {
                message.success('获取成功');
                await queryList();
                updateLoading.value = false;
            }
        } catch (e) {
            updateLoading.value = false;
        } finally {
            updateLoading.value = false;
        }
    }

    async function exportFile(all: boolean) {
        const resp = await getReq<{ [key: string]: { pointList: RowData[]; dayList: RowData[] } }>('/profitAnalysis/list', {
            unitId: all ? null : query.value.unitId,
            startDate: query.value.dateRange[0],
            endDate: query.value.dateRange[1]
        });
        const res = resp.data;
        const workbook = new ExcelJS.Workbook();
        Object.keys(res).forEach((sheetName) => {
            const worksheet = workbook.addWorksheet(sheetName);
            // 设置工作表列宽和行高
            worksheet.columns = Array(31).fill({ width: 12 });
            // 添加标题和表头
            const row1 = [
                ...['日期', '时点', '省内', '', '省间日前', '', '', '', '', '省间日内', '', '', '', '', '省间现货'],
                '',
                ...['日期', '省内', '', '省间日前', '', '', '', '', '省间日内', '', '', '', '', '省间现货']
            ];
            const row2 = [
                ...['', '', '日前电价\n(元/MWh)', '实时电价\n(元/MWh)'],
                ...['申报出力\n(MW)', '中标出力\n(MW)', '中标率', '日前中标电价\n(元/MWh)', '中标收益\n(元)'],
                ...['申报出力\n(MW)', '中标出力\n(MW)', '中标率', '实时中标电价\n(元/MWh)', '中标收益\n(元)'],
                ...['总收益\n(元)'],
                '',
                ...['', '日前电价\n(元/MWh)', '实时电价\n(元/MWh)'],
                ...['申报出力\n(MW)', '中标出力\n(MW)', '中标率', '日前中标电价\n(元/MWh)', '中标收益\n(元)'],
                ...['申报出力\n(MW)', '中标出力\n(MW)', '中标率', '实时中标电价\n(元/MWh)', '中标收益\n(元)'],
                ...['总收益\n(元)']
            ];
            const keyMap = [
                ...['date', 'time', 'ipDayAheadPrice', 'ipRealTimePrice'],
                ...['opbdReportPower', 'opbdBidPower', 'opbdBidPercent', 'opbdDayAheadBidPrice', 'opbdBidProfit'],
                ...['opidReportPower', 'opidBidPower', 'opidBidPercent', 'opidRealTimeBidPrice', 'opidBidProfit'],
                ...['totalBidProfit'],
                '',
                ...['date', 'ipDayAheadPrice', 'ipRealTimePrice'],
                ...['opbdReportPower', 'opbdBidPower', 'opbdBidPercent', 'opbdDayAheadBidPrice', 'opbdBidProfit'],
                ...['opidReportPower', 'opidBidPower', 'opidBidPercent', 'opidRealTimeBidPrice', 'opidBidProfit'],
                ...['totalBidProfit']
            ];
            worksheet.addRow(row1);
            worksheet.addRow(row2);

            // 添加数据
            res[sheetName].pointList.forEach((pointRow: any, rowIndex: number) =>
                worksheet.addRow(
                    keyMap.map((key, keyIndex) => (keyIndex < 16 ? pointRow[key] : ((res as any)[sheetName].dayList[rowIndex] ?? {})[key]))
                )
            );

            // 添加合并单元格
            const mergeCells: number[][] = [
                [1, 1, 2, 1],
                [1, 2, 2, 2],
                [1, 3, 1, 4],
                [1, 5, 1, 9],
                [1, 10, 1, 14],
                [3, 1, 3, 2],

                [1, 17, 2, 17],
                [1, 18, 1, 19],
                [1, 20, 1, 24],
                [1, 25, 1, 29]
            ];
            mergeCells.forEach((points: any) => worksheet.mergeCells(points));

            worksheet.getRow(1).height = 32;
            worksheet.getRow(2).height = 32;

            // 设置单元格居中、细边框
            function setStyle(cell: any) {
                cell.alignment = { vertical: 'middle', horizontal: 'center', wrapText: true };
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            }

            for (let rowIndex = 1; rowIndex <= res[sheetName].pointList.length + 2; rowIndex++) {
                const row = worksheet.getRow(rowIndex);
                const hasDayVal = res[sheetName].dayList.length + 2 >= rowIndex;
                for (let colIndex = 1; colIndex <= keyMap.length; colIndex++) {
                    if (colIndex < 16 || (colIndex > 16 && hasDayVal)) {
                        let cell = row.getCell(colIndex);
                        setStyle(cell);
                        if (colIndex === 7 || colIndex === 12 || colIndex === 22 || colIndex === 27) {
                            cell.numFmt = '0.00%';
                        }
                    }
                }
            }
        });

        workbook.xlsx.writeBuffer().then((buffer) => {
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${query.value.dateRange[0].replace('-', '')}-${query.value.dateRange[1].replace('-', '')}-省间收益分析.xlsx`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });
        return;
    }

    const tableWrapperRef: Ref<HTMLDivElement | undefined> = ref();
    const tableHeight: Ref<number> = ref(500);
    commonStore.$subscribe(() => {
        if (tableWrapperRef.value) {
            tableHeight.value = window.innerHeight - Math.ceil(tableWrapperRef.value.getBoundingClientRect().top) - 56;
            console.log(tableHeight.value);
        }
    });
</script>
<style lang="scss" scoped>
    .profit-analysis-wrapper {
        display: flex;
        height: 100%;
        flex-direction: column;
        padding: 10px;
        row-gap: 10px;
        .header {
            flex: 0 0 20px;
            .n-form.n-form--inline {
                flex-wrap: wrap;
                gap: 10px 0;
            }
        }
        .table-wrapper {
            flex: 1;
        }
    }
    :deep(.n-data-table-tr) {
        .blue-bg {
            background-color: #b3dbff33;
        }
    }
    :deep(.n-data-table-base-table-body .n-data-table-table) {
        border-bottom: 1px solid var(--n-merged-border-color);
    }
</style>
