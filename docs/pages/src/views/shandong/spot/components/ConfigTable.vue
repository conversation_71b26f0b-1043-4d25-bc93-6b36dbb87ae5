<template>
    <div class="config-table">
        <el-form ref="formRef" :model="formData" label-width="auto">
            <el-table
                :data="tableData"
                :height="height - 80"
                border
                size="small"
                stripe
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55" />
                <el-table-column label="时间" prop="time" width="100" align="center" />
                <el-table-column label="申报类型" width="120" align="center">
                    <template #default="{ row }">
                        <el-select
                            v-model="row.strategyType"
                            size="small"
                            style="width: 100%"
                            :disabled="row.disabled"
                        >
                            <el-option
                                v-for="item in strategyTypeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="申报方式" width="120" align="center">
                    <template #default="{ row }">
                        <el-select
                            v-model="row.reportType"
                            size="small"
                            style="width: 100%"
                            :disabled="row.disabled"
                        >
                            <el-option
                                v-for="item in reportTypeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="申报电量(MW)" width="150" align="center">
                    <template #default="{ row }">
                        <el-input-number
                            v-model="row.power"
                            :precision="2"
                            :step="0.01"
                            :min="0"
                            size="small"
                            style="width: 100%"
                            :disabled="row.disabled"
                        />
                    </template>
                </el-table-column>
                <el-table-column label="实际申报电量(MW)" width="150" align="center">
                    <template #default="{ row }">
                        <span>{{ row.reportPower || '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="状态" width="100" align="center">
                    <template #default="{ row }">
                        <el-tag
                            :type="getStatusTagType(row.status)"
                            size="small"
                        >
                            {{ getStatusText(row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120" align="center" fixed="right">
                    <template #default="{ row, $index }">
                        <el-button
                            type="danger"
                            size="small"
                            @click="removeRow($index)"
                            :disabled="row.disabled"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-form>
        
        <div class="table-actions">
            <el-button type="primary" size="small" @click="addRow">
                添加时段
            </el-button>
            <el-button type="danger" size="small" @click="removeSelectedRows" :disabled="selectedRows.length === 0">
                批量删除
            </el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { ConfigRowData, Query } from '../types';
import { 
    timeOptions, 
    strategyTypeOptions, 
    reportTypeOptions, 
    statusTextMap 
} from '../types';

interface Props {
    modelValue: ConfigRowData[];
    query: Query;
    height: number;
}

interface Emits {
    (e: 'update:modelValue', value: ConfigRowData[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref();
const formData = ref({});
const selectedRows = ref<ConfigRowData[]>([]);
let keyCounter = 0;

// 使用 v-model 双向绑定
const tableData = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
});

// 添加行
const addRow = () => {
    const newRow: ConfigRowData = {
        key: ++keyCounter,
        timeIndex: tableData.value.length,
        account: props.query.account,
        strategyDate: props.query.strategyDate,
        time: '00:00',
        strategyType: 1,
        reportType: 1,
        power: 0,
        reportPower: 0,
        status: 0,
        disabled: false
    };
    
    const newData = [...tableData.value, newRow];
    tableData.value = newData;
};

// 删除行
const removeRow = (index: number) => {
    const newData = tableData.value.filter((_, i) => i !== index);
    tableData.value = newData;
};

// 批量删除选中行
const removeSelectedRows = () => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要删除的行');
        return;
    }
    
    const selectedKeys = selectedRows.value.map(row => row.key);
    const newData = tableData.value.filter(row => !selectedKeys.includes(row.key));
    tableData.value = newData;
    selectedRows.value = [];
};

// 处理选择变化
const handleSelectionChange = (selection: ConfigRowData[]) => {
    selectedRows.value = selection;
};

// 获取状态文本
const getStatusText = (status?: number) => {
    return status !== undefined ? statusTextMap[status] : '未申报';
};

// 获取状态标签类型
const getStatusTagType = (status?: number) => {
    switch (status) {
        case 1: return 'success';
        case 2: return 'danger';
        default: return 'info';
    }
};

// 监听查询条件变化，更新表格数据
watch(() => props.query, (newQuery) => {
    // 更新现有数据的 account 和 strategyDate
    const newData = tableData.value.map(row => ({
        ...row,
        account: newQuery.account,
        strategyDate: newQuery.strategyDate
    }));
    tableData.value = newData;
}, { deep: true });

// 暴露方法给父组件
defineExpose({
    addRow,
    removeRow,
    removeSelectedRows,
    validate: () => formRef.value?.validate()
});
</script>

<style scoped lang="scss">
.config-table {
    .table-actions {
        margin-top: 10px;
        display: flex;
        gap: 10px;
    }
}
</style>
