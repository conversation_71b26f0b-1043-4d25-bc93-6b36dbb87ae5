<template>
    <div class="record-table">
        <div class="table-header">
            <h4>历史记录</h4>
            <div class="header-actions">
                <el-button type="primary" size="small" @click="refreshData">
                    刷新
                </el-button>
                <el-button type="danger" size="small" @click="deleteSelected" :disabled="selectedRows.length === 0">
                    删除选中
                </el-button>
            </div>
        </div>
        
        <el-table
            :data="tableData"
            :height="height - 120"
            border
            size="small"
            stripe
            v-loading="loading"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55" />
            <el-table-column label="策略日期" prop="strategyDate" width="120" align="center" />
            <el-table-column label="时间" prop="time" width="100" align="center" />
            <el-table-column label="申报类型" width="100" align="center">
                <template #default="{ row }">
                    {{ getStrategyTypeText(row.strategyType) }}
                </template>
            </el-table-column>
            <el-table-column label="申报方式" width="100" align="center">
                <template #default="{ row }">
                    {{ getReportTypeText(row.reportType) }}
                </template>
            </el-table-column>
            <el-table-column label="申报电量(MW)" prop="power" width="130" align="center">
                <template #default="{ row }">
                    {{ row.power?.toFixed(2) || '-' }}
                </template>
            </el-table-column>
            <el-table-column label="实际申报电量(MW)" prop="reportPower" width="150" align="center">
                <template #default="{ row }">
                    {{ row.reportPower?.toFixed(2) || '-' }}
                </template>
            </el-table-column>
            <el-table-column label="状态" width="100" align="center">
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusTagType(row.status)"
                        size="small"
                    >
                        {{ getStatusText(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="createTime" width="160" align="center" />
            <el-table-column label="更新时间" prop="updateTime" width="160" align="center" />
            <el-table-column label="操作" width="120" align="center" fixed="right">
                <template #default="{ row }">
                    <el-button
                        type="primary"
                        size="small"
                        @click="editRecord(row)"
                        :disabled="row.status === 1"
                    >
                        编辑
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        
        <el-pagination
            v-if="total > 0"
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            style="margin-top: 10px; justify-content: center;"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { RecordRowData, Query, SdSpotConfig } from '../types';
import { 
    statusTextMap, 
    strategyTypeTextMap, 
    reportTypeTextMap 
} from '../types';
import { queryConfigListByDateRange, deleteConfig } from '@/api/sdSpot';

interface Props {
    query: Query;
    height: number;
}

interface Emits {
    (e: 'edit', record: SdSpotConfig): void;
    (e: 'delete', record: SdSpotConfig): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const loading = ref(false);
const tableData = ref<RecordRowData[]>([]);
const selectedRows = ref<RecordRowData[]>([]);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

// 获取状态文本
const getStatusText = (status?: number) => {
    return status !== undefined ? statusTextMap[status] : '未申报';
};

// 获取申报类型文本
const getStrategyTypeText = (strategyType?: number) => {
    return strategyType !== undefined ? strategyTypeTextMap[strategyType] : '-';
};

// 获取申报方式文本
const getReportTypeText = (reportType?: number) => {
    return reportType !== undefined ? reportTypeTextMap[reportType] : '-';
};

// 获取状态标签类型
const getStatusTagType = (status?: number) => {
    switch (status) {
        case 1: return 'success';
        case 2: return 'danger';
        default: return 'info';
    }
};

// 处理选择变化
const handleSelectionChange = (selection: RecordRowData[]) => {
    selectedRows.value = selection;
};

// 分页大小变化
const handleSizeChange = (size: number) => {
    pageSize.value = size;
    currentPage.value = 1;
    loadData();
};

// 当前页变化
const handleCurrentChange = (page: number) => {
    currentPage.value = page;
    loadData();
};

// 加载数据
const loadData = async () => {
    if (!props.query.account) return;
    
    loading.value = true;
    try {
        const params = {
            account: props.query.account,
            startDate: props.query.dateRange[0],
            endDate: props.query.dateRange[1]
        };
        
        const response = await queryConfigListByDateRange<SdSpotConfig[]>(params);
        if (response.code === 1) {
            const data = response.data || [];
            total.value = data.length;
            
            // 分页处理
            const start = (currentPage.value - 1) * pageSize.value;
            const end = start + pageSize.value;
            tableData.value = data.slice(start, end).map((item, index) => ({
                ...item,
                key: start + index
            }));
        }
    } catch (error) {
        console.error('加载数据失败:', error);
        ElMessage.error('加载数据失败');
    } finally {
        loading.value = false;
    }
};

// 刷新数据
const refreshData = () => {
    currentPage.value = 1;
    loadData();
};

// 编辑记录
const editRecord = (record: RecordRowData) => {
    emit('edit', record);
};

// 删除选中记录
const deleteSelected = async () => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要删除的记录');
        return;
    }
    
    try {
        await ElMessageBox.confirm(
            `确定要删除选中的 ${selectedRows.value.length} 条记录吗？`,
            '确认删除',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        );
        
        // 按策略日期分组删除
        const deletePromises = selectedRows.value.map(row => 
            deleteConfig({
                account: row.account,
                strategyDate: row.strategyDate
            })
        );
        
        await Promise.all(deletePromises);
        ElMessage.success('删除成功');
        selectedRows.value = [];
        refreshData();
        
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除失败:', error);
            ElMessage.error('删除失败');
        }
    }
};

// 监听查询条件变化
watch(() => props.query, () => {
    refreshData();
}, { deep: true });

// 组件挂载时加载数据
onMounted(() => {
    if (props.query.account) {
        loadData();
    }
});

// 暴露方法给父组件
defineExpose({
    refreshData,
    loadData
});
</script>

<style scoped lang="scss">
.record-table {
    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        
        h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
        }
    }
}
</style>
