<template>
    <n-data-table
        :columns="columns"
        :data="data"
        :loading="loading"
        :max-height="props.height - 25"
        :pagination="paginationReactive"
        remote
        size="small"
    />
</template>
<script lang="tsx" setup>
    import { Ref, ref } from 'vue';
    import { DataTableColumns, NDataTable, NPopover, NTag } from 'naive-ui';
    import { throttle } from 'lodash';
    import { DayRollingConfig, Query, timeArray } from '@/views/dayRollingMonitor/types';
    import { queryRecordList } from '@/api/dayRolling';

    const data: Ref<RowData[]> = ref([]);
    const loading: Ref<boolean> = ref(false);
    const props = defineProps<{ query: Query; height: number }>();

    const paginationReactive = reactive({
        page: 1,
        pageSize: 10,
        showSizePicker: true,
        pageCount: 1,
        itemCount: 0,
        pageSizes: [5, 10, 20, 50],
        pageSlot: 6,
        onChange: (page: number) => {
            paginationReactive.page = page;
            updateTable();
        },
        onUpdatePageSize: (pageSize: number) => {
            paginationReactive.pageSize = pageSize;
            paginationReactive.page = 1;
            updateTable();
        }
    });

    let columns: DataTableColumns<RowData> = [
        {
            title: '生效时间',
            key: 'strategyDate',
            align: 'center',
            render: (row: RowData) => row.strategyDate
        },
        {
            title: '申报时间',
            key: 'declareDate',
            width: '170px',
            align: 'center',
            render: (row: RowData) => row.declareDate
        },
        {
            title: '策略记录',
            key: 'detail',
            align: 'center',
            render: (row: RowData) => (
                <NPopover trigger="hover" placement="left-start">
                    {{
                        default: () => (
                            <NDataTable
                                flexHeight={true}
                                style={{ 'height': '60vh', 'width': '60vw', 'max-height': '60vh', 'max-width': '60vw', 'min-width': '900px' }}
                                columns={detailColumns}
                                data={row.details}
                                row-key={(row: DayRollingConfig) => row.timeCode}
                            />
                        ),
                        trigger: () => <NTag type="success">详情</NTag>
                    }}
                </NPopover>
            )
        }
    ];
    let detailColumns: DataTableColumns<DayRollingConfig> = [
        {
            type: 'expand',
            expandable: () => true,
            renderExpand: (row) => {
                return (
                    <NDataTable
                        size="small"
                        // flexHeight={true}
                        // style={{ 'height': '60vh', 'width': '60vw', 'max-height': '60vh', 'max-width': '60vw', 'min-width': '900px' }}
                        columns={detailLogColumns}
                        data={row.logs}
                        // row-key={(row: DayRollingConfig) => row.timeCode}
                    />
                );
            }
        },
        {
            title: '申报时段',
            key: 'timeCode',
            align: 'center',
            render: (row) => timeArray[row.timeCode - 1] + '-' + timeArray[row.timeCode]
        },
        {
            title: '申报方向',
            key: 'tradeRole',
            align: 'center',
            render: (row) => (row.tradeRole === '1' ? '买入' : row.tradeRole === '2' ? '卖出' : '-')
        },
        {
            title: '电量',
            key: 'power',
            align: 'center',
            render: (row) => row.power ?? '-'
        },
        {
            title: '电价',
            key: 'price',
            align: 'center',
            render: (row) => row.price ?? '-'
        },
        {
            title: '申报电量',
            key: 'reportPower',
            align: 'center',
            render: (row) => row.reportPower ?? '-'
        },
        {
            title: '申报电价',
            key: 'reportPrice',
            align: 'center',
            render: (row) => row.reportPrice ?? '-'
        },
        {
            title: '当前状态',
            key: 'status',
            align: 'center',
            render: (row) => checkStatus(row)
        },
        {
            title: '历史状态',
            key: 'historyStatus',
            align: 'center',
            render: (row) => checkStatus(row)
        }
    ];

    let detailLogColumns: DataTableColumns<DayRollingConfig> = [
        {
            title: '申报方向',
            key: 'tradeRole',
            align: 'center',
            render: (row) => (row.tradeRole === '1' ? '买入' : row.tradeRole === '2' ? '卖出' : '-')
        },
        {
            title: '电量',
            key: 'power',
            align: 'center',
            render: (row) => row.power ?? '-'
        },
        {
            title: '电价',
            key: 'price',
            align: 'center',
            render: (row) => row.price ?? '-'
        },
        {
            title: '申报电量',
            key: 'reportPower',
            align: 'center',
            render: (row) => row.reportPower ?? '-'
        },
        {
            title: '申报电价',
            key: 'reportPrice',
            align: 'center',
            render: (row) => row.reportPrice ?? '-'
        },
        {
            title: '状态',
            key: 'status',
            align: 'center',
            render: (row) => checkStatus(row)
        },
        {
            title: '创建时间',
            key: 'createTime',
            align: 'center',
            render: (row) => row.createTime
        }
    ];

    const updateTable = throttle(async () => {
        loading.value = true;
        let res = await queryRecordList<any>({
            unitId: props.query.unitIdList[0],
            page: paginationReactive,
            type: 2
        });
        if (res.code === 1) {
            paginationReactive.pageCount = Math.ceil(res.data.total / paginationReactive.pageSize);
            paginationReactive.itemCount = res.data.total;
            data.value = res.data.data
                .sort((a: any, b: any) => (a.strategyDate > b.strategyDate ? -1 : 1))
                .map((it: any, index: number) => Object.assign(it, { key: index }));
            data.value.forEach((row) => {
                let logsMapByTimeCode = row.detailLogs.reduce((prev: { [key: number]: DayRollingConfig[] }, curr) => {
                    if (!prev[curr.timeCode]) {
                        prev[curr.timeCode] = [];
                    }
                    prev[curr.timeCode].push(curr);
                    return prev;
                }, {});
                row.details.forEach((detail) => {
                    detail.logs = logsMapByTimeCode[detail.timeCode];
                });
            });
        }
        console.log(data.value);
        loading.value = false;
    }, 300);

    function checkStatus(detail: DayRollingConfig) {
        if (detail.status === 1) {
            return <NTag type="success">成功</NTag>;
        }
        if (detail.status === 2) {
            return <NTag type="error">失败</NTag>;
        }
        return <NTag>待执行</NTag>;
    }

    defineExpose({ updateTable });

    interface RowData {
        unitId: string;
        strategyDate: string;
        declareDate: string;
        details: DayRollingConfig[];
        detailLogs: DayRollingConfig[];
    }
</script>
<style scoped lang="scss">
    :deep(.opts) {
        display: flex;
        justify-content: space-between;
        user-select: none;
        margin-right: 15px;
        span {
            height: 18px;
            svg {
                height: 18px;
                width: 18px;
            }
        }
    }
</style>
