export interface Query {
    provinceId: string;
    unitId: string;
    strategyDate: string;
}

export interface ImqpNewEnergyTradeConfig {
    unitId: string;
    strategyDate: string;
    declareDate: string;
    trid: string | undefined;
    jydyid: string | undefined;
    startTime: string | undefined;
    endTime: string | undefined;
    startTimeAm: string | undefined;
    endTimeAm: string | undefined;
    startTimePm: string | undefined;
    endTimePm: string | undefined;
    zcPower: number | undefined;
    jcPower: number | undefined;
    yfPower: number | undefined;
    tradeRole: string | undefined;
    power: number | undefined;
    status: number;
    logId: string | undefined;
    createTime: string | undefined;
    updateTime: string | undefined;
}

export interface RowData extends ImqpNewEnergyTradeConfig {
    details: ImqpNewEnergyTradeConfig[];
}
