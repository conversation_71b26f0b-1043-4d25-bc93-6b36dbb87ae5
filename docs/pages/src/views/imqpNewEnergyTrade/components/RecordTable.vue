<template>
    <n-data-table
        :columns="columns"
        :data="data"
        :loading="loading"
        :max-height="props.height"
        :pagination="paginationReactive"
        remote
        size="small"
    />
</template>
<script lang="tsx" setup>
    import { Ref, ref } from 'vue';
    import { DataTableColumns, NDataTable, NPopover, NTag } from 'naive-ui';
    import { throttle } from 'lodash';
    import { ImqpNewEnergyTradeConfig, Query, RowData } from '../types';
    import { queryRecordList } from '@/api/imqpNewEnergyTrade';

    const data: Ref<RowData[]> = ref([]);
    const loading: Ref<boolean> = ref(false);
    const props = defineProps<{ query: Query; height: number }>();

    const paginationReactive = reactive({
        page: 1,
        pageSize: 10,
        showSizePicker: true,
        pageCount: 1,
        itemCount: 0,
        pageSizes: [5, 10, 20, 50],
        pageSlot: 6,
        onChange: (page: number) => {
            paginationReactive.page = page;
            updateTable();
        },
        onUpdatePageSize: (pageSize: number) => {
            paginationReactive.pageSize = pageSize;
            paginationReactive.page = 1;
            updateTable();
        }
    });

    let columns: DataTableColumns<RowData> = [
        {
            title: '策略时间',
            key: 'strategyDate',
            align: 'center',
            render: (row: RowData) => row.strategyDate
        },
        {
            title: '申报时间',
            key: 'declareDate',
            align: 'center',
            render: (row: RowData) => row.declareDate
        },
        {
            title: '策略记录',
            key: 'detail',
            align: 'center',
            render: (row: RowData) => (
                <NPopover trigger="hover" placement="left-start">
                    {{
                        default: () => (
                            <NDataTable
                                maxHeight={'60vh'}
                                style={{ 'width': '60vw', 'max-width': '60vw' }}
                                columns={detailColumns}
                                data={row.details}
                            />
                        ),
                        trigger: () => (row.details ? checkStatus(row.details[0]) : '')
                    }}
                </NPopover>
            )
        }
    ];
    let detailColumns: DataTableColumns<ImqpNewEnergyTradeConfig> = [
        { title: '制定时间', key: 'createTime', width: 160, align: 'center' },
        {
            title: '申报方向',
            key: 'tradeRole',
            align: 'center',
            render: (row) => (row.tradeRole === '1' ? '增持' : row.tradeRole === '2' ? '减持' : '')
        },
        {
            title: '申报电量',
            key: 'power',
            align: 'center',
            render: (row) => row.power
        },
        {
            title: '增持电量',
            key: 'power',
            align: 'center',
            render: (row) => row.zcPower
        },
        {
            title: '减持电量',
            key: 'power',
            align: 'center',
            render: (row) => row.jcPower
        },
        {
            title: '已发电量',
            key: 'power',
            align: 'center',
            render: (row) => row.yfPower
        },
        {
            title: '状态',
            key: 'price',
            align: 'center',
            render: (row) => checkStatus(row)
        }
    ];

    const updateTable = throttle(async () => {
        loading.value = true;
        let res = await queryRecordList<any>({
            unitId: props.query.unitId,
            page: paginationReactive,
            type: 1
        });
        if (res.code === 1) {
            paginationReactive.pageCount = Math.ceil(res.data.total / paginationReactive.pageSize);
            paginationReactive.itemCount = res.data.total;
            data.value = res.data.data
                .sort((a: any, b: any) => (a.strategyDate > b.strategyDate ? -1 : 1))
                .map((it: any, index: number) => Object.assign(it, { key: index }));
        }
        loading.value = false;
    }, 300);

    function checkStatus(detail: ImqpNewEnergyTradeConfig) {
        if (detail.status === 1) {
            return <NTag type="success">成功</NTag>;
        }
        if (detail.status === 2) {
            return <NTag type="error">失败</NTag>;
        }
        return <NTag>待执行</NTag>;
    }

    defineExpose({ updateTable });
</script>
<style scoped lang="scss">
    :deep(.opts) {
        display: flex;
        justify-content: space-between;
        user-select: none;
        margin-right: 15px;
        span {
            height: 18px;
            svg {
                height: 18px;
                width: 18px;
            }
        }
    }
</style>
