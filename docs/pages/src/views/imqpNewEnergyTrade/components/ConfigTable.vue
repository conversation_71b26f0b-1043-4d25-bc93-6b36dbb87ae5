<template>
    <n-config-provider :theme-overrides="{ DataTable: { tdPaddingSmall: '3px' } }" abstract>
        <div v-if="fisrt" class="imqp-new-energy-trade-config-page">
            <n-flex :wrap="false" align="center">
                <div>开盘时间:</div>
                <span v-show="!editTime">{{ `${fisrt.startTime ?? '暂无'} ~ ${fisrt.endTime ?? '暂无'}` }}</span>
            </n-flex>
            <n-flex :wrap="false" align="center" justify="space-between">
                <n-flex>
                    <n-flex :wrap="false" align="center">
                        <div>上午开盘时间:</div>
                        <span v-show="!editTime">{{ `${fisrt.startTimeAm ?? fisrt.startTime ?? '暂无'} ~ ${fisrt.endTimeAm ?? '暂无'}` }}</span>
                    </n-flex>
                    <n-flex :wrap="false" align="center">
                        <div>下午开盘时间:</div>
                        <span v-show="!editTime">{{ `${fisrt.startTimePm ?? '暂无'} ~ ${fisrt.endTimePm ?? fisrt.endTime ?? '暂无'}` }}</span>
                    </n-flex>
                </n-flex>
            </n-flex>
            <n-data-table :columns="columns" :data="data" :loading="loading" :max-height="props.height - 38" size="small" virtual-scroll />
            <n-row justify-content="center">
                <n-button :disabled="data.length === 0" :loading="saveLoading" size="small" type="primary" @click="saveConfig(false)">确认</n-button>
            </n-row>
        </div>
        <n-card v-else>
            <n-empty description="暂无数据" size="large" style="width: 600px"></n-empty>
        </n-card>
    </n-config-provider>
</template>
<script lang="tsx" setup>
    import { Ref } from 'vue';
    import { DataTableColumns, NButton, NInputNumber, NRadio, NRadioGroup } from 'naive-ui';
    import { ImqpNewEnergyTradeConfig, Query } from '../types';
    import { queryConfigList, saveConfigList } from '@/api/imqpNewEnergyTrade';

    const props = defineProps<{ query: Query; declareDate: string; height: number }>();
    const emit = defineEmits(['save']);

    const editTime: Ref<boolean> = ref(false);

    const loading: Ref<boolean> = ref(false);
    const data: Ref<ImqpNewEnergyTradeConfig[]> = ref([]);
    const fisrt: Ref<ImqpNewEnergyTradeConfig | null> = ref(null);
    const message = useMessage();

    onMounted(() => {
        updateConfig();
    });

    async function updateConfig() {
        loading.value = true;
        data.value = [];
        try {
            let res = await queryConfigList<ImqpNewEnergyTradeConfig[]>({
                unitId: props.query.unitId,
                strategyDate: props.query.strategyDate
            });
            if (res.code === 1) {
                data.value = res.data;
                fisrt.value = data.value[0] ?? null;
            }
        } finally {
            loading.value = false;
        }
    }

    const saveLoading: Ref<boolean> = ref(false);

    async function saveConfig(onlyUpdateTime: boolean) {
        saveLoading.value = true;
        try {
            const resp = await saveConfigList({
                strategyDate: props.query.strategyDate,
                declareDate: props.declareDate,
                unitId: props.query.unitId,
                type: 1,
                onlyUpdateTime,
                configList: data.value.filter((it) => it.tradeRole !== undefined && it.power !== undefined)
            });
            if (resp.code === 1) {
                message.success('保存成功');
                saveLoading.value = false;
                emit('save');
            }
        } finally {
            saveLoading.value = false;
        }
    }

    const columns: DataTableColumns<ImqpNewEnergyTradeConfig> = [
        {
            title: '申报时间',
            key: 'declareDate',
            align: 'center',
            render: (row) => row.declareDate
        },
        {
            title: '申报方向',
            key: 'tradeRole',
            align: 'center',
            render: (row) => (
                <NRadioGroup v-model:value={row.tradeRole} name="tradeRole">
                    <NRadio value={'1'}>增持</NRadio>
                    <NRadio value={'2'}>减持</NRadio>
                </NRadioGroup>
            )
        },
        {
            title: '已发电量(MWh)',
            key: 'yfPower',
            align: 'center'
        },
        {
            title: '挂牌电量(MWh)',
            key: 'power',
            align: 'center',
            render: (row, index) => (
                <NInputNumber
                    value={row.power}
                    showButton={false}
                    min={0}
                    max={row.tradeRole === '1' ? row.zcPower : row.tradeRole === '2' ? row.jcPower : 0}
                    precision={0}
                    placeholder={
                        '' +
                        (row.zcPower === undefined && row.jcPower === undefined
                            ? '没有限额数据'
                            : row.tradeRole === '1'
                              ? row.zcPower ?? '-'
                              : row.tradeRole === '2'
                                ? row.jcPower ?? '-'
                                : (row.zcPower ?? '-') + ' / ' + (row.jcPower ?? '-'))
                    }
                    onUpdate:value={(v: number | null) => {
                        if (v != null) {
                            data.value[index].power = v;
                        }
                    }}
                ></NInputNumber>
            )
        }
    ];

    defineExpose({ updateConfig });
</script>
<style lang="scss" scoped>
    .imqp-new-energy-trade-config-page {
        flex: 0 0 600px;
        display: flex;
        flex-direction: column;
        row-gap: 10px;
        :deep(.n-data-table .n-data-table-td.n-data-table-td--summary) {
            background-color: var(--n-merged-td-color);
            padding-top: 10px;
            padding-bottom: 10px;
        }
        :deep(.n-data-table-tr--summary td) {
            position: sticky;
            bottom: 0;
        }
    }
</style>
