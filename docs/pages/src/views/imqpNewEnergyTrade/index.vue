<template>
    <div class="imqp-new-energy-trade-page">
        <n-card class="header" size="small">
            <n-form ref="formRef" :model="query" :show-feedback="false" inline label-placement="left" size="small">
                <n-form-item label="省份" path="provinceId">
                    <n-select v-model:value="query.provinceId" :options="provinceList" style="width: 100px" />
                </n-form-item>
                <n-form-item label="交易单元" path="unitId">
                    <n-select v-model:value="query.unitId" :options="unitList" style="width: 200px"></n-select>
                </n-form-item>
                <n-form-item label="策略时间" path="strategyDate">
                    <n-date-picker
                        v-model:formatted-value="query.strategyDate"
                        :close-on-select="true"
                        input-readonly
                        style="width: 140px"
                        type="month"
                        update-value-on-close
                        value-format="yyyy-MM"
                    />
                </n-form-item>
                <n-form-item>
                    <n-flex>
                        <n-button size="small" type="primary" @click="updateData">查询</n-button>
                        <!--                        <n-button size="small" type="primary" @click="exportRecord(false)">导出</n-button>-->
                        <!--                        <n-button size="small" type="primary" @click="exportRecord(true)">一键导出</n-button>-->
                    </n-flex>
                </n-form-item>
                <n-form-item>
                    <n-button :loading="callDetectJobStatus" size="small" type="warning" @click="callDetectJob">更新 </n-button>
                </n-form-item>
            </n-form>
        </n-card>
        <n-card class="main" size="small">
            <div class="title">江西电力市场{{ dayjs(query.strategyDate).format('YYYY年M月') }}保量保价新能源合同转让交易</div>
            <div class="wrapper">
                <div ref="configWrapperRef" class="config-table">
                    <config-table ref="configRef" :declare-date="''" :height="configHeight" :query="query" @save="updateData" />
                </div>
                <div class="record-table">
                    <record-table ref="recordTableRef" :height="configHeight" :query="query" />
                </div>
            </div>
        </n-card>
    </div>
</template>
<script lang="ts" setup>
    import { onMounted, ref, Ref } from 'vue';
    import dayjs from 'dayjs';
    import { queryUnitList } from '@/api/unit';
    import { callDetectJobByUnitId, exportAllRecordList, exportRecordList } from '@/api/imqpNewEnergyTrade';
    import { RoleType, UnitConfig } from '@/types';
    import { Query } from './types';
    import ConfigTable from './components/ConfigTable.vue';
    import RecordTable from './components/RecordTable.vue';
    import { useCommonStore } from '@/stores/common';
    import { useProvinceStore } from '@/stores/province';

    const dateFormat = 'YYYY-MM';

    const commonStore = useCommonStore();
    const provinceStore = useProvinceStore();
    const provinceList = ref(provinceStore.getProvinceList());

    const unitList: Ref<any[]> = ref([]);
    const query: Ref<Query> = ref({
        provinceId: provinceList.value[0].value,
        unitId: '',
        strategyDate: dayjs().format(dateFormat)
    });

    const message = useMessage();

    async function exportRecord(exportAll: boolean) {
        const res = exportAll
            ? await exportAllRecordList<{ [key: string]: any[] }>({ type: 1 })
            : await exportRecordList<{ [key: string]: any[] }>({
                  unitId: query.value.unitId,
                  strategyDate: query.value.strategyDate,
                  type: 1
              });
    }

    const configRef = ref();
    const recordTableRef = ref();

    async function updateData() {
        configRef.value.updateConfig();
        recordTableRef.value.updateTable();
    }

    const configWrapperRef = ref();
    const configHeight = ref(300);
    commonStore.$subscribe(() => {
        if (configWrapperRef.value) {
            configHeight.value = window.innerHeight - Math.ceil(configWrapperRef.value.getBoundingClientRect().top) - 105;
            console.log(configHeight.value);
        }
    });

    onMounted(async () => {
        let res = await queryUnitList<UnitConfig[]>(RoleType.IMQP_NEW_ENERGY_TRADE);
        if (res.code !== 1) {
            message.error(res.msg);
        }
        if (res.code === 1 && res.data.length > 0) {
            unitList.value = res.data.map((it: any) => Object.assign({}, { label: it.uName, value: it.unitId }));
            query.value.unitId = unitList.value[0].value;
            await updateData();
        }
    });

    const callDetectJobStatus = ref(false);

    async function callDetectJob() {
        callDetectJobStatus.value = true;
        try {
            const res = await callDetectJobByUnitId<null>({
                unitId: query.value.unitId,
                strategyDate: query.value.strategyDate
            });
            if (res.code === 1) {
                message.success('刷新完成');
                await updateData();
            } else {
                message.error('任务出错: ' + res.msg);
            }
        } catch (e) {
            message.error('任务出错: ' + (e as Error)?.message);
        } finally {
            callDetectJobStatus.value = false;
        }
    }
</script>
<style lang="scss" scoped>
    .imqp-new-energy-trade-page {
        display: flex;
        height: 100%;
        flex-direction: column;
        padding: 10px;
        row-gap: 10px;
        .header {
            flex: 0 0 20px;
            .n-form.n-form--inline {
                flex-wrap: wrap;
                gap: 10px 0;
            }
        }
        .main {
            flex: 1;
            overflow: auto;
            --title-height: 50px;
            .title {
                text-align: center;
                color: red;
                font-size: 22px;
                font-family: serif;
                height: var(--title-height);
                padding-bottom: 20px;
            }
            .wrapper {
                height: calc(100% - var(--title-height));
                display: flex;
                justify-content: space-between;
                column-gap: 10px;
                .config-table {
                    flex: 0 1 600px;
                }
                .record-table {
                    flex: 0 1 400px;
                }
            }
        }
    }
</style>
