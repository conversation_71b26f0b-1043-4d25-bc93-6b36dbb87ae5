export class WebSocketClient {
    private readonly url: string;
    private readonly monitorTask: number | null = null;
    private socket: WebSocket | null = null;
    private messageFn: ((data: string) => void) | null = null;

    constructor(url: string) {
        this.url = url;
        this.monitorTask = setInterval(() => this.init(), 3000);
    }

    init() {
        if (this.socket === null) {
            this.socket = new WebSocket(this.url);
            this.socket.addEventListener('open', (event: Event) => {
                // console.log('open', event);
            });
            this.socket.addEventListener('close', (event: Event) => {
                this.socket = null;
                // console.log('close', event);
            });
            this.socket.addEventListener('error', (event: Event) => {
                this.socket = null;
                // console.log('error', event);
            });
            this.socket.addEventListener('message', (event: MessageEvent) => {
                // console.log('message', event);
                if (this.messageFn !== null) {
                    this.messageFn(event.data);
                }
            });
        }
        return this;
    }

    onMessage(fn: (data: string) => void) {
        this.messageFn = fn;
        return this;
    }

    send(data: string | ArrayBuffer | Blob | ArrayBufferView) {
        if (this.socket !== null && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(data);
        } else {
            console.warn('ws not ready');
        }
    }

    close() {
        this.socket?.close();
        if (this.monitorTask !== null) {
            clearInterval(this.monitorTask);
        }
    }
}
