export class SseClient {
    private readonly url: string;
    private readonly onMessage: (event: MessageEvent<any>) => void;
    private eventSource?: EventSource;

    constructor(url: string, onMessage: (event: MessageEvent<any>) => void) {
        this.url = url;
        this.onMessage = onMessage;
        this.init();
    }

    init() {
        this.eventSource = new EventSource(this.url);
        this.eventSource.addEventListener('default', (event) => {
            this.onMessage(event);
        });
        this.eventSource.addEventListener('error', (e) => {
            this.close();
            setTimeout(() => this.init(), 300);
        });
    }

    close() {
        this.eventSource?.close();
    }
}
