import { createRouter, createWeb<PERSON>ashHistory, RouteRecordRaw } from 'vue-router';
import menuComponent from '@/views/common/menu/index.vue';
import { NIcon } from 'naive-ui';
import { BusinessTime, CalendarAlt, CalendarDay, ListOl, Mendeley, Tools } from '@vicons/fa';
import { MenuType, RoleType } from '@/types';

const showRoutes: RouteRecordRaw[] = [
    {
        path: '/op',
        name: '省间现货',
        meta: { type: MenuType.INDEX, order: 1, hasPermission: true, icon: h(NIcon, null, { default: () => h(BusinessTime) }) },
        component: menuComponent,
        children: [
            {
                path: '/opid',
                name: '省间现货日内交易自动申报',
                meta: { type: MenuType.MENU, order: 4, hasPermission: false, roleType: RoleType.OPID },
                component: () => import('@/views/onProvinceInDay/index.vue')
            },
            {
                path: '/opbd',
                name: '省间现货日前交易自动申报',
                meta: { type: MenuType.MENU, order: 3, hasPermission: false, roleType: RoleType.OPBD },
                component: () => import('@/views/onProvinceBeforeDay/index.vue')
            },
            {
                path: '/profitAnalysis',
                name: '省间收益分析',
                meta: { type: MenuType.MENU, order: 4, hasPermission: false, roleType: RoleType.PROFIT_ANALYSIS },
                component: () => import('@/views/profitAnalysis/index.vue')
            }
        ]
    },
    {
        path: '/dr',
        name: '日滚动',
        meta: { type: MenuType.INDEX, order: 2, hasPermission: true, icon: h(NIcon, null, { default: () => h(ListOl) }) },
        component: menuComponent,
        children: [
            {
                path: '/dayRolling',
                name: '日滚动自动抢报',
                meta: { type: MenuType.MENU, order: 1, hasPermission: false, roleType: RoleType.DAY_ROLLING },
                component: () => import('@/views/dayRolling/index.vue')
            },
            {
                path: '/dayRollingMonitor',
                name: '日滚动自动盯盘',
                meta: { type: MenuType.MENU, order: 1, hasPermission: false, roleType: RoleType.DAY_ROLLING },
                component: () => import('@/views/dayRollingMonitor/index.vue')
            },
            {
                path: '/dayRollingInfo',
                name: '日滚动交易记录',
                meta: { type: MenuType.MENU, order: 1, hasPermission: false, roleType: RoleType.DAY_ROLLING },
                component: () => import('@/views/dayRollingInfo/index.vue')
            }
        ]
    },
    {
        path: '/d-2',
        name: 'D-2交易',
        meta: { type: MenuType.INDEX, order: 3, hasPermission: true, icon: h(NIcon, null, { default: () => h(CalendarDay) }) },
        component: menuComponent,
        children: [
            {
                path: '/integrationTrade',
                name: 'D-2日融合交易',
                meta: { type: MenuType.MENU, order: 1, hasPermission: false, roleType: RoleType.INTEGRATION_TRADE },
                component: () => import('@/views/logs/index.vue')
            },
            {
                path: '/newEnergyTrade',
                name: 'D-2日新能源交易',
                meta: { type: MenuType.MENU, order: 1, hasPermission: false, roleType: RoleType.NEW_ENERGY_TRADE },
                component: () => import('@/views/logs/index.vue')
            }
        ]
    },
    {
        path: '/qp',
        name: '保量保价新能源交易',
        meta: { type: MenuType.INDEX, order: 4, hasPermission: true, icon: h(NIcon, null, { default: () => h(CalendarAlt) }) },
        component: menuComponent,
        children: [
            {
                path: '/imqpNewEnergyTrade',
                name: '月内保量保价新能源交易',
                meta: { type: MenuType.MENU, order: 1, hasPermission: false, roleType: RoleType.IMQP_NEW_ENERGY_TRADE },
                component: () => import('@/views/imqpNewEnergyTrade/index.vue')
            }
        ]
    },
    {
        path: '/pp',
        name: '代购电',
        meta: { type: MenuType.INDEX, order: 5, hasPermission: true, icon: h(NIcon, null, { default: () => h(Mendeley) }) },
        component: menuComponent,
        children: [
            {
                path: '/proxyPurchase',
                name: '电网代购电',
                meta: { type: MenuType.MENU, order: 1, hasPermission: false, roleType: RoleType.PROXY_PURCHASE },
                component: () => import('@/views/proxyPurchase/index.vue')
            }
        ]
    },
    {
        path: '/config',
        name: '系统工具',
        meta: { type: MenuType.INDEX, order: 99, hasPermission: true, icon: h(NIcon, null, { default: () => h(Tools) }) },
        component: menuComponent,
        children: [
            {
                path: '/autoLogin',
                name: '自动登录',
                meta: { type: MenuType.MENU, order: 2, hasPermission: false, roleType: RoleType.AUTO_LOGIN },
                component: () => import('@/views/autoLogin/index.vue')
            },
            {
                path: '/logs',
                name: '日志',
                meta: { type: MenuType.MENU, order: 4, hasPermission: false, roleType: RoleType.LOGS },
                component: () => import('@/views/logs/index.vue')
            }
        ]
    }
];

const systemRoutes: RouteRecordRaw[] = [
    {
        path: '/system',
        meta: { type: MenuType.INDEX, order: Number.MAX_VALUE, hasPermission: true },
        component: menuComponent,
        children: []
    }
];
const baseRoutes: RouteRecordRaw[] = [
    {
        path: '/loading',
        meta: { type: MenuType.MENU, order: Number.MAX_VALUE, hasPermission: true },
        component: () => import('@/views/Loading.vue')
    },
    {
        path: '/nop',
        name: 'nop',
        component: () => import('@/views/NoPermissionFound.vue'),
        meta: { type: MenuType.MENU, order: Number.MAX_VALUE, hasPermission: true }
    },
    {
        path: '/:pathMatch(.*)*',
        meta: { type: MenuType.MENU, order: Number.MAX_VALUE, hasPermission: true },
        redirect: '/loading'
    }
];

const router = createRouter({
    history: createWebHashHistory(),
    routes: baseRoutes
});
export default router;
export { baseRoutes, showRoutes, systemRoutes };
