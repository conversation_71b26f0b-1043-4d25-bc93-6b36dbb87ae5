import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import path from 'path';

import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers';

export default defineConfig(({ command }) => {
    let same = {
        base: './',
        envDir: './.env',
        plugins: [
            vue(),
            vueJsx({}),
            AutoImport({
                imports: [
                    'vue',
                    'vue-router',
                    {
                        'naive-ui': ['useDialog', 'useMessage', 'useNotification', 'useLoadingBar']
                    }
                ],
                resolvers: [NaiveUiResolver()]
            }),
            Components({
                resolvers: [NaiveUiResolver()],
                dirs: ['src/views/common/components']
            })
        ],
        resolve: {
            extensions: ['.js', '.vue', '.ts', '.json'],
            alias: {
                '@': path.resolve(__dirname, 'src')
            }
        }
    };
    let diff;
    if (command === 'build') {
        diff = {
            build: {
                outDir: '../../src/main/resources/static',
                assetsDir: 'assets',
                emptyOutDir: true,
                chunkSizeWarningLimit: 1000
            }
        };
    } else {
        diff = {
            server: {
                host: '0.0.0.0',
                port: 33000,
                proxy: {
                    '/api': {
                        target: 'http://localhost:8081/',
                        changeOrigin: true,
                        rewrite: (path) => {
                            return path.replace(/^\/api/, '');
                        }
                    }
                }
            }
        };
    }
    return Object.assign(same, diff);
});
