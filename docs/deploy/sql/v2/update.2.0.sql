alter table system_account_config
    add proxy varchar(255) comment '代理配置';

create table user_sd_spot_config
(
    account      varchar(255) not null comment '交易单元 ID',
    strategyDate varchar(20)  not null comment '策略时间',
    time         varchar(20)  not null comment '时间（H:mm）',
    strategyType int comment '申报类型 {1: , 2: , 3: 日内, 4: 日前}',
    reportType   int default 1 comment '申报类型 {1: 手动, 2: 自动}',
    power        double comment '申报电量（用户输入或自动策略系统输出）',
    reportPower  double comment '实际申报电量（限制三位小数）',
    status       int default 0 comment '状态 {0: 未申报, 1: 成功, 2: 失败}',
    createTime   varchar(50) comment '创建时间',
    updateTime   varchar(50) comment '更新时间',
    primary key (account, strategyDate, time)
) comment '山东现货申报配置';

-- tmp

create table province_profiles
(
    profileId   varchar(255) comment '实例ID (template: 模板配置)',
    profileName varchar(255) not null comment '实例名称',
    isEnabled   boolean      not null default true,
    primary key (profileId)
) comment '省份表信息表';

insert into province_profiles(profileId, profileName)
values ('template_all', '全局默认配置'),
       ('template_sx', '山西配置'),
       ('template_sd', '山东配置')
;

create table profile_configs
(
    profileId   varchar(255) not null comment '关联profileId',
    configKey   varchar(255) not null comment '配置项的键',
    configValue text comment '配置项的值',
    description varchar(255) comment '配置项的描述',
    primary key (profileId, configKey)
) comment '配置表';
create index idx_profile_configs_key on profile_configs (profileId, configKey);

insert into profile_configs(profileId, configKey, configValue, description)
values ('template_all', 'httpWaitMilliSecond', '2000', '每次请求等待间隔（毫秒，正整数）'),
       ('template_sx', 'queryLimit', 'true', '省间申报查询限额 {true: 查询限额, false: 使用装机容量}'),
       ('template_sx', 'pmos.domain', 'pmos.sx.sgcc.com.cn', '域名'),
       ('template_sx', 'pmos.origin', 'https://pmos.sx.sgcc.com.cn', '访问地址'),
       ('template_sd', 'queryLimit', 'true', '省间申报查询限额 {true: 查询限额, false: 使用装机容量}')
;