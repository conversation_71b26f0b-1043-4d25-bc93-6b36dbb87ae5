drop table if exists data_profit_analysis;
create table data_profit_analysis
(
    unitId               varchar(255) not null comment '交易单元',
    date                 varchar(20)  not null comment '策略时间; eg: 2024-01-01',
    ipDayAheadPrice      text comment '省内日前电价',
    ipRealTimePrice      text comment '省内实时电价',
    opbdReportPower      text comment '省间日前申报出力',
    opbdBidPower         text comment '省间日前中标出力',
    opbdDayAheadBidPrice text comment '省间日前日前中标电价',
    opidReportPower      text comment '省间日内申报出力',
    opidBidPower         text comment '省间日内中标出力',
    opidRealTimeBidPrice text comment '省间日内实时中标电价',
    createTime           varchar(50) comment '创建时间',
    primary key (unitId, date)
) comment ='山西策略收益分析数据';

drop table if exists system_account_config;
create table system_account_config
(
    plantId      varchar(255) not null comment '交易中心场站 ID',
    plantName    varchar(255) comment '交易中心场站名称',
    uKeyCode     text comment 'UKey 内容',
    userName     varchar(255) comment '账号',
    password     varchar(255) comment '密码',
    uKeyPrivate  text comment 'UKey Private',
    uKeyPassword varchar(255) comment 'UKey Password',
    grayTag      varchar(255) comment '账号 grayTag',
    phone        varchar(20) comment '手机号',
    useUKey      int default 1 comment '使用UKey登录 {0: 不使用, 1: 使用}',
    useSMS       int default 0 comment '使用短信登录 {0: 不使用, 1: 使用}',
    smsRegex     varchar(255) comment '短信验证码提取正则',
    primary key (plantId)
) comment ='交易中心账号配置';

drop table if exists system_opid_time_config;
create table system_opid_time_config
(
    tradeTimePart bigint not null comment '交易时段: [1,12]',
    orderNumber   varchar(10) comment '时段编号: 0001 - 0012',
    startPart     varchar(10) comment '开始时间',
    endPart       varchar(10) comment '结束时间',
    bidStartTime  varchar(10) comment '申报开始时间',
    bidEndTime    varchar(10) comment '申报结束时间',
    primary key (tradeTimePart)
) comment ='省间日内交易时间配置';

drop table if exists system_unit_config;
create table system_unit_config
(
    unitId         varchar(255) not null comment '交易单元 ID',
    unitName       varchar(255) comment '交易单元名称',
    plantId        varchar(255) comment '交易中心场站 ID',
    grayTag        varchar(255) comment '交易单元 grayTag',
    dispatchId     varchar(255) comment '交易单元调度 ID',
    dispatchName   varchar(255) comment '交易单元调度名称',
    limitPower     double comment '场站装机',
    uName          varchar(255) comment '交易单元简称',
    unitIdIlt      varchar(255) comment '中长期交易单元 ID',
    unitNameIlt    varchar(255) comment '中长期交易单元名称',
    generatorSetId varchar(255) comment '机组 ID',
    type           int comment '类型 {0: 风电, 1: 光电}',
    primary key (unitId)
) comment ='交易中心交易单元信息配置';

drop table if exists system_unit_config_proxy_purchase;
create table system_unit_config_proxy_purchase
(
    unitId   varchar(255) not null comment '交易单元 ID',
    unitName varchar(255) comment '交易单元名称',
    plantId  varchar(255) comment '账号 ID',
    primary key (unitId)
) comment ='山西电网代购电交易单元配置';

drop table if exists user_day_rolling_config_detail;
create table user_day_rolling_config_detail
(
    unitId          varchar(255)  not null comment '交易单元',
    strategyDate    varchar(50)   not null comment '策略时间; eg: [2024-01-01]',
    declareDate     varchar(50)   not null comment '申报时间; eg: [2024-01-02, 2024-01-03, 2024-01-04]',
    timeCode        int           not null comment '时间段: [1, 24]',
    type            int default 0 not null comment '类型: {0: 未配置占位符, 1: 抢报, 2: 盯盘}',
    tradeSeqId      varchar(255) comment '交易 ID; eg: "PHBSX2023123142003832"',
    tradeRole       varchar(10) comment '交易方向: {1: 买入, 2: 卖出}',
    tradeRoleLock   varchar(10) comment '交易方向锁定: {1: 买入, 2: 卖出} ~~> [tradeDirection]',
    power           double comment '用户配置电量',
    price           double comment '用户配置电价',
    reportPower     double comment '申报电量',
    reportPrice     double comment '申报电价',
    buyEnergyLimit  double comment '买入限制',
    sellEnergyLimit double comment '卖出限制',
    priceLowerLimit double comment '电价下限',
    priceUpperLimit double comment '电价上限',
    status          int default 0 comment '状态: {0: 未申报, 1: 成功, 2: 失败}',
    createTime      varchar(50) comment '创建时间',
    updateTime      varchar(50) comment '更新时间',
    primary key (unitId, strategyDate, declareDate, timeCode, type)
) comment ='日滚动配置';

drop table if exists user_day_rolling_config_detail_log;
create table user_day_rolling_config_detail_log
(
    unitId       varchar(255)  not null comment '交易单元',
    strategyDate varchar(50)   not null comment '策略时间; eg: [2024-01-01]',
    declareDate  varchar(50)   not null comment '申报时间; eg: [2024-01-02, 2024-01-03, 2024-01-04]',
    timeCode     int           not null comment '时间段: [1, 24]',
    type         int default 0 not null comment '类型: {0: 未配置占位符, 1: 抢报, 2: 盯盘}',
    createTime   varchar(50)   not null comment '创建时间',
    tradeSeqId   varchar(255) comment '交易 ID; eg: "PHBSX2023123142003832"',
    tradeRole    varchar(10) comment '交易方向: {1: 买入, 2: 卖出}',
    power        double comment '用户配置电量',
    price        double comment '用户配置电价',
    reportPower  double comment '申报电量',
    reportPrice  double comment '申报电价',
    status       int default 0 comment '状态: {0: 未申报, 1: 成功, 2: 失败}',
    primary key (unitId, strategyDate, declareDate, timeCode, type, createTime)
) comment ='日滚动配置日志';

drop table if exists user_day_rolling_time_info;
create table user_day_rolling_time_info
(
    strategyDate   varchar(50)  not null comment '策略时间; eg: [2023-12-27]',
    declareDate    varchar(50)  not null comment '申报时间; eg: [2023-12-29, 2023-12-30, 2023-12-31]',
    tradeSeqId     varchar(255) not null comment '交易序列; eg: "PHBSX2023123142003832"',
    beginDate      varchar(50) comment '标的开始日期; eg: 2023-12-31T00:00:00.000+0800',
    endDate        varchar(50) comment '标的结束日期; eg: 2023-12-31T00:00:00.000+0800',
    bidDate        varchar(50) comment '策略日期; eg: 2023-12-27T00:00:00.000+0800',
    startTime      varchar(50) comment '开始时间; eg: 2023-12-27T10:00:00.000+0800',
    endTime        varchar(50) comment '结束时间; eg: 2023-12-27T16:00:00.000+0800',
    startTime1     varchar(50) comment '开始时间; eg: 2023-12-27 10:00:00.0',
    endTime1       varchar(50) comment '结束时间; eg: 2023-12-27 12:00:00.0',
    startTime2     varchar(50) comment '开始时间; eg: 2023-12-27 14:00:00.0',
    endTime2       varchar(50) comment '结束时间; eg: 2023-12-27 16:00:00.0',
    tradeBatchId   varchar(255) comment '交易批次; eg: A202312314004339',
    tradeCycle     varchar(10) comment '交易周期: {1: 多月交易, 2: 月度交易, 3: 旬交易, 4: 日滚动交易}',
    tradeStage     varchar(10) comment '交易类型: {1: 集中竞价, 2: 滚动撮合}',
    tradeseqName   varchar(255) comment '交易名称; eg: 2023年12月27日日滚动交易(2023-12-31)',
    tradeseqStatus varchar(50) comment '交易状态',
    createTime     varchar(50) comment '创建时间',
    primary key (strategyDate, declareDate, tradeSeqId)
) comment ='日滚动时间配置';

drop table if exists user_day_rolling_info_market;
create table user_day_rolling_info_market
(
    strategyDate  varchar(50)  not null comment '策略时间; eg: [2024-01-01]',
    tradeseqId    varchar(255) not null comment '交易 ID; eg: "PHBSX2023123142003832"',
    timeCode      int          not null comment '时间段: [1, 24]; 其中：-1 代表信息数据',
    tradeseqName  varchar(255) comment '申报名称; eg: 2024年09月10日日滚动交易(2024-9-13)',
    total         varchar(50) comment '总交易量',
    maxPrice      varchar(50) comment '最高价',
    minPrice      varchar(50) comment '最低价',
    weightedPrice varchar(50) comment '加权价格',
    midPrice      varchar(50) comment '中位数价格',
    createTime    varchar(50) comment '创建时间',
    primary key (strategyDate, tradeseqId, timeCode)
) comment ='日滚动市场信息';

drop table if exists user_day_rolling_info_unit;
create table user_day_rolling_info_unit
(
    unitId       varchar(255) not null comment '交易单元',
    strategyDate varchar(50)  not null comment '策略时间; eg: [2024-01-01]',
    tradeSeqId   varchar(255) not null comment '交易 ID; eg: "PHBSX2023123142003832"',
    timeCode     int          not null comment '时间段: [1, 24]',
    tradeRole    varchar(10) comment '交易方向: {1: 买入, 2: 卖出}',
    power        varchar(50) comment '用户配置电量',
    price        varchar(50) comment '用户配置电价',
    detail       text comment '详情：[{"bidEnergy": 30}]',
    createTime   varchar(50) comment '创建时间',
    primary key (unitId, strategyDate, tradeSeqId, timeCode)
) comment ='日滚动单元信息';

drop table if exists user_imqp_new_energy_trade_config;
create table user_imqp_new_energy_trade_config
(
    unitId       varchar(255) not null comment '交易单元',
    strategyDate varchar(20)  not null comment '策略时间; eg: 2024-05',
    declareDate  varchar(20)  not null comment '申报时间; eg: 2024-05-25',
    trid         varchar(255) comment '交易 ID; eg: 1200002566',
    jydyid       varchar(255) comment '交易 ID; eg: EB475DC2C5C39FCEE05344DCEA0A8C1F',
    guid         varchar(255) comment '交易 ID; eg: 19807B192DA03471E06343DCEA0AA094',
    startTime    varchar(50) comment '开始时间; eg: 2024-05-21 09:30:00',
    endTime      varchar(50) comment '结束时间; eg: 2024-05-21 16:30:00',
    startTimeAm  varchar(50) default '09:30:00' comment '开始时间1; eg: 09:30:00',
    endTimeAm    varchar(50) default '11:30:00' comment '结束时间1; eg: 11:30:00',
    startTimePm  varchar(50) default '14:30:00' comment '开始时间2; eg: 14:30:00',
    endTimePm    varchar(50) default '16:30:00' comment '结束时间2; eg: 16:30:00',
    zcPower      double comment '增持可挂牌剩余电量',
    jcPower      double comment '减持可挂牌剩余电量',
    yfPower      double comment '已发电量',
    tradeRole    varchar(10) comment '交易方向: {1: 增持, 2: 减持}',
    power        double comment '用户配置挂牌电量',
    status       int  default 0 comment '状态: {0: 未申报, 1: 成功, 2: 失败}',
    logId        varchar(255) comment '最新 log 的 ID',
    createTime   varchar(50) comment '创建时间',
    updateTime   varchar(50) comment '更新时间',
    primary key (unitId, strategyDate, declareDate)
) comment ='江西中长期 月内保量保价新能源交易 用户配置';

drop table if exists user_imqp_new_energy_trade_config_log;
create table user_imqp_new_energy_trade_config_log
(
    logId        varchar(255) not null comment 'ID',
    unitId       varchar(255) not null comment '交易单元',
    strategyDate varchar(20)  not null comment '策略时间; eg: 2024-05',
    declareDate  varchar(20)  not null comment '申报时间; eg: 2024-05-25',
    zcPower      double comment '增持可挂牌剩余电量',
    jcPower      double comment '减持可挂牌剩余电量',
    yfPower      double comment '已发电量',
    tradeRole    varchar(10) comment '交易方向: {1: 增持, 2: 减持}',
    power        double comment '用户配置挂牌电量',
    status       int default 0 comment '状态: {0: 未申报, 1: 成功, 2: 失败}',
    createTime   varchar(50) comment '创建时间',
    updateTime   varchar(50) comment '更新时间',
    primary key (logId)
) comment ='江西中长期 月内保量保价新能源交易 用户配置记录';

drop table if exists user_opbd_config;
create table user_opbd_config
(
    unitId       varchar(255) not null comment '交易单元 ID',
    strategyDate varchar(20)  not null comment '策略时间',
    strategyType int comment '申报类型 {1: , 2: , 3: , 4: }',
    unitName     varchar(255) comment '交易单元名称',
    createTime   varchar(50) comment '创建时间',
    primary key (unitId, strategyDate)
) comment ='省间日前配置';

create index user_opbd_config_strategy_time_index on user_opbd_config (strategyDate);

drop table if exists user_opbd_config_detail;
create table user_opbd_config_detail
(
    unitId       varchar(255) not null comment '交易单元 ID',
    strategyDate varchar(20)  not null comment '策略时间',
    startTime    varchar(20)  not null comment '开始时间',
    endTime      varchar(20)  not null comment '结束时间',
    strategyType int comment '申报类型 {1: , 2: , 3: 日内, 4: 日前}',
    percent      double comment '用户配置电量比例',
    price        int comment '用户配置电价',
    reportPower  int comment '实际申报电量',
    reportPrice  int comment '实际申报电价',
    powerLimit   double comment '限额',
    status       int default 0 comment '状态 {0: 未申报, 1: 成功, 2: 失败}',
    timePart     int comment '交易时段: [1,12]',
    dispatchId   varchar(255) comment '交易单元调度 ID',
    createTime   varchar(50) comment '创建时间',
    updateTime   varchar(50) comment '更新时间',
    primary key (unitId, strategyDate, startTime, endTime)
) comment ='省间日前配置详情';

create index user_opbd_config_detail_strategy_time_index on user_opbd_config_detail (strategyDate);

drop table if exists user_opid_config;
create table user_opid_config
(
    unitId       varchar(255) not null comment '交易单元 ID',
    strategyDate varchar(20)  not null comment '策略时间',
    strategyType int comment '申报类型 {1: , 2: , 3: 日内, 4: 日前}',
    reportType   int default 1 comment '申报类型 {1: 手动, 2: 自动}',
    unitName     varchar(255) comment '交易单元名称',
    createTime   varchar(50) comment '创建时间',
    primary key (unitId, strategyDate)
) comment ='省间日内配置';

drop table if exists user_opid_config_detail;
create table user_opid_config_detail
(
    unitId       varchar(255) not null comment '交易单元 ID',
    strategyDate varchar(20)  not null comment '策略时间',
    startTime    varchar(20)  not null comment '开始时间',
    endTime      varchar(20)  not null comment '结束时间',
    strategyType int comment '申报类型 {1: , 2: , 3: 日内, 4: 日前}',
    reportType   int default 1 comment '申报类型 {1: 手动, 2: 自动}',
    percent      double comment '用户配置电量比例',
    price        double comment '用户配置电价',
    reportPower  int comment '实际申报电量',
    reportPrice  int comment '实际申报电价',
    powerLimit   double comment '限额',
    status       int default 0 comment '状态 {0: 未申报, 1: 成功, 2: 失败}',
    timePart     int comment '交易时段: [1,12]',
    dispatchId   varchar(255) comment '交易单元调度 ID',
    createTime   varchar(50) comment '创建时间',
    updateTime   varchar(50) comment '更新时间',
    primary key (unitId, strategyDate, startTime, endTime)
) comment ='省间日内配置详情';

drop table if exists user_proxy_purchase_config;
create table user_proxy_purchase_config
(
    unitId       varchar(255) not null comment '交易单元',
    strategyDate varchar(20)  not null comment '策略时间; eg: 2024-05',
    unitName     varchar(255) comment '交易单元名称',
    tradeseqId   varchar(255) comment '交易 ID; eg: PHBSX20240517A0001',
    tradeseqName varchar(255) comment '交易名称; eg: 2024年6月月度电网代理购电挂牌电力直接交易',
    listedId     varchar(255) comment '交易 ID; eg: bb23e46d9bb14e7db8b58522a4ac4aa9',
    startTime    varchar(50) comment '开始时间; eg: 2024-05-23 09:30:00',
    endTime      varchar(50) comment '结束时间; eg: 2024-05-23 11:30:00',
    price        double comment '摘牌电价',
    power        double comment '用户配置摘牌电量',
    priceLimit   double comment '用户配置摘牌电价最低值',
    status       int default 0 comment '状态: {0: 未申报, 1: 成功, 2: 失败, 3: 电价过低}',
    retryCount   int comment '重试次数',
    logId        varchar(255) comment '最新 log 的 ID',
    createTime   varchar(50) comment '创建时间',
    updateTime   varchar(50) comment '更新时间',
    primary key (unitId, strategyDate)
) comment ='山西电网代购电 用户配置';

drop table if exists user_proxy_purchase_config_log;
create table user_proxy_purchase_config_log
(
    logId        varchar(255) not null comment 'ID',
    unitId       varchar(255) not null comment '交易单元',
    strategyDate varchar(20)  not null comment '策略时间; eg: 2024-05',
    price        double comment '摘牌电价',
    power        double comment '用户配置摘牌电量',
    priceLimit   double comment '用户配置摘牌电价最低值',
    status       int default 0 comment '状态: {0: 未申报, 1: 成功, 2: 失败, 3: 电价过低}',
    retryCount   int comment '重试次数',
    createTime   varchar(50) comment '创建时间',
    updateTime   varchar(50) comment '更新时间',
    primary key (logId)
) comment ='山西电网代购电 用户配置记录';

create index user_proxy_purchase_config_log_idx on user_proxy_purchase_config_log (unitId);

drop table if exists user_config;
create table user_config
(
    `k`        varchar(255) not null,
    `v`        text,
    updateTime varchar(50),
    primary key (`k`)
) comment ='配置表';
/*

INSERT intO system_opid_time_config (tradeTimePart, orderNumber, startPart, endPart, bidStartTime, bidEndTime)
VALUES (1, '0001', '00:15', '02:00', '22:02', '22:08'),
       (2, '0002', '02:15', '04:00', '00:02', '00:08'),
       (3, '0003', '04:15', '06:00', '02:02', '02:08'),
       (4, '0004', '06:15', '08:00', '04:02', '04:08'),
       (5, '0005', '08:15', '10:00', '06:02', '06:08'),
       (6, '0006', '10:15', '12:00', '08:02', '08:08'),
       (7, '0007', '12:15', '14:00', '10:02', '10:08'),
       (8, '0008', '14:15', '16:00', '12:02', '12:08'),
       (9, '0009', '16:15', '18:00', '14:02', '14:08'),
       (10, '0010', '18:15', '20:00', '16:02', '16:08'),
       (11, '0011', '20:15', '22:00', '18:02', '18:08'),
       (12, '0012', '22:15', '24:00', '20:02', '20:08');*/
