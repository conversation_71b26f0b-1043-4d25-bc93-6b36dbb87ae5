-- update on 2024-01-11 17:03:06
drop table power_limit;
drop table spot_price;
alter table user_strategy_config_info rename to user_opid_config;
alter table user_strategy_time_config_info rename to user_opid_config_detail;
alter table plant_config_info rename to system_account_config;
alter table unit_config_info rename to system_unit_config;
alter table trade_time_config_info rename to system_opid_time_config;

alter table system_unit_config add unitIdIlt text; -- 中长期交易单元 ID
alter table system_unit_config add unitNameIlt text; -- 中长期交易单元名称

create table system_account_config_dg_tmp
(
    plantId      text not null primary key,
    plantName    text,
    uKeyCode     text,
    type         integer,
    userName     text,
    password     text,
    uKeyPrivate  text,
    uKeyPassword text,
    grayTag      text,
    endTime      text
);
insert into system_account_config_dg_tmp(plantId, plantName, uKeyCode, type, userName, password, uKeyPrivate,
                                         uKeyPassword, grayTag, endTime)
select plantId,
       plantName,
       uKeyCode,
       type,
       userName,
       password,
       uKeyPrivate,
       uKeyPassowrd,
       grayTag,
       endTime
from system_account_config;
drop table system_account_config;
alter table system_account_config_dg_tmp rename to system_account_config;

create table system_opid_time_config_dg_tmp
(
    tradeTimePart integer,
    orderNumber   text,
    startPart     text,
    endPart       text,
    bidStartTime  text,
    bidEndTime    text,
    constraint system_opid_time_config_pk
        primary key (tradeTimePart)
);
insert into system_opid_time_config_dg_tmp(startPart, endPart, tradeTimePart, orderNumber, bidStartTime, bidEndTime)
select startPart, endPart, tradeTimePart, orderNumber, bidStartTime, bidEndTime
from system_opid_time_config;
drop table system_opid_time_config;
alter table system_opid_time_config_dg_tmp rename to system_opid_time_config;

create table user_opid_config_dg_tmp
(
    unitId       text,
    strategyDate text,
    strategyType integer,
    unitName     text,
    createTime   text,
    constraint user_opid_config_pk
        primary key (unitId, strategyDate) on conflict replace
);
insert into user_opid_config_dg_tmp(unitId, unitName, strategyDate, strategyType, createTime)
select unitId, unitName, strategyTime, strategyType, createTime
from user_opid_config;
drop table user_opid_config;
alter table user_opid_config_dg_tmp rename to user_opid_config;

create table user_opid_config_detail_dg_tmp
(
    unitId       text,
    strategyDate text,
    startTime    text,
    endTime      text,
    strategyType integer,
    percent      numeric,
    price        numeric,
    reportPower  numeric,
    reportPrice  numeric,
    powerLimit   numeric,
    status       integer default 0,
    timePart     integer,
    dispatchId   text,
    createTime   text,
    updateTime   text,
    constraint user_opid_config_detail_pk
        primary key (unitId, strategyDate, startTime, endTime) on conflict replace
);
insert into user_opid_config_detail_dg_tmp(unitId, strategyDate, startTime, endTime, percent, price, dispatchId,
                                           reportPrice, reportPower, status, createTime, updateTime,
                                           strategyType, timePart, powerLimit)
select unitId,
       strategyTime,
       startTime,
       endTime,
       percent,
       price,
       dispatchId,
       strategyPrice,
       strategyQuantity,
       status,
       createTime,
       updateTime,
       strategyType,
       timePart,
       powerLimit
from user_opid_config_detail;
drop table user_opid_config_detail;
alter table user_opid_config_detail_dg_tmp rename to user_opid_config_detail;

create table user_opbd_config
(
    unitId       text,
    strategyDate text,
    strategyType integer,
    unitName     text,
    createTime   text,
    constraint user_opbd_config_pk
        primary key (unitId, strategyDate) on conflict replace
);


create table user_opbd_config_detail
(
    unitId       text,
    strategyDate text,
    startTime    text,
    endTime      text,
    strategyType integer,
    percent      numeric,
    price        numeric,
    reportPower  numeric,
    reportPrice  numeric,
    powerLimit   numeric,
    status       integer default 0,
    timePart     integer,
    dispatchId   text,
    createTime   text,
    updateTime   text,
    constraint user_opbd_config_detail_pk
        primary key (unitId, strategyDate, startTime, endTime) on conflict replace
);

create index user_opbd_config_strategy_time_index on user_opbd_config (strategyDate);
create index user_opbd_config_detail_strategy_time_index on user_opbd_config_detail (strategyDate);


create table user_day_rolling_time_info
(
    strategyDate   text not null, -- 策略时间; eg: ['2023-12-27']
    declareDate    text not null, -- 申报时间; eg: ['2023-12-29', '2023-12-30', '2023-12-31']
    tradeseqId     text,          -- 交易序列; eg: "PHBSX2023123142003832"
    beginDate      text,          -- 标的开始日期; eg: 2023-12-31T00:00:00.000+0800
    endDate        text,          -- 标的结束日期; eg: 2023-12-31T00:00:00.000+0800
    bidDate        text,          -- 策略日期; eg: 2023-12-27T00:00:00.000+0800
    startTime      text,          -- 开始时间; eg: 2023-12-27T10:00:00.000+0800
    endTime        text,          -- 结束时间; eg: 2023-12-27T16:00:00.000+0800
    startTime1     text,          -- 开始时间; eg: 2023-12-27 10:00:00.0
    endTime1       text,          -- 结束时间; eg: 2023-12-27 12:00:00.0
    startTime2     text,          -- 开始时间; eg: 2023-12-27 14:00:00.0
    endTime2       text,          -- 结束时间; eg: 2023-12-27 16:00:00.0
    tradeBatchId   text,          -- 交易批次; eg: A202312314004339
    tradeCycle     text,          -- 交易周期: {1: 多月交易, 2: 月度交易, 3: 旬交易, 4: 日滚动交易}
    tradeStage     text,          -- 交易类型: {1: 集中竞价, 2: 滚动撮合}
    tradeseqName   text,          -- 交易名称; eg: 2023年12月27日日滚动交易(2023-12-31)
    tradeseqStatus text,          -- 交易状态
    createTime     text,          -- 创建时间
    constraint user_day_rolling_time_info_pk
        primary key (strategyDate, declareDate, tradeSeqId) on conflict replace
);

create table user_day_rolling_config_detail
(
    unitId          text    not null,           -- 交易单元
    strategyDate    text    not null,           -- 策略时间; eg: ['2024-01-01']
    declareDate     text    not null,           -- 申报时间; eg: ['2024-01-02', '2024-01-03', '2024-01-04']
    timeCode        integer not null,           -- 时间段: [1, 24]
    type            integer not null default 0, -- 类型: {0: 未配置占位符, 1: 抢报, 2: 盯盘}
    tradeSeqId      text,                       -- 交易 ID; eg: "PHBSX2023123142003832"
    tradeRole       text,                       -- 交易方向: {1: 买入, 2: 卖出}
    power           numeric,                    -- 用户配置电量
    price           numeric,                    -- 用户配置电价
    reportPower     numeric,                    -- 申报电量
    reportPrice     numeric,                    -- 申报电价
    buyEnergyLimit  numeric,                    -- 买入限制
    sellEnergyLimit numeric,                    -- 卖出限制
    priceLowerLimit numeric,                    -- 电价下限
    priceUpperLimit numeric,                    -- 电价上限
    status          integer          default 0, -- 状态: {0: 未申报, 1: 成功, 2: 失败}
    createTime      text,                       -- 创建时间
    updateTime      text,                       -- 更新时间
    constraint user_day_rolling_config_detail_pk
        primary key (unitId, strategyDate, declareDate, timeCode, type) on conflict replace
);

create table user_day_rolling_config_detail_log
(
    unitId       text    not null,           -- 交易单元
    strategyDate text    not null,           -- 策略时间; eg: ['2024-01-01']
    declareDate  text    not null,           -- 申报时间; eg: ['2024-01-02', '2024-01-03', '2024-01-04']
    timeCode     integer not null,           -- 时间段: [1, 24]
    type         integer not null default 0, -- 类型: {0: 未配置占位符, 1: 抢报, 2: 盯盘}
    tradeSeqId   text,                       -- 交易 ID; eg: "PHBSX2023123142003832"
    tradeRole    text,                       -- 交易方向: {1: 买入, 2: 卖出}
    power        numeric,                    -- 用户配置电量
    price        numeric,                    -- 用户配置电价
    reportPower  numeric,                    -- 申报电量
    reportPrice  numeric,                    -- 申报电价
    status       integer          default 0, -- 状态: {0: 未申报, 1: 成功, 2: 失败}
    createTime   text,                       -- 创建时间
    constraint user_day_rolling_config_detail_log_pk
        primary key (unitId, strategyDate, declareDate, timeCode, type, createTime) on conflict replace
);

alter table user_day_rolling_config_detail add tradeRoleLock text; -- 交易方向锁定[tradeDirection] {1: 买入, 2: 卖出}
