alter table system_unit_config add generatorSetId TEXT; -- 机组ID

create table data_profit_analysis
(
    unitId               text not null, -- 交易单元
    date                 text not null, -- 策略时间; eg: '2024-01-01'
    ipDayAheadPrice      text,          -- 省内日前电价
    ipRealTimePrice      text,          -- 省内实时电价
    opbdReportPower      text,          -- 省间日前申报出力
    opbdBidPower         text,          -- 省间日前中标出力
    opbdDayAheadBidPrice text,          -- 省间日前日前中标电价
    opidReportPower      text,          -- 省间日内申报出力
    opidBidPower         text,          -- 省间日内中标出力
    opidRealTimeBidPrice text,          -- 省间日内实时中标电价
    createTime           text,          -- 创建时间
    constraint data_profit_analysis_pk
        primary key (unitId, date) on conflict replace
);
