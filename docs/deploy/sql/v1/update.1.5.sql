create table user_imqp_new_energy_trade_config -- 江西中长期 月内保量保价新能源交易 用户配置
(
    unitId       text not null,              -- 交易单元
    strategyDate text not null,              -- 策略时间; eg: '2024-05'
    declareDate  text not null,              -- 申报时间; eg: '2024-05-25'
    trid         text,                       -- 交易 ID; eg: '1200002566'
    jydyid       text,                       -- 交易 ID; eg: 'EB475DC2C5C39FCEE05344DCEA0A8C1F'
    guid         text,                       -- 交易 ID; eg: '19807B192DA03471E06343DCEA0AA094'
    startTime    text,                       -- 开始时间; eg: '2024-05-21 09:30:00'
    endTime      text,                       -- 结束时间; eg: '2024-05-21 16:30:00'
    startTimeAm  text    default '09:30:00', -- 开始时间1; eg: '09:30:00'
    endTimeAm    text    default '11:30:00', -- 结束时间1; eg: '11:30:00'
    startTimePm  text    default '14:30:00', -- 开始时间2; eg: '14:30:00'
    endTimePm    text    default '16:30:00', -- 结束时间2; eg: '16:30:00'
    zcPower      numeric,                    -- 增持可挂牌剩余电量
    jcPower      numeric,                    -- 减持可挂牌剩余电量
    yfPower      numeric,                    -- 已发电量
    tradeRole    text,                       -- 交易方向: {1: 增持, 2: 减持}
    power        numeric,                    -- 用户配置挂牌电量
    status       integer default 0,          -- 状态: {0: 未申报, 1: 成功, 2: 失败}
    logId        text,                       -- 最新 log 的 ID
    createTime   text,                       -- 创建时间
    updateTime   text,                       -- 更新时间
    constraint user_imqp_new_energy_trade_config_pk
        primary key (unitId, strategyDate, declareDate) on conflict replace
);

create table user_imqp_new_energy_trade_config_log -- 江西中长期 月内保量保价新能源交易 用户配置记录
(
    logId        text not null primary key, -- ID
    unitId       text not null,             -- 交易单元
    strategyDate text not null,             -- 策略时间; eg: '2024-05'
    declareDate  text not null,             -- 申报时间; eg: '2024-05-25'
    zcPower      numeric,                   -- 增持可挂牌剩余电量
    jcPower      numeric,                   -- 减持可挂牌剩余电量
    yfPower      numeric,                   -- 已发电量
    tradeRole    text,                      -- 交易方向: {1: 增持, 2: 减持}
    power        numeric,                   -- 用户配置挂牌电量
    status       integer default 0,         -- 状态: {0: 未申报, 1: 成功, 2: 失败}
    createTime   text,                      -- 创建时间
    updateTime   text                       -- 更新时间
);
