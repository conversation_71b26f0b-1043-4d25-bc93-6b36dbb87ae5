create table data_profit_analysis -- 山西策略收益分析数据
(
    unitId               text not null, -- 交易单元
    date                 text not null, -- 策略时间; eg: '2024-01-01'
    ipDayAheadPrice      text,          -- 省内日前电价
    ipRealTimePrice      text,          -- 省内实时电价
    opbdReportPower      text,          -- 省间日前申报出力
    opbdBidPower         text,          -- 省间日前中标出力
    opbdDayAheadBidPrice text,          -- 省间日前日前中标电价
    opidReportPower      text,          -- 省间日内申报出力
    opidBidPower         text,          -- 省间日内中标出力
    opidRealTimeBidPrice text,          -- 省间日内实时中标电价
    createTime           text,          -- 创建时间
    constraint data_profit_analysis_pk
        primary key (unitId, date) on conflict replace
);

create table system_account_config -- 交易中心账号配置
(
    plantId      text not null primary key, -- 交易中心场站 ID
    plantName    text,                      -- 交易中心场站名称
    uKeyCode     text,                      -- UKey 内容
    userName     text,                      -- 账号
    password     text,                      -- 密码
    uKeyPrivate  text,                      -- UKey Private
    uKeyPassword text,                      -- UKey Password
    grayTag      text,                      -- 账号 grayTag
    phone        text,                      -- 手机号
    useUKey      integer default 1,         -- 使用UKey登录 {0: 不使用, 1: 使用}
    useSMS       integer default 0,         -- 使用短信登录 {0: 不使用, 1: 使用}
    smsRegex     text                       -- 短信验证码提取正则
);

create table system_opid_time_config -- 省间日内交易时间配置
(
    tradeTimePart integer not null primary key, -- 交易时段: [1,12]
    orderNumber   text,                         -- 时段编号: 0001 - 0012
    startPart     text,                         -- 开始时间
    endPart       text,                         -- 结束时间
    bidStartTime  text,                         -- 申报开始时间
    bidEndTime    text                          -- 申报结束时间
);

create table system_unit_config -- 交易中心交易单元信息配置
(
    unitId         text not null primary key, -- 交易单元 ID
    unitName       text,                      -- 交易单元名称
    plantId        text,                      -- 交易中心场站 ID
    grayTag        text,                      -- 交易单元 grayTag
    dispatchId     text,                      -- 交易单元调度 ID
    dispatchName   text,                      -- 交易单元调度名称
    limitPower     numeric,                   -- 场站装机
    uName          text,                      -- 交易单元简称
    unitIdIlt      text,                      -- 中长期交易单元 ID
    unitNameIlt    text,                      -- 中长期交易单元名称
    generatorSetId text,                      -- 机组 ID
    type           integer                    -- 类型 {0: 风电, 1: 光电}
);

create table system_unit_config_proxy_purchase -- 山西电网代购电交易单元配置
(
    unitId   text not null primary key, -- 交易单元 ID
    unitName text,                      -- 交易单元名称
    plantId  text                       -- 账号 ID
);

create table user_day_rolling_config_detail -- 日滚动配置
(
    unitId          text              not null, -- 交易单元
    strategyDate    text              not null, -- 策略时间; eg: ['2024-01-01']
    declareDate     text              not null, -- 申报时间; eg: ['2024-01-02', '2024-01-03', '2024-01-04']
    timeCode        integer           not null, -- 时间段: [1, 24]
    type            integer default 0 not null, -- 类型: {0: 未配置占位符, 1: 抢报, 2: 盯盘}
    tradeSeqId      text,                       -- 交易 ID; eg: "PHBSX2023123142003832"
    tradeRole       text,                       -- 交易方向: {1: 买入, 2: 卖出}
    tradeRoleLock   text,                       -- 交易方向锁定: {1: 买入, 2: 卖出} ~~> [tradeDirection]
    power           numeric,                    -- 用户配置电量
    price           numeric,                    -- 用户配置电价
    reportPower     numeric,                    -- 申报电量
    reportPrice     numeric,                    -- 申报电价
    buyEnergyLimit  numeric,                    -- 买入限制
    sellEnergyLimit numeric,                    -- 卖出限制
    priceLowerLimit numeric,                    -- 电价下限
    priceUpperLimit numeric,                    -- 电价上限
    status          integer default 0,          -- 状态: {0: 未申报, 1: 成功, 2: 失败}
    createTime      text,                       -- 创建时间
    updateTime      text,                       -- 更新时间
    constraint user_day_rolling_config_detail_pk
        primary key (unitId, strategyDate, declareDate, timeCode, type) on conflict replace
);

create table user_day_rolling_config_detail_log -- 日滚动配置日志
(
    unitId       text              not null, -- 交易单元
    strategyDate text              not null, -- 策略时间; eg: ['2024-01-01']
    declareDate  text              not null, -- 申报时间; eg: ['2024-01-02', '2024-01-03', '2024-01-04']
    timeCode     integer           not null, -- 时间段: [1, 24]
    type         integer default 0 not null, -- 类型: {0: 未配置占位符, 1: 抢报, 2: 盯盘}
    tradeSeqId   text,                       -- 交易 ID; eg: "PHBSX2023123142003832"
    tradeRole    text,                       -- 交易方向: {1: 买入, 2: 卖出}
    power        numeric,                    -- 用户配置电量
    price        numeric,                    -- 用户配置电价
    reportPower  numeric,                    -- 申报电量
    reportPrice  numeric,                    -- 申报电价
    status       integer default 0,          -- 状态: {0: 未申报, 1: 成功, 2: 失败}
    createTime   text,                       -- 创建时间
    constraint user_day_rolling_config_detail_log_pk
        primary key (unitId, strategyDate, declareDate, timeCode, type, createTime) on conflict replace
);

create table user_day_rolling_time_info -- 日滚动时间配置
(
    strategyDate   text not null, -- 策略时间; eg: ['2023-12-27']
    declareDate    text not null, -- 申报时间; eg: ['2023-12-29', '2023-12-30', '2023-12-31']
    tradeSeqId     text,          -- 交易序列; eg: "PHBSX2023123142003832"
    beginDate      text,          -- 标的开始日期; eg: 2023-12-31T00:00:00.000+0800
    endDate        text,          -- 标的结束日期; eg: 2023-12-31T00:00:00.000+0800
    bidDate        text,          -- 策略日期; eg: 2023-12-27T00:00:00.000+0800
    startTime      text,          -- 开始时间; eg: 2023-12-27T10:00:00.000+0800
    endTime        text,          -- 结束时间; eg: 2023-12-27T16:00:00.000+0800
    startTime1     text,          -- 开始时间; eg: 2023-12-27 10:00:00.0
    endTime1       text,          -- 结束时间; eg: 2023-12-27 12:00:00.0
    startTime2     text,          -- 开始时间; eg: 2023-12-27 14:00:00.0
    endTime2       text,          -- 结束时间; eg: 2023-12-27 16:00:00.0
    tradeBatchId   text,          -- 交易批次; eg: A202312314004339
    tradeCycle     text,          -- 交易周期: {1: 多月交易, 2: 月度交易, 3: 旬交易, 4: 日滚动交易}
    tradeStage     text,          -- 交易类型: {1: 集中竞价, 2: 滚动撮合}
    tradeseqName   text,          -- 交易名称; eg: 2023年12月27日日滚动交易(2023-12-31)
    tradeseqStatus text,          -- 交易状态
    createTime     text,          -- 创建时间
    constraint user_day_rolling_time_info_pk
        primary key (strategyDate, declareDate, tradeSeqId) on conflict replace
);

create table user_day_rolling_info_market
(
    strategyDate  text    not null, -- 策略时间; eg: ['2024-01-01']
    tradeseqId    text    not null, -- 交易 ID; eg: "PHBSX2023123142003832"
    timeCode      integer not null, -- 时间段: [1, 24]; 其中：-1 代表信息数据
    tradeseqName  text,             -- 申报名称; eg: 2024年09月10日日滚动交易(2024-9-13)
    total         text,             -- 总交易量
    maxPrice      text,             -- 最高价
    minPrice      text,             -- 最低价
    weightedPrice text,             -- 加权价格
    midPrice      text,             -- 中位数价格
    createTime    text,             -- 创建时间
    primary key (strategyDate, tradeseqId, timeCode) on conflict replace
);

create table user_day_rolling_info_unit
(
    unitId       text    not null, -- 交易单元
    strategyDate text    not null, -- 策略时间; eg: ['2024-01-01']
    tradeSeqId   text    not null, -- 交易 ID; eg: "PHBSX2023123142003832"
    timeCode     integer not null, -- 时间段: [1, 24]
    tradeRole    text,             -- 交易方向: {1: 买入, 2: 卖出}
    power        text,             -- 用户配置电量
    price        text,             -- 用户配置电价
    detail       text,             -- 详情：[{"bidEnergy": 30}]
    createTime   text,             -- 创建时间
    primary key (unitId, strategyDate, tradeSeqId, timeCode) on conflict replace
);

create table user_imqp_new_energy_trade_config -- 江西中长期 月内保量保价新能源交易 用户配置
(
    unitId       text not null,              -- 交易单元
    strategyDate text not null,              -- 策略时间; eg: '2024-05'
    declareDate  text not null,              -- 申报时间; eg: '2024-05-25'
    trid         text,                       -- 交易 ID; eg: '1200002566'
    jydyid       text,                       -- 交易 ID; eg: 'EB475DC2C5C39FCEE05344DCEA0A8C1F'
    guid         text,                       -- 交易 ID; eg: '19807B192DA03471E06343DCEA0AA094'
    startTime    text,                       -- 开始时间; eg: '2024-05-21 09:30:00'
    endTime      text,                       -- 结束时间; eg: '2024-05-21 16:30:00'
    startTimeAm  text    default '09:30:00', -- 开始时间1; eg: '09:30:00'
    endTimeAm    text    default '11:30:00', -- 结束时间1; eg: '11:30:00'
    startTimePm  text    default '14:30:00', -- 开始时间2; eg: '14:30:00'
    endTimePm    text    default '16:30:00', -- 结束时间2; eg: '16:30:00'
    zcPower      numeric,                    -- 增持可挂牌剩余电量
    jcPower      numeric,                    -- 减持可挂牌剩余电量
    yfPower      numeric,                    -- 已发电量
    tradeRole    text,                       -- 交易方向: {1: 增持, 2: 减持}
    power        numeric,                    -- 用户配置挂牌电量
    status       integer default 0,          -- 状态: {0: 未申报, 1: 成功, 2: 失败}
    logId        text,                       -- 最新 log 的 ID
    createTime   text,                       -- 创建时间
    updateTime   text,                       -- 更新时间
    constraint user_imqp_new_energy_trade_config_pk
        primary key (unitId, strategyDate, declareDate) on conflict replace
);

create table user_imqp_new_energy_trade_config_log -- 江西中长期 月内保量保价新能源交易 用户配置记录
(
    logId        text not null primary key, -- ID
    unitId       text not null,             -- 交易单元
    strategyDate text not null,             -- 策略时间; eg: '2024-05'
    declareDate  text not null,             -- 申报时间; eg: '2024-05-25'
    zcPower      numeric,                   -- 增持可挂牌剩余电量
    jcPower      numeric,                   -- 减持可挂牌剩余电量
    yfPower      numeric,                   -- 已发电量
    tradeRole    text,                      -- 交易方向: {1: 增持, 2: 减持}
    power        numeric,                   -- 用户配置挂牌电量
    status       integer default 0,         -- 状态: {0: 未申报, 1: 成功, 2: 失败}
    createTime   text,                      -- 创建时间
    updateTime   text                       -- 更新时间
);

create table user_opbd_config -- 省间日前配置
(
    unitId       text,    -- 交易单元 ID
    strategyDate text,    -- 策略时间
    strategyType integer, -- 申报类型 {1: , 2: , 3: , 4: }
    unitName     text,    -- 交易单元名称
    createTime   text,    -- 创建时间
    constraint user_opbd_config_pk
        primary key (unitId, strategyDate) on conflict replace
);

create index user_opbd_config_strategy_time_index on user_opbd_config (strategyDate);

create table user_opbd_config_detail -- 省间日前配置详情
(
    unitId       text,              -- 交易单元 ID
    strategyDate text,              -- 策略时间
    startTime    text,              -- 开始时间
    endTime      text,              -- 结束时间
    strategyType integer,           -- 申报类型 {1: , 2: , 3: 日内, 4: 日前}
    percent      numeric,           -- 用户配置电量比例
    price        numeric,           -- 用户配置电价
    reportPower  numeric,           -- 实际申报电量
    reportPrice  numeric,           -- 实际申报电价
    powerLimit   numeric,           -- 限额
    status       integer default 0, -- 状态 {0: 未申报, 1: 成功, 2: 失败}
    timePart     integer,           -- 交易时段: [1,12]
    dispatchId   text,              -- 交易单元调度 ID
    createTime   text,              -- 创建时间
    updateTime   text,              -- 更新时间
    constraint user_opbd_config_detail_pk
        primary key (unitId, strategyDate, startTime, endTime) on conflict replace
);

create index user_opbd_config_detail_strategy_time_index on user_opbd_config_detail (strategyDate);

create table user_opid_config -- 省间日内配置
(
    unitId       text,              -- 交易单元 ID
    strategyDate text,              -- 策略时间
    strategyType integer,           -- 申报类型 {1: , 2: , 3: 日内, 4: 日前}
    reportType   integer default 1, -- 申报类型 {1: 手动, 2: 自动}
    unitName     text,              -- 交易单元名称
    createTime   text,              -- 创建时间
    constraint user_opid_config_pk
        primary key (unitId, strategyDate) on conflict replace
);

create table user_opid_config_detail -- 省间日内配置详情
(
    unitId       text,              -- 交易单元 ID
    strategyDate text,              -- 策略时间
    startTime    text,              -- 开始时间
    endTime      text,              -- 结束时间
    strategyType integer,           -- 申报类型 {1: , 2: , 3: 日内, 4: 日前}
    reportType   integer default 1, -- 申报类型 {1: 手动, 2: 自动}
    percent      numeric,           -- 用户配置电量比例
    price        numeric,           -- 用户配置电价
    reportPower  numeric,           -- 实际申报电量
    reportPrice  numeric,           -- 实际申报电价
    powerLimit   numeric,           -- 限额
    status       integer default 0, -- 状态 {0: 未申报, 1: 成功, 2: 失败}
    timePart     integer,           -- 交易时段: [1,12]
    dispatchId   text,              -- 交易单元调度 ID
    createTime   text,              -- 创建时间
    updateTime   text,              -- 更新时间
    constraint user_opid_config_detail_pk
        primary key (unitId, strategyDate, startTime, endTime) on conflict replace
);

create table user_proxy_purchase_config -- 山西电网代购电 用户配置
(
    unitId       text not null,     -- 交易单元
    strategyDate text not null,     -- 策略时间; eg: '2024-05'
    unitName     text,              -- 交易单元名称
    tradeseqId   text,              -- 交易 ID; eg: 'PHBSX20240517A0001'
    tradeseqName text,              -- 交易名称; eg: '2024年6月月度电网代理购电挂牌电力直接交易'
    listedId     text,              -- 交易 ID; eg: 'bb23e46d9bb14e7db8b58522a4ac4aa9'
    startTime    text,              -- 开始时间; eg: '2024-05-23 09:30:00'
    endTime      text,              -- 结束时间; eg: '2024-05-23 11:30:00'
    price        numeric,           -- 摘牌电价
    power        numeric,           -- 用户配置摘牌电量
    priceLimit   numeric,           -- 用户配置摘牌电价最低值
    status       integer default 0, -- 状态: {0: 未申报, 1: 成功, 2: 失败, 3: 电价过低}
    retryCount   integer,           -- 重试次数
    logId        text,              -- 最新 log 的 ID
    createTime   text,              -- 创建时间
    updateTime   text,              -- 更新时间
    constraint user_imqp_new_energy_trade_config_pk
        primary key (unitId, strategyDate) on conflict replace
);

create table user_proxy_purchase_config_log -- 山西电网代购电 用户配置记录
(
    logId        text not null primary key, -- ID
    unitId       text not null,             -- 交易单元
    strategyDate text not null,             -- 策略时间; eg: '2024-05'
    price        numeric,                   -- 摘牌电价
    power        numeric,                   -- 用户配置摘牌电量
    priceLimit   numeric,                   -- 用户配置摘牌电价最低值
    status       integer default 0,         -- 状态: {0: 未申报, 1: 成功, 2: 失败, 3: 电价过低}
    retryCount   integer,                   -- 重试次数
    createTime   text,                      -- 创建时间
    updateTime   text                       -- 更新时间
);

create index user_proxy_purchase_config_log_idx on user_proxy_purchase_config_log (unitId);

create table user_config -- 配置表
(
    key        text not null,
    value      text,
    updateTime text,
    constraint user_config_pk
        primary key (key) on conflict replace
);


INSERT INTO system_opid_time_config (tradeTimePart, orderNumber, startPart, endPart, bidStartTime, bidEndTime)
VALUES (1, '0001', '00:15', '02:00', '22:02', '22:08'),
       (2, '0002', '02:15', '04:00', '00:02', '00:08'),
       (3, '0003', '04:15', '06:00', '02:02', '02:08'),
       (4, '0004', '06:15', '08:00', '04:02', '04:08'),
       (5, '0005', '08:15', '10:00', '06:02', '06:08'),
       (6, '0006', '10:15', '12:00', '08:02', '08:08'),
       (7, '0007', '12:15', '14:00', '10:02', '10:08'),
       (8, '0008', '14:15', '16:00', '12:02', '12:08'),
       (9, '0009', '16:15', '18:00', '14:02', '14:08'),
       (10, '0010', '18:15', '20:00', '16:02', '16:08'),
       (11, '0011', '20:15', '22:00', '18:02', '18:08'),
       (12, '0012', '22:15', '24:00', '20:02', '20:08');
