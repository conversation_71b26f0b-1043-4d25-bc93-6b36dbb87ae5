create table system_unit_config_proxy_purchase -- 山西电网代购电 交易单元
(
    unitId   text not null primary key, -- 交易单元 ID
    unitName text,                      -- 交易单元名称
    plantId  text                       -- 账号 ID
);

create table user_proxy_purchase_config -- 山西电网代购电 用户配置
(
    unitId       text not null,     -- 交易单元
    strategyDate text not null,     -- 策略时间; eg: '2024-05'
    unitName     text,              -- 交易单元名称
    tradeseqId   text,              -- 交易 ID; eg: 'PHBSX20240517A0001'
    tradeseqName text,              -- 交易名称; eg: '2024年6月月度电网代理购电挂牌电力直接交易'
    listedId     text,              -- 交易 ID; eg: 'bb23e46d9bb14e7db8b58522a4ac4aa9'
    startTime    text,              -- 开始时间; eg: '2024-05-23 09:30:00'
    endTime      text,              -- 结束时间; eg: '2024-05-23 11:30:00'
    price        numeric,           -- 摘牌电价
    power        numeric,           -- 用户配置摘牌电量
    priceLimit   numeric,           -- 用户配置摘牌电价最低值
    status       integer default 0, -- 状态: {0: 未申报, 1: 成功, 2: 失败, 3: 电价过低}
    retryCount   integer,           -- 重试次数
    logId        text,              -- 最新 log 的 ID
    createTime   text,              -- 创建时间
    updateTime   text,              -- 更新时间
    constraint user_imqp_new_energy_trade_config_pk
        primary key (unitId, strategyDate) on conflict replace
);

create table user_proxy_purchase_config_log -- 山西电网代购电 用户配置记录
(
    logId        text not null primary key, -- ID
    unitId       text not null,             -- 交易单元
    strategyDate text not null,             -- 策略时间; eg: '2024-05'
    price        numeric,                   -- 摘牌电价
    power        numeric,                   -- 用户配置摘牌电量
    priceLimit   numeric,                   -- 用户配置摘牌电价最低值
    status       integer default 0,         -- 状态: {0: 未申报, 1: 成功, 2: 失败, 3: 电价过低}
    retryCount   integer,                   -- 重试次数
    createTime   text,                      -- 创建时间
    updateTime   text                       -- 更新时间
);

create index user_proxy_purchase_config_log_idx on user_proxy_purchase_config_log (unitId);
