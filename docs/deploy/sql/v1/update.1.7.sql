create table user_day_rolling_info_market
(
    strategyDate  text    not null, -- 策略时间; eg: ['2024-01-01']
    tradeseqId    text    not null, -- 交易 ID; eg: "PHBSX2023123142003832"
    timeCode      integer not null, -- 时间段: [1, 24]
    tradeseqName  text,             -- 申报名称; eg: 2024年09月10日日滚动交易(2024-9-13)
    total         text,             -- 总交易量
    maxPrice      text,             -- 最高价
    minPrice      text,             -- 最低价
    weightedPrice text,             -- 加权价格
    midPrice      text,             -- 中位数价格
    createTime    text,             -- 创建时间
    primary key (strategyDate, tradeseqId, timeCode) on conflict replace
);

create table user_day_rolling_info_unit
(
    unitId       text    not null, -- 交易单元
    strategyDate text    not null, -- 策略时间; eg: ['2024-01-01']
    tradeSeqId   text    not null, -- 交易 ID; eg: "PHBSX2023123142003832"
    timeCode     integer not null, -- 时间段: [1, 24]
    tradeRole    text,             -- 交易方向: {1: 买入, 2: 卖出}
    power        text,             -- 用户配置电量
    price        text,             -- 用户配置电价
    detail       text,             -- 详情：[{"bidEnergy": 30}]
    createTime   text,             -- 创建时间
    primary key (unitId, strategyDate, tradeSeqId, timeCode) on conflict replace
);
