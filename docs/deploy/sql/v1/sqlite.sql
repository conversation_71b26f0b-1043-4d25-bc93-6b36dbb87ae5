begin transaction;

drop table if exists plant_config_info;
create table if not exists plant_config_info
(
    plantId      text not null,
    plantName    text,
    uKeyCode     text,
    type         integer,
    userName     text,
    password     text,
    uKeyPrivate  text,
    uKeyPassowrd text,
    grayTag      text,
    endTime      text,
    primary key (plantId)
);

drop table if exists spot_price;
create table if not exists spot_price
(
    realPrice   numeric,
    beforePrice numeric,
    dateTime    text,
    createTime  text,
    constraint spot_price_unique unique (dateTime)
);

drop table if exists trade_time_config_info;
create table if not exists trade_time_config_info
(
    startPart     text,
    endPart       text,
    tradeTimePart integer,
    orderNumber   text,
    bidStartTime  text,
    bidEndTime    text
);

drop table if exists unit_config_info;
create table if not exists unit_config_info
(
    unitId     text not null,
    unitName   text,
    plantId    text,
    grayTag    text,
    dispatchId text,
    limitPower numeric,
    uName      text,
    primary key (unitId)
);

drop table if exists user_strategy_config_info;
create table if not exists user_strategy_config_info
(
    unitId       text,
    unitName     text,
    strategyTime text,
    strategyType integer,
    createTime   text,
    constraint user_strategy_config_info_unique unique (unitId, strategyTime)
);

drop table if exists user_strategy_time_config_info;
create table if not exists user_strategy_time_config_info
(
    unitId           text,
    strategyTime     text,
    startTime        text,
    endTime          text,
    percent          numeric,
    percentN         numeric,
    price            numeric,
    dispatchId       text,
    strategyPrice    numeric,
    strategyQuantity numeric,
    status           integer default 0,
    createTime       text,
    updateTime       text,
    constraint user_strategy_time_config_info_unique unique (unitId, strategyTime, startTime, endTime)
);

insert into trade_time_config_info (startPart, endPart, tradeTimePart, orderNumber, bidStartTime, bidEndTime)
values ('00:15', '02:00', 1, '0001', '22:02', '22:08'),
       ('02:15', '04:00', 2, '0002', '00:02', '00:08'),
       ('04:15', '06:00', 3, '0003', '02:02', '02:08'),
       ('06:15', '08:00', 4, '0004', '04:02', '04:08'),
       ('08:15', '10:00', 5, '0005', '06:02', '06:08'),
       ('10:15', '12:00', 6, '0006', '08:02', '08:08'),
       ('12:15', '14:00', 7, '0007', '10:02', '10:08'),
       ('14:15', '16:00', 8, '0008', '12:02', '12:08'),
       ('16:15', '18:00', 9, '0009', '14:02', '14:08'),
       ('18:15', '20:00', 10, '0010', '16:02', '16:08'),
       ('20:15', '22:00', 11, '0011', '18:02', '18:08'),
       ('22:15', '24:00', 12, '0012', '20:02', '20:08');

insert into plant_config_info(plantId, plantName, uKeyCode, type, userName, password, uKeyPrivate, uKeyPassowrd, grayTag, endTime)
values ('p1', '测试场站', 'MIIXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', 0, 'user01', '123456', '', '', '0123456', '2024-12-31');

insert into unit_config_info(unitId, unitName, plantId, grayTag, dispatchId, limitPower, uName)
values ('u1', '测试交易单元15kv#1', 'p1', '0123456', '12345678', '100', '测试交易单元'),
       ('u2', '测试交易单元15kv#2', 'p1', '0123456', '12345678', '100', '测试交易单元');

commit;
