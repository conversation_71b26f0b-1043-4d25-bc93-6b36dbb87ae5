// ==UserScript==
// @name         自动登录
// @namespace    http://tampermonkey.net/
// @version      0.2.20240718
// @description  try to take over the world!
// <AUTHOR>
// @match        http://127.0.0.1:8081/*
// @match        http://127.0.0.1:33000/*
// @include      https://pmos.sx.sgcc.com.cn
// @match        https://pmos.sx.sgcc.com.cn
// @match        https://pmos.sx.sgcc.com.cn/
// @icon         https://www.google.com/s2/favicons?sz=64&domain=sgcc.com.cn
// @grant        GM_getResourceText
// @grant        GM_addStyle
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_xmlhttpRequest
// @grant        GM_cookie
// @grant        GM_webRequest
// @grant        GM_addValueChangeListener
// @run-at       document-start
// @noframes
// ==/UserScript==

(function () {
    'use strict';
    var Notify=function(){"use strict";var t=function(t){t.wrapper.classList.add("notify--fade"),setTimeout((function(){t.wrapper.classList.add("notify--fadeIn")}),100)},e=function(t){t.wrapper.classList.remove("notify--fadeIn"),setTimeout((function(){t.wrapper.remove()}),t.speed)},s=function(t){t.wrapper.classList.add("notify--slide"),setTimeout((function(){t.wrapper.classList.add("notify--slideIn")}),100)},i=function(t){t.wrapper.classList.remove("notify--slideIn"),setTimeout((function(){t.wrapper.remove()}),t.speed)};return function(){function o(t){var e=this;this.notifyOut=function(t){t(e)};var s=t.status,i=t.type,o=void 0===i?1:i,n=t.title,a=t.text,r=t.showIcon,c=void 0===r||r,h=t.customIcon,l=void 0===h?"":h,p=t.customClass,d=void 0===p?"":p,u=t.speed,f=void 0===u?500:u,m=t.effect,v=void 0===m?"fade":m,w=t.showCloseButton,y=void 0===w||w,L=t.autoclose,g=void 0!==L&&L,C=t.autotimeout,x=void 0===C?3e3:C,I=t.gap,E=void 0===I?20:I,Z=t.distance,b=void 0===Z?20:Z,B=t.position,M=void 0===B?"right top":B,N=t.customWrapper,O=void 0===N?"":N;this.customWrapper=O,this.status=s,this.title=n,this.text=a,this.showIcon=c,this.customIcon=l,this.customClass=d,this.speed=f,this.effect=v,this.showCloseButton=y,this.autoclose=g,this.autotimeout=x,this.gap=E,this.distance=b,this.type=o,this.position=M,this.checkRequirements()?(this.setContainer(),this.setWrapper(),this.setPosition(),this.showIcon&&this.setIcon(),this.showCloseButton&&this.setCloseButton(),this.setContent(),this.container.prepend(this.wrapper),this.setEffect(),this.notifyIn(this.selectedNotifyInEffect),this.autoclose&&this.autoClose(),this.setObserver()):console.error("You must specify 'title' or 'text' at least.")}return o.prototype.checkRequirements=function(){return!(!this.title&&!this.text)},o.prototype.setContainer=function(){var t=document.querySelector(".notifications-container");t?this.container=t:(this.container=document.createElement("div"),this.container.classList.add("notifications-container"),document.body.appendChild(this.container)),this.container.style.setProperty("--distance",this.distance+"px")},o.prototype.setPosition=function(){var t="notify-is-";"center"===this.position?this.container.classList.add(t+"center"):this.container.classList.remove(t+"center"),this.position.includes("left")?this.container.classList.add(t+"left"):this.container.classList.remove(t+"left"),this.position.includes("right")?this.container.classList.add(t+"right"):this.container.classList.remove(t+"right"),this.position.includes("x-center")?this.container.classList.add(t+"x-center"):this.container.classList.remove(t+"x-center"),this.position.includes("top")?this.container.classList.add(t+"top"):this.container.classList.remove(t+"top"),this.position.includes("bottom")?this.container.classList.add(t+"bottom"):this.container.classList.remove(t+"bottom"),this.position.includes("y-center")?this.container.classList.add(t+"y-center"):this.container.classList.remove(t+"y-center")},o.prototype.setCloseButton=function(){var t=this,e=document.createElement("div");e.classList.add("notify__close"),e.innerHTML='<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="m8.94 8 4.2-4.193a.67.67 0 0 0-.947-.947L8 7.06l-4.193-4.2a.67.67 0 1 0-.947.947L7.06 8l-4.2 4.193a.667.667 0 0 0 .217 1.093.666.666 0 0 0 .73-.146L8 8.94l4.193 4.2a.665.665 0 0 0 .947 0 .665.665 0 0 0 0-.947L8.94 8Z" fill="currentColor"/></svg>',this.wrapper.appendChild(e),e.addEventListener("click",(function(){t.close()}))},o.prototype.setWrapper=function(){var t;this.customWrapper?this.wrapper=(t=this.customWrapper,(new DOMParser).parseFromString(t,"text/html").body.childNodes[0]):this.wrapper=document.createElement("div"),this.wrapper.style.setProperty("--gap",this.gap+"px"),this.wrapper.style.transitionDuration=this.speed+"ms",this.wrapper.classList.add("notify"),this.wrapper.classList.add("notify--type-"+this.type),this.wrapper.classList.add("notify--"+this.status),this.autoclose&&this.wrapper.style.setProperty("--timeout",""+(this.autotimeout+this.speed)),this.autoclose&&this.wrapper.classList.add("notify-autoclose"),this.customClass&&this.wrapper.classList.add(this.customClass)},o.prototype.setContent=function(){var t,e,s=document.createElement("div");s.classList.add("notify-content"),this.title&&((t=document.createElement("div")).classList.add("notify__title"),t.textContent=this.title,this.showCloseButton||(t.style.paddingRight="0")),this.text&&((e=document.createElement("div")).classList.add("notify__text"),e.innerHTML=this.text.trim(),this.title||(e.style.marginTop="0")),this.wrapper.appendChild(s),this.title&&s.appendChild(t),this.text&&s.appendChild(e)},o.prototype.setIcon=function(){var t=document.createElement("div");t.classList.add("notify__icon"),t.innerHTML=this.customIcon||function(t){switch(t){case"success":return'<svg height="32" width="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill="currentColor" d="m19.627 11.72-5.72 5.733-2.2-2.2a1.335 1.335 0 0 0-2.255.381 1.334 1.334 0 0 0 .375 1.5l3.133 3.146a1.333 1.333 0 0 0 1.88 0l6.667-6.667a1.334 1.334 0 1 0-1.88-1.893ZM16 2.667a13.333 13.333 0 1 0 0 26.666 13.333 13.333 0 0 0 0-26.666Zm0 24a10.666 10.666 0 1 1 0-21.333 10.666 10.666 0 0 1 0 21.333Z"/></svg>';case"warning":return'<svg height="32" width="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill="currentColor" d="M13.666 15A1.333 1.333 0 0 0 15 13.667V8.334a1.333 1.333 0 0 0-2.665 0v5.333A1.333 1.333 0 0 0 13.666 15Zm-.507 5.227c.325.134.69.134 1.014 0 .164-.064.314-.159.44-.28a1.56 1.56 0 0 0 .28-.44c.075-.158.111-.332.106-.507a1.333 1.333 0 0 0-.386-.946 1.53 1.53 0 0 0-.44-.28A1.333 1.333 0 0 0 12.334 19a1.4 1.4 0 0 0 .386.947c.127.121.277.216.44.28ZM13.666 27a13.333 13.333 0 1 0 0-26.667 13.333 13.333 0 0 0 0 26.667Zm0-24a10.667 10.667 0 1 1 0 21.333 10.667 10.667 0 0 1 0-21.333Z"/></svg>';case"error":return'<svg height="32" width="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill="currentColor" d="M16 2.667a13.333 13.333 0 1 0 0 26.666 13.333 13.333 0 0 0 0-26.666Zm0 24A10.667 10.667 0 0 1 5.333 16a10.56 10.56 0 0 1 2.254-6.533l14.946 14.946A10.56 10.56 0 0 1 16 26.667Zm8.413-4.134L9.467 7.587A10.56 10.56 0 0 1 16 5.333 10.667 10.667 0 0 1 26.667 16a10.56 10.56 0 0 1-2.254 6.533Z"/></svg>';case"info":return'<svg height="32" width="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill="currentColor" d="M16 14.667A1.333 1.333 0 0 0 14.667 16v5.333a1.333 1.333 0 1 0 2.666 0V16A1.333 1.333 0 0 0 16 14.667Zm.507-5.227a1.333 1.333 0 0 0-1.014 0 1.334 1.334 0 0 0-.44.28c-.117.13-.212.278-.28.44a1.12 1.12 0 0 0-.106.507 1.333 1.333 0 0 0 .386.946c.13.118.279.213.44.28a1.333 1.333 0 0 0 1.84-1.226 1.4 1.4 0 0 0-.386-.947 1.334 1.334 0 0 0-.44-.28ZM16 2.667a13.333 13.333 0 1 0 0 26.666 13.333 13.333 0 0 0 0-26.666Zm0 24a10.666 10.666 0 1 1 0-21.333 10.666 10.666 0 0 1 0 21.333Z"/></svg>'}}(this.status),(this.status||this.customIcon)&&this.wrapper.appendChild(t)},o.prototype.setObserver=function(){var t=this,e=new IntersectionObserver((function(e){e[0].intersectionRatio<=0&&t.close()}),{threshold:0});setTimeout((function(){e.observe(t.wrapper)}),this.speed)},o.prototype.notifyIn=function(t){t(this)},o.prototype.autoClose=function(){var t=this;setTimeout((function(){t.close()}),this.autotimeout+this.speed)},o.prototype.close=function(){this.notifyOut(this.selectedNotifyOutEffect)},o.prototype.setEffect=function(){switch(this.effect){case"fade":this.selectedNotifyInEffect=t,this.selectedNotifyOutEffect=e;break;case"slide":this.selectedNotifyInEffect=s,this.selectedNotifyOutEffect=i;break;default:this.selectedNotifyInEffect=t,this.selectedNotifyOutEffect=e}},o}()}();
    let xhook = function(){"use strict";const e=(e,t)=>Array.prototype.slice.call(e,t);let t=null;"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?t=self:"undefined"!=typeof global?t=global:unsafeWindow&&(t=unsafeWindow);const n=t,o=t.document,r=["load","loadend","loadstart"],s=["progress","abort","error","timeout"],a=e=>["returnValue","totalSize","position"].includes(e),i=function(e,t){for(let n in e){if(a(n))continue;const o=e[n];try{t[n]=o}catch(e){}}return t},c=function(e,t,n){const o=e=>function(o){const r={};for(let e in o){if(a(e))continue;const s=o[e];r[e]=s===t?n:s}return n.dispatchEvent(e,r)};for(let r of Array.from(e))n._has(r)&&(t[`on${r}`]=o(r))},u=function(e){if(o&&null!=o.createEventObject){const t=o.createEventObject();return t.type=e,t}try{return new Event(e)}catch(t){return{type:e}}},l=function(t){let n={};const o=e=>n[e]||[],r={addEventListener:function(e,t,r){n[e]=o(e),n[e].indexOf(t)>=0||(r=void 0===r?n[e].length:r,n[e].splice(r,0,t))},removeEventListener:function(e,t){if(void 0===e)return void(n={});void 0===t&&(n[e]=[]);const r=o(e).indexOf(t);-1!==r&&o(e).splice(r,1)},dispatchEvent:function(){const n=e(arguments),s=n.shift();t||(n[0]=i(n[0],u(s)),Object.defineProperty(n[0],"target",{writable:!1,value:this}));const a=r[`on${s}`];a&&a.apply(r,n);const c=o(s).concat(o("*"));for(let e=0;e<c.length;e++){c[e].apply(r,n)}},_has:e=>!(!n[e]&&!r[`on${e}`])};return t&&(r.listeners=t=>e(o(t)),r.on=r.addEventListener,r.off=r.removeEventListener,r.fire=r.dispatchEvent,r.once=function(e,t){var n=function(){return r.off(e,n),t.apply(null,arguments)};return r.on(e,n)},r.destroy=()=>n={}),r};var f=function(e,t){switch(typeof e){case"object":return n=e,Object.entries(n).map((([e,t])=>`${e.toLowerCase()}: ${t}`)).join("\r\n");case"string":return function(e,t){const n=e.split("\r\n");null==t&&(t={});for(let e of n)if(/([^:]+):\s*(.+)/.test(e)){const e=null!=RegExp.$1?RegExp.$1.toLowerCase():void 0,n=RegExp.$2;null==t[e]&&(t[e]=n)}return t}(e,t)}var n;return[]};const d=l(!0),p=e=>void 0===e?null:e,h=n.XMLHttpRequest,y=function(){const e=new h,t={};let n,o,a,u=null;var y=0;const v=function(){if(a.status=u||e.status,-1!==u&&(a.statusText=e.statusText),-1===u);else{const t=f(e.getAllResponseHeaders());for(let e in t){const n=t[e];if(!a.headers[e]){const t=e.toLowerCase();a.headers[t]=n}}}},b=function(){x.status=a.status,x.statusText=a.statusText},g=function(){n||x.dispatchEvent("load",{}),x.dispatchEvent("loadend",{}),n&&(x.readyState=0)},E=function(e){for(;e>y&&y<4;)x.readyState=++y,1===y&&x.dispatchEvent("loadstart",{}),2===y&&b(),4===y&&(b(),"text"in a&&(x.responseText=a.text),"xml"in a&&(x.responseXML=a.xml),"data"in a&&(x.response=a.data),"finalUrl"in a&&(x.responseURL=a.finalUrl)),x.dispatchEvent("readystatechange",{}),4===y&&(!1===t.async?g():setTimeout(g,0))},m=function(e){if(4!==e)return void E(e);const n=d.listeners("after");var o=function(){if(n.length>0){const e=n.shift();2===e.length?(e(t,a),o()):3===e.length&&t.async?e(t,a,o):o()}else E(4)};o()};var x=l();t.xhr=x,e.onreadystatechange=function(t){try{2===e.readyState&&v()}catch(e){}4===e.readyState&&(o=!1,v(),function(){if(e.responseType&&"text"!==e.responseType)"document"===e.responseType?(a.xml=e.responseXML,a.data=e.responseXML):a.data=e.response;else{a.text=e.responseText,a.data=e.responseText;try{a.xml=e.responseXML}catch(e){}}"responseURL"in e&&(a.finalUrl=e.responseURL)}()),m(e.readyState)};const w=function(){n=!0};x.addEventListener("error",w),x.addEventListener("timeout",w),x.addEventListener("abort",w),x.addEventListener("progress",(function(t){y<3?m(3):e.readyState<=3&&x.dispatchEvent("readystatechange",{})})),"withCredentials"in e&&(x.withCredentials=!1),x.status=0;for(let e of Array.from(s.concat(r)))x[`on${e}`]=null;if(x.open=function(e,r,s,i,c){y=0,n=!1,o=!1,t.headers={},t.headerNames={},t.status=0,t.method=e,t.url=r,t.async=!1!==s,t.user=i,t.pass=c,a={},a.headers={},m(1)},x.send=function(n){let u,l;for(u of["type","timeout","withCredentials"])l="type"===u?"responseType":u,l in x&&(t[u]=x[l]);t.body=n;const f=d.listeners("before");var p=function(){if(!f.length)return function(){for(u of(c(s,e,x),x.upload&&c(s.concat(r),e.upload,x.upload),o=!0,e.open(t.method,t.url,t.async,t.user,t.pass),["type","timeout","withCredentials"]))l="type"===u?"responseType":u,u in t&&(e[l]=t[u]);for(let n in t.headers){const o=t.headers[n];n&&e.setRequestHeader(n,o)}e.send(t.body)}();const n=function(e){if("object"==typeof e&&("number"==typeof e.status||"number"==typeof a.status))return i(e,a),"data"in e||(e.data=e.response||e.text),void m(4);p()};n.head=function(e){i(e,a),m(2)},n.progress=function(e){i(e,a),m(3)};const d=f.shift();1===d.length?n(d(t)):2===d.length&&t.async?d(t,n):n()};p()},x.abort=function(){u=-1,o?e.abort():x.dispatchEvent("abort",{})},x.setRequestHeader=function(e,n){const o=null!=e?e.toLowerCase():void 0,r=t.headerNames[o]=t.headerNames[o]||e;t.headers[r]&&(n=t.headers[r]+", "+n),t.headers[r]=n},x.getResponseHeader=e=>p(a.headers[e?e.toLowerCase():void 0]),x.getAllResponseHeaders=()=>p(f(a.headers)),e.overrideMimeType&&(x.overrideMimeType=function(){e.overrideMimeType.apply(e,arguments)}),e.upload){let e=l();x.upload=e,t.upload=e}return x.UNSENT=0,x.OPENED=1,x.HEADERS_RECEIVED=2,x.LOADING=3,x.DONE=4,x.response="",x.responseText="",x.responseXML=null,x.readyState=0,x.statusText="",x};y.UNSENT=0,y.OPENED=1,y.HEADERS_RECEIVED=2,y.LOADING=3,y.DONE=4;var v={patch(){h&&(n.XMLHttpRequest=y)},unpatch(){h&&(n.XMLHttpRequest=h)},Native:h,Xhook:y};function b(e,t,n,o){return new(n||(n=Promise))((function(r,s){function a(e){try{c(o.next(e))}catch(e){s(e)}}function i(e){try{c(o.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,i)}c((o=o.apply(e,t||[])).next())}))}const g=n.fetch;function E(e){return e instanceof Headers?m([...e.entries()]):Array.isArray(e)?m(e):e}function m(e){return e.reduce(((e,[t,n])=>(e[t]=n,e)),{})}const x=function(e,t={headers:{}}){let n=Object.assign(Object.assign({},t),{isFetch:!0});if(e instanceof Request){const o=function(e){let t={};return["method","headers","body","mode","credentials","cache","redirect","referrer","referrerPolicy","integrity","keepalive","signal","url"].forEach((n=>t[n]=e[n])),t}(e),r=Object.assign(Object.assign({},E(o.headers)),E(n.headers));n=Object.assign(Object.assign(Object.assign({},o),t),{headers:r,acceptedRequest:!0})}else n.url=e;const o=d.listeners("before"),r=d.listeners("after");return new Promise((function(t,s){let a=t;const i=function(e){if(!r.length)return a(e);const t=r.shift();return 2===t.length?(t(n,e),i(e)):3===t.length?t(n,e,i):i(e)},c=function(e){if(void 0!==e){const n=new Response(e.body||e.text,e);return t(n),void i(n)}u()},u=function(){if(!o.length)return void l();const e=o.shift();return 1===e.length?c(e(n)):2===e.length?e(n,c):void 0},l=()=>b(this,void 0,void 0,(function*(){const{url:t,isFetch:o,acceptedRequest:r}=n,c=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(n,["url","isFetch","acceptedRequest"]);return e instanceof Request&&c.body instanceof ReadableStream&&(c.body=yield new Response(c.body).text()),g(t,c).then((e=>i(e))).catch((function(e){return a=s,i(e),s(e)}))}));u()}))};var w={patch(){g&&(n.fetch=x)},unpatch(){g&&(n.fetch=g)},Native:g,Xhook:x};const O=d;return O.EventEmitter=l,O.before=function(e,t){if(e.length<1||e.length>2)throw"invalid hook";return O.on("before",e,t)},O.after=function(e,t){if(e.length<2||e.length>3)throw"invalid hook";return O.on("after",e,t)},O.enable=function(){v.patch(),w.patch()},O.disable=function(){v.unpatch(),w.unpatch()},O.XMLHttpRequest=v.Native,O.fetch=w.Native,O.headers=f,O.enable(),O}();
    const notifyCSS = `.notifications-container{max-height:100vh;max-width:320px;pointer-events:none;position:fixed;width:100%;z-index:9999}.notifications-container.notify-is-x-center{left:50%;transform:translateX(-50%)}.notifications-container.notify-is-y-center{top:50%;transform:translateY(-50%)}.notifications-container.notify-is-center{left:50%;top:50%;transform:translate(-50%, -50%)}.notifications-container.notify-is-left{left:0}.notifications-container.notify-is-right{right:0}.notifications-container.notify-is-top{top:0}.notifications-container.notify-is-bottom{bottom:0}.notifications-container.notify-is-x-center.notify-is-top{top:var(--distance)}.notifications-container.notify-is-x-center.notify-is-bottom{bottom:var(--distance)}.notifications-container>*{pointer-events:auto}.notify{--notify-error: rgb(235, 87, 87);--notify-error-progress: rgb(192, 69, 69);--notify-success: rgb(111, 207, 151);--notify-success-progress: rgb(84, 170, 120);--notify-warning: rgb(242, 201, 76);--notify-warning-progress: rgb(196, 166, 79);--notify-info: rgb(81, 205, 243);--notify-info-progress: rgb(84, 169, 196);--notify-gray: rgb(51, 51, 51);--notify-gray-2: rgb(77, 77, 77);--notify-gray-3: rgb(130, 130, 130);--notify-white: rgb(255, 255, 255);--notify-white-2: rgba(255, 255, 255, 0.8);--notify-padding: 0.75rem;--notify-icon-size: 32px;--notify-close-icon-size: 16px;align-items:center;border-radius:6px;box-sizing:border-box;display:flex;font-family:-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;overflow:hidden;padding:var(--notify-padding);position:relative;text-decoration:none;transition-timing-function:ease;width:100%}.notify__icon{align-items:center;display:flex;flex-shrink:0;height:var(--notify-icon-size);justify-content:center;margin-right:12px;width:var(--notify-icon-size)}.notify__close{align-items:center;cursor:pointer;display:flex;height:var(--notify-close-icon-size);justify-content:center;position:absolute;right:12px;top:12px;user-select:none;width:var(--notify-close-icon-size)}.notify__close *{pointer-events:none}.notify__title{font-size:1rem;font-weight:600;padding-right:calc(var(--notify-padding) + var(--notify-close-icon-size))}.notify__text{font-size:0.875rem;margin-top:0.25rem}.notify--type-1{background-color:#fff;border:1px solid currentColor}.notify--type-1 .notify__close{color:var(--notify-gray-3)}.notify--type-1 .notify__title{color:var(--notify-gray)}.notify--type-1 .notify__text{color:var(--notify-gray-2)}.notify--type-2{color:var(--notify-gray)}.notify--type-3{color:var(--notify-white)}.notify--type-3 .notify__text{color:var(--notify-white-2)}.notify--error.notify--type-1{box-shadow:0 2px 26px rgba(215,0,0,0.1);color:var(--notify-error)}.notify--error.notify--type-2,.notify--error.notify--type-3{background-color:var(--notify-error)}.notify--warning.notify--type-1{box-shadow:0 2px 26px rgba(242,201,76,0.1);color:var(--notify-warning)}.notify--warning.notify--type-2,.notify--warning.notify--type-3{background-color:var(--notify-warning)}.notify--success.notify--type-1{box-shadow:0 2px 26px rgba(82,215,0,0.1);color:var(--notify-success)}.notify--success.notify--type-2,.notify--success.notify--type-3{background-color:var(--notify-success)}.notify--info.notify--type-1{box-shadow:0 2px 26px rgba(84,175,202,0.1);color:var(--notify-info)}.notify--info.notify--type-2,.notify--info.notify--type-3{background-color:var(--notify-info)}.notify--fade{opacity:0;will-change:opacity}.notify--fadeIn{opacity:1}.notify--slide{opacity:0;will-change:opacity, transform}.notify-is-center .notify--slide,.notify-is-y-center .notify--slide,.notify-is-x-center:not(.notify-is-bottom) .notify--slide{transform:translateY(-20px)}.notify-is-x-center.notify-is-bottom .notify--slide{transform:translateY(20px)}.notify-is-right .notify--slide{transform:translateX(calc(var(--distance) + 110%))}.notify-is-left .notify--slide{transform:translateX(calc((var(--distance) * -1) - 110%))}.notify-is-x-center:not(.notify-is-bottom) .notify--slideIn,.notify-is-center .notify--slideIn,.notify-is-y-center .notify--slideIn,.notify-is-x-center.notify-is-bottom .notify--slideIn{opacity:1;transform:translateY(0)}.notify-is-right .notify--slideIn,.notify-is-left .notify--slideIn{opacity:1;transform:translateX(0)}.notify-is-left .notify{left:var(--distance)}.notify-is-right .notify{right:var(--distance)}.notify-is-top .notify,.notify-is-center .notify,.notify-is-y-center .notify,.notify-is-x-center.notify-is-top .notify{margin-top:var(--gap)}.notify-is-bottom .notify,.notify-is-x-center:not(.notify-is-top) .notify{margin-bottom:var(--gap)}.notify.notify-autoclose{--progress-height: 5px;padding-bottom:calc(var(--notify-padding) + var(--progress-height))}.notify.notify-autoclose::before{animation:progress calc(var(--timeout) * 1ms) linear forwards;bottom:0;content:'';height:var(--progress-height);left:0;position:absolute;transform:scale3d(1, 1, 1);transform-origin:left;width:100%}@keyframes progress{to{transform:scale3d(0, 1, 1)}}.notify.notify-autoclose.notify--error::before{background-color:var(--notify-error-progress)}.notify.notify-autoclose.notify--warning::before{background-color:var(--notify-warning-progress)}.notify.notify-autoclose.notify--success::before{background-color:var(--notify-success-progress)}.notify.notify-autoclose.notify--info::before{background-color:var(--notify-info-progress)}`;
    GM_addStyle(notifyCSS);

    const url = "http://127.0.0.1:8081";
    const pmosUrl = "https://pmos.sx.sgcc.com.cn";

    console.log("curr url:", location.href)
    if (location.href.startsWith(url)) {
        new Notify({status: 'success', title: '检测到申报管理页面！', text: '', autoclose: true, type: 3});
        xhook.after(function (request, response) {
            if (request.url.match(/\/api\/v1\/login\/getCookies/)) {
                if (response.status === 200) {
                    let resp = JSON.parse(response.data);
                    if (resp.code === 1) {
                        GM_setValue('cookies', JSON.stringify(resp.data));
                        new Notify({status: 'success', title: '登录完成！', text: '登录成功，即将切换到交易中心页面', autoclose: true, type: 3});
                        setTimeout(() => {
                            unsafeWindow.open(pmosUrl + '/#/', "_blank");
                        }, 1500);
                    }
                }
            }
        });
    } else if (location.href.startsWith(pmosUrl + '/#/')) {
        xhook.after(function (request, response) {
            if (request.url.match(/\/px-common-authcenter\/auth\/v2\/logout/)) {
                new Notify({status: 'success', title: '退出登录', text: '退出登录', autoclose: true, type: 3});
                GM_setValue('cookies', '[]');
                unsafeWindow.cookieStore.getAll().then(arr => arr.forEach(a => unsafeWindow.cookieStore.delete(a.name)))
            }
        });
        loopCheck(10, 1100, () => document.querySelector(('.login-form-newLogin.login-form')), () => {
            if (document.visibilityState === 'visible') {
                new Notify({status: 'success', title: '检测到登录界面', text: GM_getValue('cookies'), autoclose: true, type: 3});
                let cookies = JSON.parse(GM_getValue('cookies'));
                console.log('cookies', cookies);
                if (cookies && cookies.length > 0) {
                    cookies.forEach((it) => GM_cookie.set(Object.assign(it, it.attributes), (err) => {
                        console.warn(err)
                    }));
                    setTimeout(()=>{
                        GM_setValue('cookies', '[]');
                        location.href = pmosUrl + '/#/dashboard';
                    },1000)
                }
            }
        });
    }

    function loopCheck(count, interval, check, fn) {
        let checkTask = setInterval(() => {
            if (count-- <= 0) {
                clearInterval(checkTask);
                return;
            }
            let result = check();
            if (result !== undefined && result !== null) {
                console.log('result:', result);
                clearInterval(checkTask);
                if (fn) {
                    fn(result);
                }
            }
        }, interval);
    }

})();

