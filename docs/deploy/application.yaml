server:
  port: 8081
logging:
  file:
    name: D:/spot/logs
spring:
  datasource:
    url: jdbc:h2:file:D:/spot/spot;MODE=MySQL;AUTO_SERVER=TRUE;DATABASE_TO_LOWER=TRUE;CACHE_SIZE=65536
custom:
  role:
    text: "H4sIAAAAAAAA/xWMxxEDQQzDWlKWthzF/kvw+cfBAARptoyhplfMwD3kixHplnuxDoDXPnOFZS958iA22ISbJjv0XhnyCMOQQjYKZs75X836oMcwJKELS1B8762N8U9s/Rat3+bKiMV7oWZfHDnon9n6LeUb8DdU3tt+mimu5rCkG0VCDnzMHzHjeoiVT+scaSbQi6Px8gerpLV67gAAAA=="
  pmos:
    httpWaitMilliSecond: 2000 # 每次请求等待间隔（毫秒，正整数）
    provinceCode: sx # 省份编码: {gs: 甘肃, hn: 湖南, jibei: 冀北, jx: 江西, sn: 陕西, sx: 山西}
    queryLimit: true # 省间申报查询限额或者根据装机容量比例 {true: 查询限额, false: 使用装机容量}
    url:
      domain: pmos.sx.sgcc.com.cn # pmos.sx.sgcc.com.cn
      origin: https://pmos.sx.sgcc.com.cn # https://pmos.sx.sgcc.com.cn
    login:
      httpWaitMilliSecond: 500 # 每次请求等待间隔（毫秒，正整数）
      blockPuzzle:
        retry: 5 # 解析验证码尝试次数，取值范围: [0, 10]，0 就关闭登录功能了
      retry: 1 # 登录尝试次数，取值范围: [0, 10]，0 就关闭自动登录功能了
      smsUrl: https://221.122.106.203:443/SPPP-web/api/v1/autoReport
    digest: "61da3ac42c0c7ebb5bc68652b1210c6e52391bcd5fb16ebc2f7bca36a46a523b" # 默认的 p-digest，填写则同时生成 b-digest 和 p-digest
  common:
    defaultHttpTimeout: 180000 # HTTP 请求超时时间：默认 3 分钟
    syncTimeCron: "40 49 * * * ?" # 每小时同步一次时间
  dayRolling: # 日滚动
    detectCron: "0 30 9 * * ?" # 每天 9:30 获取当天开始结束时间
    detectCron2: "0 30 16 * * ?" # 每天 16:30 获取第二天开始结束时间
    queryDishDataCron: "4/5 * * * * ?" # 盯盘数据每 5 秒钟刷新一次
    preparationTimeInSecond: 15 # 提前几秒准备登录
    waitTimeInMillSecond: 2100 # 抢报延迟几毫秒
    info:
      cron: 0 15 23 30 2 ?
      dateCorn: 0 0 8 * * ?  # 查询未来两日的交易时间
      syncUrl: "https://trade.sprixin.com/SPPP-web/api/v1/autoReport/drStrategy/updateDate"
      httpWaitTime: 1000
  opbd: # 省间日前
    startCron: "0 30 9 * * ?" # 每天 9:30 获取当天开始结束时间
    startTime: "11:30:00" # 在此时间之前可以修改配置
    range:
      maxDay: 3 # 最多报几天（仅冀北生效，冀北可以报未来多天，配置此项限制一天可申报的天数）
  opid: # 省间日内
    cron: "10 * * * * ?" # 每分钟检测
    allCron: "0 0 1 * * ?" # 每天这个时间或启动后检测全部申报任务
    range:
      start: 8 # 申报开始时间，根据交易中心限制的结束时间计算（单位：分钟）。例：18:10 分结束，10-8=2 则 18:02 分开始申报
      end: 2 # 申报结束时间，根据交易中心限制的结束时间计算（单位：分钟）。例：18:10 分结束，10-2=8 则 18:08 分结束申报
  profitAnalysis: # 山西省间收益分析
    crawlerCron: "0 35 9 * * ?" # 每天 9:35 爬取数据
    crawlerCron2: "0 35 21 * * ?" # 每天 21:35，爬取次日省间日内电价，用于自动策略
  integrationTrade: # 江西中长期 D-2 日融合交易
  newEnergyTrade: # 江西中长期 D-2 日新能源交易
  imqpNewEnergyTrade: # 江西中长期 月内保量保价新能源交易
    textMatch: "^江西电力市场\\d+年\\d+月保量保价新能源合同转让交易(<span style=\"color:red;margin-left:2px\">\\*</span>)?$" # 文字匹配
    listCron: "0 0 9 15-20 * ?" # 在指定日期的 9:00 获取交易日历
    preparationTimeInSecond: 10 # 提前几秒准备登录
    httpWaitMilliSecond: 100 # 每次请求等待间隔（毫秒，正整数）
