@startuml
!theme plain

<style>
.cloud{
BackgroundColor #bee
}
</style>

skinparam node {
    borderColor #000
    backgroundColor #fff
    fontSize 14
}
skinparam artifact {
    borderColor #000
    backgroundColor #Technology
    fontSize 12
}
skinparam database {
    backgroundColor #6cf
}
skinparam cloud {
    backgroundColor #fc6
}
' 服务端组件
node "Server" as Server {
    artifact "APP" as App
    artifact "FRPS" as Frps
    database "DB" as DB
}

' 代理执行机组件
node "Node" as Node1 {
    artifact "FRPC+SOCKS5" as Frpc1
    artifact "Browser" as Browser1
}
node "Node" as Node2 {
    artifact "FRPC+SOCKS5" as Frpc2
}

' 客户端
node "Client" as Client1 {
    artifact "Browser" as <PERSON>rowser<PERSON>
}
node "Client" as Client2 {
    artifact "Browser" as BrowserB
}

' 外部系统组件
cloud "SGCC" as Sgcc {
}

cloud "TRADE" as Trade {
}

BrowserA -down-> App : http
BrowserB -down-> App : http
Browser1 -up-> App : http

App <-left-> Frps : socks5
Frpc1 <-up-> Frps : tcp
Frpc2 <-up-> Frps : tcp
Node1 <-up-> Sgcc : http
Node2 <-up-> Sgcc : http

App <-right-> DB : R/W
App <-up-> Trade : auth

@enduml