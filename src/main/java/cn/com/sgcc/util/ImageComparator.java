package cn.com.sgcc.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * ImageComparator
 */
@Slf4j
public class ImageComparator {
    @Data
    private static class Options {
        double threshold = 0.1;
        boolean includeAA = false;
        double alpha = 0.1;
        int[] aaColor = {255, 255, 0};
        int[] diffColor = {255, 0, 0};
        int[] diffColorAlt = null;
        boolean diffMask = false;
    }

    /**
     * compare
     *
     * @param img1   img1
     * @param img2   img2
     * @param output output
     * @param width  width
     * @param height height
     * @return int
     */
    public static int compare(byte[] img1, byte[] img2, byte[] output, int width, int height) {
        if (img1.length != img2.length || (output != null && output.length != img1.length)) {
            throw new IllegalArgumentException("Image sizes do not match.");
        }
        if (img1.length != width * height * 4) {
            throw new IllegalArgumentException("Image data size does not match width/height.");
        }
        var options = new Options();
        // check if images are identical
        final int len = width * height;
        final int[] a32 = new int[len];
        final int[] b32 = new int[len];
        boolean identical = true;
        for (int i = 0; i < len; i++) {
            a32[i] = ((img1[i * 4] & 0xFF) << 24)
                     | ((img1[i * 4 + 1] & 0xFF) << 16)
                     | ((img1[i * 4 + 2] & 0xFF) << 8)
                     | (img1[i * 4 + 3] & 0xFF);
            b32[i] = ((img2[i * 4] & 0xFF) << 24)
                     | ((img2[i * 4 + 1] & 0xFF) << 16)
                     | ((img2[i * 4 + 2] & 0xFF) << 8)
                     | (img2[i * 4 + 3] & 0xFF);
            if (a32[i] != b32[i]) {
                identical = false;
                break;
            }
        }
        if (identical) {
            // fast path if identical
            if (output != null && !options.diffMask) {
                for (int i = 0; i < len; i++) {
                    drawGrayPixel(img1, i * 4, options.alpha, output);
                }
            }
            return 0;
        }
        // maximum acceptable square distance between two colors;
        // 35215 is the maximum possible value for the YIQ difference metric
        final double maxDelta = 35215 * options.threshold * options.threshold;
        int diff = 0;
        // compare each pixel of one image against the other one
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                final int pos = (y * width + x) * 4;
                // squared YUV distance between colors at this pixel position, negative if the img2 pixel is darker
                final double delta = colorDelta(img1, img2, pos, pos, false);
                // the color difference is above the threshold
                if (Math.abs(delta) > maxDelta) {
                    // check it's a real rendering difference or just anti-aliasing
                    if (!options.includeAA
                        && (antialiased(img1, x, y, width, height, img2)
                            || antialiased(img2, x, y, width, height, img1))
                    ) {
                        // one of the pixels is anti-aliasing; draw as yellow and do not count as difference
                        // note that we do not include such pixels in a mask
                        if (output != null && !options.diffMask) {
                            drawPixel(output, pos, options.aaColor);
                        }
                    } else {
                        // found substantial difference not caused by anti-aliasing; draw it as such
                        if (output != null) {
                            drawPixel(output, pos, ((delta < 0 && options.diffColorAlt != null)
                                    ? options.diffColorAlt
                                    : options.diffColor));
                        }
                        diff++;
                    }
                } else if (output != null) {
                    // pixels are similar; draw background as grayscale image blended with white
                    if (!options.diffMask) {
                        drawGrayPixel(img1, pos, options.alpha, output);
                    }
                }
            }
        }
        // return the number of different pixels
        return diff;
    }

    /**
     * antialiased
     *
     * @param img    img
     * @param x1     x1
     * @param y1     y1
     * @param width  width
     * @param height height
     * @param img2   img2
     * @return boolean
     */
    private static boolean antialiased(byte[] img, int x1, int y1, int width, int height, byte[] img2) {
        int x0 = Math.max(x1 - 1, 0);
        int y0 = Math.max(y1 - 1, 0);
        int x2 = Math.min(x1 + 1, width - 1);
        int y2 = Math.min(y1 + 1, height - 1);
        int pos = (y1 * width + x1) * 4;
        int zeroes = (x1 == x0 || x1 == x2 || y1 == y0 || y1 == y2) ? 1 : 0;
        double min = 0;
        double max = 0;
        int minX = 0;
        int minY = 0;
        int maxX = 0;
        int maxY = 0;
        for (int x = x0; x <= x2; x++) {
            for (int y = y0; y <= y2; y++) {
                if (x == x1 && y == y1) {
                    continue;
                }
                double delta = colorDelta(img, img, pos, (y * width + x) * 4, true);
                if (delta == 0) {
                    zeroes++;
                    if (zeroes > 2) {
                        return false;
                    }
                } else if (delta < min) {
                    min = delta;
                    minX = x;
                    minY = y;
                } else if (delta > max) {
                    max = delta;
                    maxX = x;
                    maxY = y;
                }
            }
        }
        if (min == 0 || max == 0) {
            return false;
        }
        return ((hasManySiblings(img, minX, minY, width, height)
                 && hasManySiblings(img2, minX, minY, width, height))
                || (hasManySiblings(img, maxX, maxY, width, height)
                    && hasManySiblings(img2, maxX, maxY, width, height)));
    }

    /**
     * hasManySiblings
     *
     * @param img    img
     * @param x1     x1
     * @param y1     y1
     * @param width  width
     * @param height height
     * @return boolean
     */
    private static boolean hasManySiblings(byte[] img, int x1, int y1, int width, int height) {
        int x0 = Math.max(x1 - 1, 0);
        int y0 = Math.max(y1 - 1, 0);
        int x2 = Math.min(x1 + 1, width - 1);
        int y2 = Math.min(y1 + 1, height - 1);
        int pos = (y1 * width + x1) * 4;
        int zeroes = (x1 == x0 || x1 == x2 || y1 == y0 || y1 == y2) ? 1 : 0;
        for (int x = x0; x <= x2; x++) {
            for (int y = y0; y <= y2; y++) {
                if (x == x1 && y == y1) {
                    continue;
                }
                int pos2 = (y * width + x) * 4;
                if (img[pos] == img[pos2] && img[pos + 1] == img[pos2 + 1]
                    && img[pos + 2] == img[pos2 + 2] && img[pos + 3] == img[pos2 + 3]) {
                    zeroes++;
                }
                if (zeroes > 2) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * colorDelta
     *
     * @param img1  img1
     * @param img2  img2
     * @param k     k
     * @param m     m
     * @param yOnly yOnly
     * @return double
     */
    private static double colorDelta(byte[] img1, byte[] img2, int k, int m, boolean yOnly) {
        double r1 = img1[k] & 0xFF;
        double g1 = img1[k + 1] & 0xFF;
        double b1 = img1[k + 2] & 0xFF;
        double a1 = img1[k + 3] & 0xFF;
        double r2 = img2[m] & 0xFF;
        double g2 = img2[m + 1] & 0xFF;
        double b2 = img2[m + 2] & 0xFF;
        double a2 = img2[m + 3] & 0xFF;
        if (a1 == a2 && r1 == r2 && g1 == g2 && b1 == b2) {
            return 0;
        }
        if (a1 < 255) {
            a1 /= 255;
            r1 = blend(r1, a1);
            g1 = blend(g1, a1);
            b1 = blend(b1, a1);
        }
        if (a2 < 255) {
            a2 /= 255;
            r2 = blend(r2, a2);
            g2 = blend(g2, a2);
            b2 = blend(b2, a2);
        }
        double y1 = rgb2y(r1, g1, b1);
        double y2 = rgb2y(r2, g2, b2);
        double y = y1 - y2;
        if (yOnly) {
            return y;
        }
        double i = rgb2i(r1, g1, b1) - rgb2i(r2, g2, b2);
        double q = rgb2q(r1, g1, b1) - rgb2q(r2, g2, b2);
        double delta = 0.5053 * y * y + 0.299 * i * i + 0.1957 * q * q;
        return (y1 > y2) ? -delta : delta;
    }

    /**
     * rgb2y
     *
     * @param r r
     * @param g g
     * @param b b
     * @return double
     */
    private static double rgb2y(double r, double g, double b) {
        return r * 0.29889531 + g * 0.58662247 + b * 0.11448223;
    }

    /**
     * rgb2i
     *
     * @param r r
     * @param g g
     * @param b b
     * @return double
     */
    private static double rgb2i(double r, double g, double b) {
        return r * 0.59597799 - g * 0.2741761 - b * 0.32180189;
    }

    /**
     * rgb2q
     *
     * @param r r
     * @param g g
     * @param b b
     * @return double
     */
    private static double rgb2q(double r, double g, double b) {
        return r * 0.21147017 - g * 0.52261711 + b * 0.31114694;
    }

    /**
     * blend
     *
     * @param c c
     * @param a a
     * @return double
     */
    private static double blend(double c, double a) {
        return 255 + (c - 255) * a;
    }

    /**
     * drawPixel
     *
     * @param output output
     * @param pos    pos
     * @param color  color
     */
    private static void drawPixel(byte[] output, int pos, int[] color) {
        output[pos] = (byte) color[0];
        output[pos + 1] = (byte) color[1];
        output[pos + 2] = (byte) color[2];
        output[pos + 3] = (byte) 255;
    }

    /**
     * drawGrayPixel
     *
     * @param img    img
     * @param i      i
     * @param alpha  alpha
     * @param output output
     */
    private static void drawGrayPixel(byte[] img, int i, double alpha, byte[] output) {
        int r = img[i] & 0xFF;
        int g = img[i + 1] & 0xFF;
        int b = img[i + 2] & 0xFF;
        int val = (int) blend(rgb2y(r, g, b), (alpha * img[i + 3]) / 255.0);
        drawPixel(output, i, new int[]{val, val, val});
    }
}
