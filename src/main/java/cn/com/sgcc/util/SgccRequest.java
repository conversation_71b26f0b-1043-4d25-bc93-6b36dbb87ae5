package cn.com.sgcc.util;

import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.constants.ProvConsts;
import cn.hutool.http.HttpGlobalConfig;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * SgccRequest
 */
@Slf4j
@Component
public class SgccRequest {

    @Value("${custom.common.defaultHttpTimeout}")
    int defaultHttpTimeout;
    @Value("${custom.pmos.httpWaitMilliSecond}")
    long httpWaitMilliSecondDefault;
    @Value("${custom.pmos.provinceCode}")
    String provinceCode;

    /**
     * init
     */
    @PostConstruct
    public void init() {
        log.info("默认 HTTP 请求超时时间: {}ms", defaultHttpTimeout);
        HttpGlobalConfig.setTimeout(defaultHttpTimeout);
    }

    /**
     * getGetRequest
     *
     * @param url       url
     * @param loginInfo loginInfo
     * @return HttpRequest
     */
    public HttpRequest getGetRequest(String url, LoginInfo loginInfo) {
        log.info("username: {}, get url: {}", loginInfo.getUserName(), url);
        HttpRequest httpRequest = HttpRequest.get(ProvConsts.Prov.valueOf(provinceCode).getOrigin() + url);
        waitTime(null);
        return setHeaders(httpRequest, loginInfo);
    }

    /**
     * getPostFormRequest
     *
     * @param url       url
     * @param body      body
     * @param loginInfo loginInfo
     * @return HttpRequest
     */
    public HttpRequest getPostFormRequest(String url, String body, LoginInfo loginInfo) {
        return getPostFormRequest(url, body, loginInfo, null);
    }

    /**
     * getPostFormRequest
     *
     * @param url                 url
     * @param body                body
     * @param loginInfo           loginInfo
     * @param httpWaitMilliSecond httpWaitMilliSecond
     * @return HttpRequest
     */
    public HttpRequest getPostFormRequest(String url, String body, LoginInfo loginInfo, Long httpWaitMilliSecond) {
        return getPostRequest(url, body, loginInfo, httpWaitMilliSecond)
                .header("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
    }

    /**
     * getPostRequest
     *
     * @param url       url
     * @param body      body
     * @param loginInfo loginInfo
     * @return HttpRequest
     */
    public HttpRequest getPostRequest(String url, String body, LoginInfo loginInfo) {
        return getPostRequest(url, body, loginInfo, null);
    }

    /**
     * getPostRequest
     *
     * @param url                 url
     * @param body                body
     * @param loginInfo           loginInfo
     * @param httpWaitMilliSecond httpWaitMilliSecond
     * @return HttpRequest
     */
    public HttpRequest getPostRequest(String url, String body, LoginInfo loginInfo, Long httpWaitMilliSecond) {
        log.info("username: {}, post url: {}, body: {}", loginInfo.getUserName(), url, body);
        HttpRequest httpRequest = HttpRequest.post(ProvConsts.Prov.valueOf(provinceCode).getOrigin() + url).body(body);
        waitTime(httpWaitMilliSecond);
        return setHeaders(httpRequest, loginInfo);
    }

    /**
     * post
     *
     * @param url  url
     * @param body body
     * @return String
     */
    public String post(String url, String body) {
        return post(url, body, defaultHttpTimeout, "{}");
    }

    /**
     * post
     *
     * @param url         url
     * @param body        body
     * @param defaultResp defaultResp
     * @return String
     */
    public String post(String url, String body, String defaultResp) {
        return post(url, body, defaultHttpTimeout, defaultResp);
    }

    /**
     * post
     *
     * @param url     url
     * @param body    body
     * @param timeout timeout
     * @return String
     */
    public String post(String url, String body, int timeout) {
        return post(url, body, timeout, "{}");
    }

    /**
     * post
     *
     * @param url         url
     * @param body        body
     * @param timeout     timeout
     * @param defaultResp defaultResp
     * @return String
     */
    public String post(String url, String body, int timeout, String defaultResp) {
        log.info("POST url: {}, body: {}, timeout: {}", url, body, timeout);
        var origin = ProvConsts.Prov.valueOf(provinceCode).getOrigin();
        try (var response = HttpRequest.post(origin + url).body(body).timeout(timeout).execute()) {
            return response.body();
        } catch (Exception e) {
            return defaultResp;
        }
    }

    /**
     * get
     *
     * @param url url
     * @return String
     */
    public String get(String url) {
        return get(url, defaultHttpTimeout, "{}");
    }

    /**
     * get
     *
     * @param url         url
     * @param defaultResp defaultResp
     * @return String
     */
    public String get(String url, String defaultResp) {
        return get(url, defaultHttpTimeout, defaultResp);
    }

    /**
     * get
     *
     * @param url     url
     * @param timeout timeout
     * @return String
     */
    public String get(String url, int timeout) {
        return get(url, timeout, "{}");
    }

    /**
     * get
     *
     * @param url         url
     * @param timeout     timeout
     * @param defaultResp defaultResp
     * @return String
     */
    public String get(String url, int timeout, String defaultResp) {
        log.info("GET url: {}, timeout: {}", url, timeout);
        var origin = ProvConsts.Prov.valueOf(provinceCode).getOrigin();
        try (var response = HttpRequest.get(origin + url).timeout(timeout).execute()) {
            return response.body();
        } catch (Exception e) {
            return defaultResp;
        }
    }

    /**
     * setHeaders
     *
     * @param httpRequest httpRequest
     * @param loginInfo   loginInfo
     * @return HttpRequest
     */
    private HttpRequest setHeaders(HttpRequest httpRequest, LoginInfo loginInfo) {
        if (loginInfo.getTokenStack().size() < 5) {
            try {
                updateGatewayToken(loginInfo);
            } catch (Exception e) {
                log.warn("更新 token 失败", e);
            }
        }
        return httpRequest
                .header("Accept", "application/json, text/plain, */*")
                .header("Accept-Language", "zh-CN,zh;q=0.9")
                .header("Clienttag", "OUTNET_BROWSE")
                .header("Content-Type", "application/json;charset=UTF-8")
                .header("Currentroute", "/dashboard")
                .header("Origin", ProvConsts.Prov.valueOf(provinceCode).getOrigin())
                .header("Referer", ProvConsts.Prov.valueOf(provinceCode).getOrigin() + "/")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                                      + "AppleWebKit/537.36 (KHTML, like Gecko) "
                                      + "Chrome/120.0.0.0 Safari/537.36")
                .header("X-Ticket", loginInfo.getTicket())
                .header("X-Token", loginInfo.getTokenStack().pop().getToken());
    }

    /**
     * waitTime
     *
     * @param httpWaitMilliSecond httpWaitMilliSecond
     */
    private void waitTime(Long httpWaitMilliSecond) {
        try {
            Thread.sleep(httpWaitMilliSecond == null ? httpWaitMilliSecondDefault : httpWaitMilliSecond);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("异常中断", e);
        }
    }

    /**
     * updateGatewayToken
     *
     * @param loginInfo loginInfo
     */
    public void updateGatewayToken(LoginInfo loginInfo) {
        var respStr = post("/px-gateway-Token/batchGetPxGatewayToken", JSONObject.of(
                "clientTag", "OUTNET_BROWSE",
                "ticket", loginInfo.getTicket()
        ).toString());
        log.info("批量获取 token 结果: {}", respStr.substring(0, 50));
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.ok(sgccResp)) {
            loginInfo.getTokenStack().clear();
            var tokenData = new ArrayList<LoginInfo.TokenData>();
            var array = SgccResp.arrayData(sgccResp);
            array.forEach(o -> {
                var jsonObject = (JSONObject) o;
                tokenData.add(new LoginInfo.TokenData(
                        jsonObject.getString("token"), jsonObject.getLongValue("validStartTime"),
                        jsonObject.getLongValue("validEndTime")));
            });
            for (LoginInfo.TokenData tokenDatum : tokenData) {
                loginInfo.getTokenStack().push(tokenDatum);
            }
        } else {
            throw new RuntimeException("批量获取 token 失败: " + respStr);
        }
    }
}
