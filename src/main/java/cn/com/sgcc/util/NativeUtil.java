package cn.com.sgcc.util;

public class NativeUtil {
    private static final String NATIVE_IMAGE_CODE_PROPERTY = "org.graalvm.nativeimage.imagecode";

    /**
     * 判断当前程序是否作为 GraalVM Native Image 运行。
     * <p>
     * 这是最推荐的方法，因为它简单、无依赖，并且是 GraalVM 官方设计的一部分。
     * GraalVM 在构建原生镜像时会设置 "org.graalvm.nativeimage.imagecode" 这个系统属性。
     *
     * @return 如果是 Native Image 环境则返回 true，否则返回 false。
     */
    public static boolean isNative() {
        return System.getProperty(NATIVE_IMAGE_CODE_PROPERTY) != null;
    }

    /**
     * 判断当前程序是否运行在 GraalVM 的 JIT 模式下（即作为普通 JVM 使用）。
     * <p>
     * 这与 isNative() 是互斥的。一个程序要么是 JIT 模式，要么是 Native 模式。
     *
     * @return 如果是 GraalVM JIT 环境则返回 true，否则返回 false。
     */
    public static boolean isRunningOnGraalVMJIT() {
        // GraalVM 的 JIT 模式下，java.vm.name 属性通常包含 "GraalVM"
        String vmName = System.getProperty("java.vm.name");
        return vmName != null && vmName.toLowerCase().contains("graalvm");
    }
}
