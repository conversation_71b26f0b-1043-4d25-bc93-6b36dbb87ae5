package cn.com.sgcc.util;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;

/**
 * LockUtil
 */
@Slf4j
@SuppressWarnings("UnusedReturnValue")
public class LockUtil {

    private static final ConcurrentHashMap<String, ReentrantLock> LOCK_MAP = new ConcurrentHashMap<>();

    /**
     * lock
     *
     * @param name     name
     * @param consumer consumer
     */
    public static void lock(String name, Consumer<String> consumer) {
        lock(cn.hutool.core.thread.lock.LockUtil.class, name, consumer);
    }

    /**
     * lock
     *
     * @param clazz    clazz
     * @param name     name
     * @param consumer consumer
     */
    public static void lock(Class<?> clazz, String name, Consumer<String> consumer) {
        lock(clazz, name, true, lockKey -> {
            consumer.accept(lockKey);
            return null;
        });
    }

    /**
     * lock
     *
     * @param name     name
     * @param function function
     * @param <T>      T
     * @return T
     */
    public static <T> T lock(String name, Function<String, T> function) {
        return lock(cn.hutool.core.thread.lock.LockUtil.class, name, function);
    }

    /**
     * lock
     *
     * @param clazz    clazz
     * @param name     name
     * @param function function
     * @param <T>      T
     * @return T
     */
    public static <T> T lock(Class<?> clazz, String name, Function<String, T> function) {
        return lock(clazz, name, true, function);
    }

    /**
     * lock
     *
     * @param clazz    clazz
     * @param name     name
     * @param fair     fair
     * @param function function
     * @param <T>      T
     * @return T
     */
    public static <T> T lock(Class<?> clazz, String name, boolean fair, Function<String, T> function) {
        var lockKey = clazz.getName() + "_" + name;
        var lock = LOCK_MAP.computeIfAbsent(lockKey, k -> new ReentrantLock(fair));
        lock.lock();
        try {
            return function.apply(lockKey);
        } finally {
            lock.unlock();
        }
    }

    /**
     * tryLock
     *
     * @param name     name
     * @param duration duration
     * @param consumer consumer
     */
    public static void tryLock(String name, Duration duration, Consumer<Boolean> consumer) {
        tryLock(name, duration, true, consumer);
    }

    /**
     * tryLock
     *
     * @param name     name
     * @param duration duration
     * @param fair     fair
     * @param consumer consumer
     */
    public static void tryLock(String name, Duration duration, boolean fair, Consumer<Boolean> consumer) {
        tryLock(cn.hutool.core.thread.lock.LockUtil.class, name, duration, fair, ok -> {
            consumer.accept(ok);
            return null;
        });
    }

    /**
     * tryLock
     *
     * @param clazz    clazz
     * @param name     name
     * @param duration duration
     * @param consumer consumer
     */
    public static void tryLock(Class<?> clazz, String name, Duration duration, Consumer<Boolean> consumer) {
        tryLock(clazz, name, duration, true, consumer);
    }

    /**
     * tryLock
     *
     * @param clazz    clazz
     * @param name     name
     * @param duration duration
     * @param fair     fair
     * @param consumer consumer
     */
    public static void tryLock(Class<?> clazz, String name, Duration duration, boolean fair,
                               Consumer<Boolean> consumer) {
        tryLock(clazz, name, duration, fair, ok -> {
            consumer.accept(ok);
            return null;
        });
    }

    /**
     * tryLock
     *
     * @param clazz    clazz
     * @param name     name
     * @param duration duration
     * @param fair     fair
     * @param function function
     * @param <T>      T
     * @return T
     */
    public static <T> T tryLock(Class<?> clazz, String name, Duration duration, boolean fair,
                                Function<Boolean, T> function) {
        var lock = LOCK_MAP.computeIfAbsent(clazz.getName() + "_" + name, k -> new ReentrantLock(fair));
        var locked = false;
        try {
            locked = lock.tryLock(duration.getSeconds(), TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("异常中断", e);
        }
        try {
            return function.apply(locked);
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }
}
