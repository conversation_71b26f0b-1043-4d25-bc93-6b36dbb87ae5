package cn.com.sgcc.util;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

/**
 * SgccJxResp
 */
public class SgccJxResp {
    /**
     * ok
     *
     * @param jsonObject jsonObject
     * @return boolean
     */
    public static boolean ok(JSONObject jsonObject) {
        var code = jsonObject.getInteger("code");
        return code != null && code == 200 && "Success".equals(jsonObject.getString("message"));
    }

    /**
     * fail
     *
     * @param jsonObject jsonObject
     * @return boolean
     */
    public static boolean fail(JSONObject jsonObject) {
        return !ok(jsonObject);
    }

    /**
     * longData
     *
     * @param jsonObject jsonObject
     * @return Long
     */
    public static Long longData(JSONObject jsonObject) {
        return jsonObject.getLong("data");
    }

    /**
     * jsonData
     *
     * @param jsonObject jsonObject
     * @return JSONObject
     */
    public static JSONObject jsonData(JSONObject jsonObject) {
        return jsonObject.containsKey("data") ? jsonObject.getJSONObject("data") : new JSONObject();
    }

    /**
     * arrayData
     *
     * @param jsonObject jsonObject
     * @return JSONArray
     */
    public static JSONArray arrayData(JSONObject jsonObject) {
        return jsonObject.containsKey("data") ? jsonObject.getJSONArray("data") : new JSONArray();
    }
}
