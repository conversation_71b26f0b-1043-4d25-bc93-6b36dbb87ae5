package cn.com.sgcc.util.http;

import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.constants.ProvConsts;
import cn.com.sgcc.util.SgccResp;
import cn.hutool.crypto.SmUtil;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import javax.net.ssl.SSLContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.DefaultHttpRequestRetryStrategy;
import org.apache.hc.client5.http.impl.classic.BasicHttpClientResponseHandler;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactoryBuilder;
import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 请求工具
 */
@Slf4j
@Component
public class NewSgccRequest {
    @Value("${custom.common.defaultHttpTimeout}")
    int defaultHttpTimeout;
    @Value("${custom.pmos.httpWaitMilliSecond}")
    long httpWaitMilliSecondDefault;
    @Value("${custom.pmos.provinceCode}")
    String provinceCode;
    @Value("${custom.pmos.digest}")
    String digest;
    CloseableHttpClient client;

    static {
        System.setProperty("java.net.preferIPv4Stack", "true");
    }

    /**
     * init
     */
    @PostConstruct
    public void init() {
        SSLContext sslContext = null;
        try {
            sslContext = SSLContextBuilder.create()
                                          .loadTrustMaterial(TrustAllStrategy.INSTANCE)
                                          .build();
        } catch (Exception e) {
            log.error("loadTrustMaterial失败", e);
        }
        var socketFactory = SSLConnectionSocketFactoryBuilder.create()
                                                             .setSslContext(sslContext)
                                                             .build();
        var connectionManager = PoolingHttpClientConnectionManagerBuilder.create()
                                                                         .setMaxConnTotal(256)
                                                                         .setSSLSocketFactory(socketFactory)
                                                                         .build();
        var requestConfig = RequestConfig.custom()
                                         .setConnectionRequestTimeout(Timeout.ofMilliseconds(defaultHttpTimeout))
                                         .setResponseTimeout(Timeout.ofMilliseconds(defaultHttpTimeout))
                                         .build();
        client = HttpClients.custom()
                            .setConnectionManager(connectionManager)
                            .setDefaultRequestConfig(requestConfig)
                            .evictIdleConnections(TimeValue.ofSeconds(5))
                            .setKeepAliveStrategy((response, context) -> TimeValue.ofSeconds(10))
                            .setRetryStrategy(DefaultHttpRequestRetryStrategy.INSTANCE)
                            .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                                          + "AppleWebKit/537.36 (KHTML, like Gecko) "
                                          + "Chrome/137.0.0.0 Safari/537.36")
                            .build();
    }

    /**
     * post 请求
     *
     * @param path      请求路径
     * @param body      请求内容
     * @param reqConfig 配置
     * @return 结果
     */
    public String post(String path, JSONObject body, ReqConfig reqConfig) {
        waitTime(reqConfig.getWaitTime());
        var bodyStr = body.toString();
        var loginInfo = reqConfig.getLoginInfo();
        log.info("path: {}, username: {}, body: {}", path, loginInfo == null ? "" : loginInfo.getUserName(), bodyStr);
        try {
            var httpPost = new HttpPost(ProvConsts.Prov.valueOf(provinceCode).getOrigin() + path);
            getLoginHeaders(loginInfo).forEach(httpPost::setHeader);
            if (StringUtils.isNotBlank(digest)) {
                httpPost.setHeader("B-Digest", SmUtil.sm3(bodyStr));
                httpPost.setHeader("P-Digest", digest);
            }
            if (reqConfig.getCurrentRoute() != null) {
                httpPost.setHeader("Currentroute", reqConfig.getCurrentRoute());
            }
            httpPost.setEntity(new StringEntity(bodyStr));
            try {
                return client.execute(httpPost, new BasicHttpClientResponseHandler());
            } catch (Exception e) {
                Arrays.stream(httpPost.getHeaders()).forEach(it -> {
                    log.info("headers, k: {}, v: {}", it.getName(), it.getValue());
                });
                throw e;
            }
        } catch (Exception e) {
            log.error("调用接口失败", e);
            return null;
        }
    }

    /**
     * post
     *
     * @param path path
     * @param body body
     * @return String
     */
    public String post(String path, JSONObject body) {
        return post(path, body, new ReqConfig());
    }

    /**
     * getLoginHeaders
     *
     * @param loginInfo loginInfo
     * @return Map<String, String>
     */
    private Map<String, String> getLoginHeaders(LoginInfo loginInfo) {
        var headers = new HashMap<>(Map.of("Accept", "application/json, text/plain, */*",
            "Accept-Language", "zh-CN,zh;q=0.9",
            "Clienttag", "OUTNET_BROWSE",
            "Content-Type", "application/json;charset=UTF-8",
            "Currentroute", "/dashboard",
            "Origin", ProvConsts.Prov.valueOf(provinceCode).getOrigin(),
            "Referer", ProvConsts.Prov.valueOf(provinceCode).getOrigin() + "/"));
        if (loginInfo != null) {
            if (loginInfo.getTokenStack().size() < 5) {
                try {
                    updateGatewayToken(loginInfo);
                } catch (Exception e) {
                    log.warn("更新 token 失败", e);
                }
            }
            headers.put("X-Ticket", loginInfo.getTicket());
            headers.put("X-Token", loginInfo.getTokenStack().pop().getToken());
        }
        return headers;
    }

    /**
     * waitTime
     *
     * @param httpWaitMilliSecond httpWaitMilliSecond
     */
    private void waitTime(Long httpWaitMilliSecond) {
        try {
            Thread.sleep(httpWaitMilliSecond == null ? httpWaitMilliSecondDefault : httpWaitMilliSecond);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("异常中断", e);
        }
    }

    /**
     * 更新 Token
     *
     * @param loginInfo 登录信息
     */
    public void updateGatewayToken(LoginInfo loginInfo) {
        var respStr = post("/px-gateway-Token/batchGetPxGatewayToken", JSONObject.of(
            "clientTag", "OUTNET_BROWSE",
            "ticket", loginInfo.getTicket()
        ));
        log.info("批量获取 token 结果: {}", respStr.substring(0, 50));
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.ok(sgccResp)) {
            loginInfo.getTokenStack().clear();
            var tokenData = new ArrayList<LoginInfo.TokenData>();
            var array = SgccResp.arrayData(sgccResp);
            array.forEach(o -> {
                var jsonObject = (JSONObject) o;
                tokenData.add(new LoginInfo.TokenData(
                    jsonObject.getString("token"),
                    jsonObject.getLongValue("validStartTime"),
                    jsonObject.getLongValue("validEndTime")
                ));
            });
            for (LoginInfo.TokenData tokenDatum : tokenData) {
                loginInfo.getTokenStack().push(tokenDatum);
            }
        } else {
            throw new RuntimeException("批量获取 token 失败: " + respStr);
        }

    }
}
