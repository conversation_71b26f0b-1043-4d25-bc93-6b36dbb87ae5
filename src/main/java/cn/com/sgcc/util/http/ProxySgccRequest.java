package cn.com.sgcc.util.http;

import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.constants.ProvConsts;
import cn.com.sgcc.util.SgccResp;
import cn.hutool.crypto.SmUtil;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Objects;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 使用Java原生API构建，以支持安全的、动态的SOCKS5代理。
 * 此类旨在作为 NewSgccRequest 的直接、安全的替代品，并保证功能对等。
 */
@Slf4j
@Component
public class ProxySgccRequest {

    @Value("${custom.common.defaultHttpTimeout}")
    private int defaultHttpTimeout;
    @Value("${custom.pmos.httpWaitMilliSecond}")
    private long httpWaitMilliSecondDefault;
    @Value("${custom.pmos.provinceCode}")
    String provinceCode;
    @Value("${custom.pmos.digest}")
    private String digest;

    private static final String USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                                             + "AppleWebKit/537.36 (KHTML, like Gecko) "
                                             + "Chrome/********* Safari/537.36";
    private static final String TLS_PROTOCOL = "TLS";

    private SSLContext trustAllSslContext;

    static {
        // 保持与旧实现兼容，优先使用IPv4。
        System.setProperty("java.net.preferIPv4Stack", "true");
    }

    /**
     * 初始化服务，创建一个可重用的、信任所有SSL证书的SSLContext。
     * 这对于与使用自签名或无效证书的内部系统进行HTTPS通信是必需的。
     *
     * @throws RuntimeException 如果SSLContext初始化失败，将阻止应用启动。
     */
    @PostConstruct
    public void init() {
        TrustManager[] trustAllCerts = new TrustManager[]{
            new X509TrustManager() {
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }

                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                }

                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                }
            }
        };

        try {
            trustAllSslContext = SSLContext.getInstance(TLS_PROTOCOL);
            trustAllSslContext.init(null, trustAllCerts, new SecureRandom());
        } catch (GeneralSecurityException e) {
            log.error("关键错误：无法初始化信任所有证书的SSLContext。服务将无法正常工作。", e);
            throw new RuntimeException("无法初始化信任所有证书的SSLContext", e);
        }
    }

    /**
     * 发送GET请求的主方法。
     *
     * @param path      请求的API路径，应包含所有查询参数 (e.g., "/px-gateway-Token/getInfo?id=123")
     * @param reqConfig 本次请求的完整配置，包括代理和登录信息
     * @return 成功时返回服务器响应体字符串；发生任何错误则返回null。
     */
    public String get(String path, ReqConfig reqConfig) {
        waitTime(reqConfig.getWaitTime());
        final String fullUrl = ProvConsts.Prov.valueOf(provinceCode).getOrigin() + path;

        log.info("--> GET: {}, Proxy: {}, Username: {}",
            path,
            StringUtils.defaultIfBlank(reqConfig.getProxy(), "None"),
            reqConfig.getLoginInfo() == null ? "Anonymous" : reqConfig.getLoginInfo().getUserName());

        HttpURLConnection connection = null;
        try {
            // 1. 创建并配置连接（代理, SSL）
            connection = createConnection(fullUrl, reqConfig.getProxy());
            // 2. 配置GET请求（方法, 超时, 头部）
            configureGetRequest(connection, reqConfig);
            // 3. GET请求没有请求体，直接读取响应
            return readResponse(connection);

        } catch (Exception e) {
            log.error("<-- FAILED GET: {}. Error: {}", fullUrl, e.getMessage(), e);
            return null; // 遵循旧实现的约定，在失败时返回null
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 发送POST请求的主方法。
     * 这是一个高级别的流程编排方法，将具体实现委托给私有辅助方法。
     *
     * @param path      请求的API路径 (e.g., "/px-gateway-Token/getToken")
     * @param body      要发送的JSON请求体
     * @param reqConfig 本次请求的完整配置，包括代理和登录信息
     * @return 成功时返回服务器响应体字符串；发生任何错误则返回null，以保持与旧实现的兼容性。
     */
    public String post(String path, JSONObject body, ReqConfig reqConfig) {
        waitTime(reqConfig.getWaitTime());
        final String bodyStr = body.toString();
        final String fullUrl = ProvConsts.Prov.valueOf(provinceCode).getOrigin() + path;

        log.info("--> POST: {}, Proxy: {}, Username: {}, Body: {}",
            path,
            StringUtils.defaultIfBlank(reqConfig.getProxy(), "None"),
            reqConfig.getLoginInfo() == null ? "Anonymous" : reqConfig.getLoginInfo().getUserName(),
            bodyStr);

        HttpURLConnection connection = null;
        try {
            // 1. 创建并配置连接（代理, SSL）
            connection = createConnection(fullUrl, reqConfig.getProxy());
            // 2. 配置POST请求（方法, 超时, 头部）
            configurePostRequest(connection, bodyStr, reqConfig);
            // 3. 写入请求体
            writeRequestBody(connection, bodyStr);
            // 4. 读取并返回响应
            return readResponse(connection);

        } catch (Exception e) {
            log.error("<-- FAILED POST: {}. Error: {}", fullUrl, e.getMessage(), e);
            return null; // 遵循旧实现的约定，在失败时返回null
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 一个为方便起见提供的重载方法，用于发送不带任何特殊配置的POST请求。
     *
     * @param path 请求的API路径
     * @param body 要发送的JSON请求体
     * @return 服务器响应或null
     */
    public String post(String path, JSONObject body) {
        return post(path, body, new ReqConfig());
    }

    // ===================================================================================
    // ============================ PRIVATE HELPER METHODS ===============================
    // ===================================================================================

    /**
     * 创建一个HttpURLConnection，并根据需要配置SOCKS代理和SSL工厂。
     *
     * @param urlString    urlString
     * @param proxyAddress proxyAddress
     * @return HttpURLConnection
     * @throws Exception Exception
     */
    private HttpURLConnection createConnection(String urlString, String proxyAddress) throws Exception {
        Proxy proxy = Proxy.NO_PROXY;
        if (StringUtils.isNotBlank(proxyAddress)) {
            // 解析代理字符串 "host:port"
            String[] parts = proxyAddress.replaceFirst("(?i)socks5://", "").split(":");
            if (parts.length != 2) {
                throw new IllegalArgumentException("代理地址格式无效: " + proxyAddress);
            }
            String host = parts[0];
            int port = Integer.parseInt(parts[1]);

            // !!! 核心安全机制 !!!
            // 创建一个未解析的Socket地址。这会强制JVM将主机名直接传递给SOCKS代理，
            // 由代理服务器在远端执行DNS查询，从而完全避免本地DNS泄漏。
            InetSocketAddress unresolvedAddress = InetSocketAddress.createUnresolved(host, port);
            proxy = new Proxy(Proxy.Type.SOCKS, unresolvedAddress);
        }

        URL url = URI.create(urlString).toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection(proxy);

        if (connection instanceof HttpsURLConnection httpsConn) {
            httpsConn.setSSLSocketFactory(trustAllSslContext.getSocketFactory());
            // 允许所有主机名，模拟Apache HttpClient的TrustAllStrategy
            httpsConn.setHostnameVerifier((_, _) -> true);
        }
        return connection;
    }

    /**
     * 配置GET请求的参数，包括方法、超时和所有业务相关的HTTP头部。
     *
     * @param connection 连接实例
     * @param reqConfig  请求配置
     */
    private void configureGetRequest(HttpURLConnection connection, ReqConfig reqConfig) throws Exception {
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(defaultHttpTimeout);
        connection.setReadTimeout(defaultHttpTimeout);
        connection.setDoOutput(false); // GET请求没有请求体

        // 设置标准头部
        connection.setRequestProperty("Accept", "application/json, text/plain, */*");
        connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9");
        connection.setRequestProperty("User-Agent", USER_AGENT);
        connection.setRequestProperty("Origin", ProvConsts.Prov.valueOf(provinceCode).getOrigin());
        connection.setRequestProperty("Referer", ProvConsts.Prov.valueOf(provinceCode).getOrigin() + "/");

        // 设置业务特定的头部
        connection.setRequestProperty("Clienttag", "OUTNET_BROWSE");
        var currentRoute = StringUtils.defaultIfBlank(reqConfig.getCurrentRoute(), "/dashboard");
        connection.setRequestProperty("Currentroute", currentRoute);

        // 设置认证相关的头部
        if (reqConfig.getLoginInfo() != null) {
            setAuthenticationHeaders(connection, reqConfig);
        }

        // 自定义 header
        if (!reqConfig.getHeaders().isEmpty()) {
            reqConfig.getHeaders().forEach(connection::setRequestProperty);
        }
    }

    /**
     * 配置连接的请求参数，包括方法、超时和所有业务相关的HTTP头部。
     *
     * @param connection connection
     * @param bodyStr    bodyStr
     * @param reqConfig  reqConfig
     * @throws Exception Exception
     */
    private void configurePostRequest(HttpURLConnection connection, String bodyStr,
                                      ReqConfig reqConfig) throws Exception {
        connection.setRequestMethod("POST");
        connection.setConnectTimeout(defaultHttpTimeout);
        connection.setReadTimeout(defaultHttpTimeout);
        connection.setDoOutput(true); // 必须为true才能写入请求体

        // 设置标准头部
        connection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
        connection.setRequestProperty("Accept", "application/json, text/plain, */*");
        connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9");
        connection.setRequestProperty("User-Agent", USER_AGENT);
        connection.setRequestProperty("Origin", ProvConsts.Prov.valueOf(provinceCode).getOrigin());
        connection.setRequestProperty("Referer", ProvConsts.Prov.valueOf(provinceCode).getOrigin() + "/");

        // 设置业务特定的头部
        connection.setRequestProperty("Clienttag", "OUTNET_BROWSE");
        var currentRoute = StringUtils.defaultIfBlank(reqConfig.getCurrentRoute(), "/dashboard");
        connection.setRequestProperty("Currentroute", currentRoute);

        // 设置摘要头部
        if (StringUtils.isNotBlank(digest)) {
            connection.setRequestProperty("B-Digest", SmUtil.sm3(bodyStr));
            connection.setRequestProperty("P-Digest", digest);
        }

        // 设置认证相关的头部
        if (reqConfig.getLoginInfo() != null) {
            setAuthenticationHeaders(connection, reqConfig);
        }

        // 自定义 header
        if (!reqConfig.getHeaders().isEmpty()) {
            reqConfig.getHeaders().forEach(connection::setRequestProperty);
        }
    }

    /**
     * 处理认证相关的头部，包括在需要时自动刷新Token。
     *
     * @param connection connection
     * @param reqConfig  reqConfig
     */
    private void setAuthenticationHeaders(HttpURLConnection connection, ReqConfig reqConfig) {
        LoginInfo loginInfo = reqConfig.getLoginInfo();
        // 如果Token即将耗尽，则自动刷新。
        // 关键点：将原始的reqConfig传递下去，确保Token刷新也使用相同的代理配置。
        if (loginInfo.getTokenStack().size() < 5) {
            try {
                log.debug("Token数量不足，尝试自动刷新...");
                updateGatewayToken(loginInfo, reqConfig);
                log.info("Token刷新成功，新的Token数量: {}", loginInfo.getTokenStack().size());
            } catch (Exception e) {
                // 如果刷新失败，仅记录警告，主请求可能依然可以凭剩余的Token成功。
                log.warn("自动刷新Token失败，将继续使用剩余Token。错误: {}", e.getMessage());
            }
        }

        connection.setRequestProperty("X-Ticket", loginInfo.getTicket());
        if (!loginInfo.getTokenStack().isEmpty()) {
            connection.setRequestProperty("X-Token", loginInfo.getTokenStack().pop().getToken());
        } else {
            log.warn("无法设置'X-Token'头部，因为Token堆栈为空。");
        }
    }

    /**
     * 自动刷新网关Token的内部实现。
     *
     * @param loginInfo 需要更新的登录信息对象
     * @param reqConfig 原始请求的配置，用于确保此请求也通过正确的代理
     * @throws RuntimeException 如果Token刷新请求失败
     */
    public void updateGatewayToken(LoginInfo loginInfo, ReqConfig reqConfig) {
        log.info("正在为用户 '{}' 刷新Token...", loginInfo.getUserName());
        var respStr = post("/px-gateway-Token/batchGetPxGatewayToken", JSONObject.of(
            "clientTag", "OUTNET_BROWSE",
            "ticket", loginInfo.getTicket()
        ), reqConfig); // 传递原始配置，以保持代理一致性

        if (StringUtils.isBlank(respStr)) {
            throw new RuntimeException("批量获取Token失败: 服务器响应为空");
        }

        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.ok(sgccResp)) {
            loginInfo.getTokenStack().clear();
            var tokenDataList = new ArrayList<LoginInfo.TokenData>();
            SgccResp.arrayData(sgccResp).forEach(o -> {
                var jsonObject = (JSONObject) o;
                tokenDataList.add(new LoginInfo.TokenData(
                    jsonObject.getString("token"),
                    jsonObject.getLongValue("validStartTime"),
                    jsonObject.getLongValue("validEndTime")
                ));
            });
            loginInfo.getTokenStack().addAll(tokenDataList);
        } else {
            throw new RuntimeException("批量获取Token失败: " + respStr);
        }
    }

    /**
     * 将请求体写入连接的输出流。
     *
     * @param connection connection
     * @param bodyStr    bodyStr
     * @throws Exception Exception
     */
    private void writeRequestBody(HttpURLConnection connection, String bodyStr) throws Exception {
        try (OutputStream os = connection.getOutputStream()) {
            os.write(bodyStr.getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 读取服务器的响应，处理成功和失败的情况。
     *
     * @param connection connection
     * @return String
     * @throws Exception Exception
     */
    private String readResponse(HttpURLConnection connection) throws Exception {
        int responseCode = connection.getResponseCode();
        boolean isSuccess = responseCode >= 200 && responseCode < 300;

        // 根据成功或失败状态，选择合适的输入流
        try (InputStream stream = isSuccess ? connection.getInputStream() : connection.getErrorStream()) {
            StringBuilder responseBody = new StringBuilder();
            if (stream != null) {
                try (BufferedReader br = new BufferedReader(new InputStreamReader(stream, StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        responseBody.append(line);
                    }
                }
            }

            log.info("<-- {} {} | Body: {}", responseCode, connection.getResponseMessage(),
                responseBody.length() > 200 ? responseBody.substring(0, 200) + "..." : responseBody);

            if (isSuccess) {
                return responseBody.toString();
            } else {
                throw new ConnectException("HTTP请求失败，状态码: " + responseCode + ", 响应: " + responseBody);
            }
        }
    }

    /**
     * 根据配置执行线程等待。
     *
     * @param customWaitTime customWaitTime
     */
    private void waitTime(Long customWaitTime) {
        long effectiveWaitTime = Objects.requireNonNullElse(customWaitTime, httpWaitMilliSecondDefault);
        if (effectiveWaitTime > 0) {
            try {
                Thread.sleep(effectiveWaitTime);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("请求前的等待被中断。", e);
            }
        }
    }
}