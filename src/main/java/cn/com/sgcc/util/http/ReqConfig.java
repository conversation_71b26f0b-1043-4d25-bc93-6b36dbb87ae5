package cn.com.sgcc.util.http;

import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.system.model.SystemAccountConfig;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;

/**
 * ReqConfig
 */
@Data
public class ReqConfig {

    private SystemAccountConfig account = null;
    private LoginInfo loginInfo = null;
    private String currentRoute = null;
    private Long waitTime = null;
    private Map<String, String> headers = new HashMap<>();

    /**
     * of
     *
     * @param account      account
     * @param loginInfo    loginInfo
     * @param currentRoute currentRoute
     * @param waitTime     waitTime
     * @return ReqConfig
     */
    public static ReqConfig of(SystemAccountConfig account, LoginInfo loginInfo, String currentRoute, Long waitTime) {
        var reqConfig = new ReqConfig();
        reqConfig.setAccount(account);
        reqConfig.setLoginInfo(loginInfo);
        reqConfig.setCurrentRoute(currentRoute);
        reqConfig.setWaitTime(waitTime);
        return reqConfig;
    }

    /**
     * of
     *
     * @param account      account
     * @param loginInfo    loginInfo
     * @param currentRoute currentRoute
     * @return ReqConfig
     */
    public static ReqConfig of(SystemAccountConfig account, LoginInfo loginInfo, String currentRoute) {
        return of(account, loginInfo, currentRoute, null);
    }

    /**
     * of
     *
     * @param account      account
     * @param currentRoute currentRoute
     * @param waitTime     waitTime
     * @return ReqConfig
     */
    public static ReqConfig of(SystemAccountConfig account, String currentRoute, Long waitTime) {
        return of(account, null, currentRoute, waitTime);
    }

    /**
     * of
     *
     * @param account  account
     * @param waitTime waitTime
     * @return ReqConfig
     */
    public static ReqConfig of(SystemAccountConfig account, Long waitTime) {
        return of(account, null, null, waitTime);
    }

    /**
     * of
     *
     * @param account account
     * @return ReqConfig
     */
    public static ReqConfig of(SystemAccountConfig account) {
        return of(account, null, null, null);
    }

    /**
     * of
     *
     * @param currentRoute currentRoute
     * @param waitTime     waitTime
     * @return ReqConfig
     */
    @Deprecated
    public static ReqConfig of(String currentRoute, Long waitTime) {
        return of(null, null, currentRoute, waitTime);
    }

    /**
     * of
     *
     * @param loginInfo    loginInfo
     * @param currentRoute currentRoute
     * @return ReqConfig
     */
    @Deprecated
    public static ReqConfig of(LoginInfo loginInfo, String currentRoute) {
        return of(null, loginInfo, currentRoute, null);
    }

    /**
     * of
     *
     * @param loginInfo    loginInfo
     * @param currentRoute currentRoute
     * @param waitTime     waitTime
     * @return ReqConfig
     */
    @Deprecated
    public static ReqConfig of(LoginInfo loginInfo, String currentRoute, Long waitTime) {
        return of(null, loginInfo, currentRoute, waitTime);
    }

    /**
     * getProxy
     *
     * @return String
     */
    public String getProxy() {
        return account == null ? null : account.getProxy();
    }

    /**
     * header
     *
     * @param key   key
     * @param value value
     * @return ReqConfig
     */
    public ReqConfig header(String key, String value) {
        this.headers.put(key, value);
        return this;
    }
}
