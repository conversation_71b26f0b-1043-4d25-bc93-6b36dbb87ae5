package cn.com.sgcc.util;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

/**
 * SgccResp
 */
public class SgccResp {
    /**
     * ok
     *
     * @param jsonObject jsonObject
     * @return boolean
     */
    public static boolean ok(JSONObject jsonObject) {
        if (jsonObject == null) {
            return false;
        }
        var status = jsonObject.getInteger("status");
        return status != null && status == 0 && "Success".equals(jsonObject.getString("message"));
    }

    /**
     * fail
     *
     * @param jsonObject jsonObject
     * @return boolean
     */
    public static boolean fail(JSONObject jsonObject) {
        return !ok(jsonObject);
    }

    /**
     * longData
     *
     * @param jsonObject jsonObject
     * @return Long
     */
    public static Long longData(JSONObject jsonObject) {
        return jsonObject.getLong("data");
    }

    /**
     * jsonData
     *
     * @param jsonObject jsonObject
     * @return JSONObject
     */
    public static JSONObject jsonData(JSONObject jsonObject) {
        if (jsonObject.containsKey("data")) {
            var object = jsonObject.getJSONObject("data");
            if (object != null) {
                return object;
            }
        }
        return new JSONObject();
    }

    /**
     * arrayData
     *
     * @param jsonObject jsonObject
     * @return JSONArray
     */
    public static JSONArray arrayData(JSONObject jsonObject) {
        if (jsonObject.containsKey("data")) {
            var array = jsonObject.getJSONArray("data");
            if (array != null) {
                return array;
            }
        }
        return new JSONArray();
    }
}
