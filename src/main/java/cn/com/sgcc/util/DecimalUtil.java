package cn.com.sgcc.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * DecimalUtil
 */
@Slf4j
public class DecimalUtil {
    /**
     * nAdd
     *
     * @param vals vals
     * @return BigDecimal
     */
    public static BigDecimal nAdd(Object... vals) {
        return add(false, vals);
    }

    /**
     * add
     *
     * @param vals vals
     * @return BigDecimal
     */
    public static BigDecimal add(Object... vals) {
        return add(true, vals);
    }

    /**
     * add
     *
     * @param ignoreNull ignoreNull
     * @param vals       vals
     * @return BigDecimal
     */
    public static BigDecimal add(boolean ignoreNull, Object... vals) {
        if (vals == null || vals.length == 0) {
            return null;
        }
        try {
            boolean allNull = true;
            var result = BigDecimal.ZERO;
            for (Object val : vals) {
                if (val instanceof BigDecimal) {
                    result = result.add((BigDecimal) val);
                    allNull = false;
                } else if (val != null) {
                    result = result.add(new BigDecimal(val.toString()));
                    allNull = false;
                } else if (!ignoreNull) {
                    return null;
                }
            }
            return allNull ? null : result;
        } catch (Exception e) {
            log.error("error when add", e);
            return null;
        }
    }

    /**
     * nMultiply
     *
     * @param vals vals
     * @return BigDecimal
     */
    public static BigDecimal nMultiply(Object... vals) {
        return multiply(false, vals);
    }

    /**
     * multiply
     *
     * @param vals vals
     * @return BigDecimal
     */
    public static BigDecimal multiply(Object... vals) {
        return multiply(true, vals);
    }

    /**
     * multiply
     *
     * @param ignoreNull ignoreNull
     * @param vals       vals
     * @return BigDecimal
     */
    public static BigDecimal multiply(boolean ignoreNull, Object... vals) {
        if (vals == null || vals.length == 0) {
            return null;
        }
        try {
            boolean allNull = true;
            var result = BigDecimal.ONE;
            for (Object val : vals) {
                if (val instanceof BigDecimal) {
                    result = result.multiply((BigDecimal) val);
                    allNull = false;
                } else if (val != null) {
                    result = result.multiply(new BigDecimal(val.toString()));
                    allNull = false;
                } else if (!ignoreNull) {
                    return null;
                }
            }
            return allNull ? null : result;
        } catch (Exception e) {
            log.error("error when multiply", e);
            return null;
        }
    }

    /**
     * divide
     *
     * @param dividend dividend
     * @param divisor  divisor
     * @return BigDecimal
     */
    public static BigDecimal divide(Object dividend, Object divisor) {
        return divide(dividend, divisor, 2);
    }

    /**
     * divide
     *
     * @param dividend dividend
     * @param divisor  divisor
     * @param scale    scale
     * @return BigDecimal
     */
    public static BigDecimal divide(Object dividend, Object divisor, int scale) {
        return divide(dividend, divisor, scale, RoundingMode.HALF_UP);
    }

    /**
     * divide
     *
     * @param dividend     dividend
     * @param divisor      divisor
     * @param scale        scale
     * @param roundingMode roundingMode
     * @return BigDecimal
     */
    public static BigDecimal divide(Object dividend, Object divisor, int scale, RoundingMode roundingMode) {
        if (dividend == null || divisor == null) {
            return null;
        }
        try {
            BigDecimal dividendBD;
            BigDecimal divisorBD;
            if (divisor instanceof BigDecimal) {
                divisorBD = (BigDecimal) divisor;
            } else {
                divisorBD = new BigDecimal(divisor.toString());
            }
            if (divisorBD.doubleValue() == 0D) {
                return null;
            }
            if (dividend instanceof BigDecimal) {
                dividendBD = (BigDecimal) dividend;
            } else {
                dividendBD = new BigDecimal(dividend.toString());
            }
            return dividendBD.divide(divisorBD, scale, roundingMode);
        } catch (Exception e) {
            log.error("error when add", e);
            return null;
        }
    }

    /**
     * nAvg
     *
     * @param vals vals
     * @return BigDecimal
     */
    public static BigDecimal nAvg(Object... vals) {
        return avg(false, 2, vals);
    }

    /**
     * avg
     *
     * @param vals vals
     * @return BigDecimal
     */
    public static BigDecimal avg(Object... vals) {
        return avg(true, 2, vals);
    }

    /**
     * avg
     *
     * @param ignoreNull ignoreNull
     * @param vals       vals
     * @return BigDecimal
     */
    public static BigDecimal avg(boolean ignoreNull, Object... vals) {
        return avg(ignoreNull, 2, vals);
    }

    /**
     * avg
     *
     * @param ignoreNull ignoreNull
     * @param scale      scale
     * @param vals       vals
     * @return BigDecimal
     */
    public static BigDecimal avg(boolean ignoreNull, int scale, Object... vals) {
        return avg(ignoreNull, scale, RoundingMode.HALF_UP, vals);
    }

    /**
     * avg
     *
     * @param ignoreNull   ignoreNull
     * @param scale        scale
     * @param roundingMode roundingMode
     * @param vals         vals
     * @return BigDecimal
     */
    public static BigDecimal avg(boolean ignoreNull, int scale, RoundingMode roundingMode, Object... vals) {
        var add = add(ignoreNull, vals);
        if (add == null) {
            return null;
        }

        var count = ignoreNull ? Arrays.stream(vals).filter(Objects::nonNull).count() : vals.length;
        return divide(add, count, scale, roundingMode);
    }
}
