package cn.com.sgcc.util;

import ch.qos.logback.classic.spi.LoggingEvent;
import ch.qos.logback.core.UnsynchronizedAppenderBase;
import java.util.concurrent.LinkedTransferQueue;

/**
 * 监听日志
 */
public class ListenLogAppender<E> extends UnsynchronizedAppenderBase<E> {

    public static final LinkedTransferQueue<LoggingEvent> EVENTS = new LinkedTransferQueue<>();

    @Override
    protected void append(E eventObject) {
        if (eventObject instanceof LoggingEvent event) {
            EVENTS.put(event);
        }
    }
}
