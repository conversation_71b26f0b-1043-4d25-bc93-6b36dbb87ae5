package cn.com.sgcc.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TimeUtil {

    public static final Map<Integer, String> FIFTEEN_MINUTE_MAP = new HashMap<>();
    public static final Map<String, Integer> FIFTEEN_MINUTE_MAP_REV = new HashMap<>();
    public static final Map<String, Integer> HOUR_TIME_PART_MAP_REV = new HashMap<>();

    static {
        var timeAxis = customTimeAxis(
                LocalDate.now().atTime(0, 0),
                LocalDate.now().plusDays(1).atTime(0, 0), 60 * 15, "HH:mm", true, true);
        for (int i = 0; i < timeAxis.size(); i++) {
            FIFTEEN_MINUTE_MAP.put(i, timeAxis.get(i));
            FIFTEEN_MINUTE_MAP_REV.put(timeAxis.get(i), i);
            HOUR_TIME_PART_MAP_REV.put(timeAxis.get(i), i / 8);
        }
    }

    /**
     * 创建自定义间隔和格式的时间轴
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param timeStep   时间间隔
     * @param dataFormat 格式
     * @param offset     是否向后偏移一个时间间隔（如：从·00:00 变成 00:15)
     * @param endWith24  是否把0点转换为24点
     * @return 时间轴
     */
    public static List<String> customTimeAxis(LocalDateTime startTime, LocalDateTime endTime, long timeStep,
                                              String dataFormat, boolean offset, boolean endWith24) {
        List<String> timeAxislist = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dataFormat);
        int hourIndex = -1;
        if (endWith24 && dataFormat.contains("HH")) {
            hourIndex = dataFormat.indexOf("HH");
        }
        if (offset) {
            // 非从零开始时，从第一个时间间隔开始
            startTime = startTime.plusSeconds(timeStep);
            endTime = endTime.plusSeconds(timeStep);
        }
        while (startTime.isBefore(endTime)) {
            String timeStr = startTime.format(formatter);
            if (hourIndex != -1 && "00".equals(timeStr.substring(hourIndex, hourIndex + 2))
                && startTime.getMinute() == 0 && startTime.getSecond() == 0) {
                timeStr = startTime.plusDays(-1).format(formatter);
                timeStr = timeStr.substring(0, hourIndex) + "24" + timeStr.substring(hourIndex + 2);
            }
            timeAxislist.add(timeStr);
            startTime = startTime.plusSeconds(timeStep);
        }
        return timeAxislist;
    }

}
