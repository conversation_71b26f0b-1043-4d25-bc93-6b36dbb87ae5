package cn.com.sgcc.util;

import static cn.hutool.core.util.ZipUtil.gzip;
import static cn.hutool.core.util.ZipUtil.unGzip;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * EncryptUtil
 */
public class EncryptUtil {

    private static final Charset UTF_8 = StandardCharsets.UTF_8;

    private static final SM2 SM2 = SmUtil.sm2(
        "bd2a7dd28b7010441d1b230612f99e10b835b326745bb2cba3b2a790c9e93ed2",
        "04"
            + "7bbd98c719a31ccbac98dc521d11ac104a36cac578f87316d39313c131658dab"
            + "02f86668959bba48c958ba34d95a05db829980bbdf3e7c1cebf82d0d50842a4c"
    );

    /**
     * encrypt
     *
     * @param str str
     * @return String
     */
    public static String encrypt(String str) {
        return encrypt(SM2, str);
    }

    /**
     * encrypt
     *
     * @param sm2 sm2
     * @param str str
     * @return String
     */
    public static String encrypt(SM2 sm2, String str) {
        return Base64.encode(gzip(sm2.encryptHex(gzip(str.getBytes(UTF_8)), KeyType.PublicKey), UTF_8.name()));
    }

    /**
     * decrypt
     *
     * @param str str
     * @return String
     */
    public static String decrypt(String str) {
        return decrypt(SM2, str);
    }

    /**
     * decrypt
     *
     * @param sm2 sm2
     * @param str str
     * @return String
     */
    public static String decrypt(SM2 sm2, String str) {
        return unGzip(sm2.decrypt(unGzip(Base64.decode(str), UTF_8.name()), KeyType.PrivateKey), UTF_8.name());
    }

    /**
     * getKey
     *
     * @param keyParts keyParts
     * @param secret   secret
     * @return String
     */
    public static String getKey(byte[][] keyParts, String secret) {
        return LockUtil.lock(EncryptUtil.class, "getKey", k -> {
            StringBuilder keyBuilder = new StringBuilder();
            byte[] secretBytes = secret.getBytes(StandardCharsets.UTF_8);
            // 逆向操作，重组密钥
            for (byte[] part : keyParts) {
                // 1. 先进行异或解密
                byte[] reversedBytes = xorWithSecret(part, secretBytes);
                String reversedPart = new String(reversedBytes, StandardCharsets.UTF_8);
                // 2. 再反转字符串
                keyBuilder.append(new StringBuilder(reversedPart).reverse());
            }
            return keyBuilder.toString();
        });
    }

    /**
     * xorWithSecret
     *
     * @param data   data
     * @param secret secret
     * @return byte[]
     */
    private static byte[] xorWithSecret(byte[] data, byte[] secret) {
        byte[] result = new byte[data.length];
        for (int i = 0; i < data.length; i++) {
            result[i] = (byte) (data[i] ^ secret[i % secret.length]);
        }
        return result;
    }

    /*
    public static void main(String[] args) {
        var sm2 = new SM2();
        var q = ((BCECPublicKey) sm2.getPublicKey()).getQ();
        var pubKey = "04" + q.getXCoord().toBigInteger().toString(16) + q.getYCoord().toBigInteger().toString(16);
        var priKey = sm2.getDHex();
        System.out.println("privateKey: " + priKey);
        System.out.println("publicKey: " + pubKey);
        generatePrivateKey(priKey);
        generatePrivateKey(pubKey);

    }

    public static void generatePrivateKey(String privateKey) {
        byte[] secretBytes = "((a,b)=>(Math.ceil(a/b)))($a,$b)".getBytes(StandardCharsets.UTF_8);
        // 1. 将私钥切分成多段，比如每段50个字符
        int partSize = 16;
        int numParts = (int) Math.ceil((double) privateKey.length() / partSize);
        byte[][] obfuscatedParts = new byte[numParts][];
        for (int i = 0; i < numParts; i++) {
            int start = i * partSize;
            int end = Math.min(start + partSize, privateKey.length());
            String part = privateKey.substring(start, end);
            // 2. 对每一段进行变形：先反转字符串
            String reversedPart = new StringBuilder(part).reverse().toString();
            // 3. 再进行异或加密
            obfuscatedParts[i] = xorWithSecret(reversedPart.getBytes(StandardCharsets.UTF_8), secretBytes);
        }
        // 4. 生成可以直接粘贴到Java代码里的字节数组定义
        System.out.println("--- 请将以下代码复制到你的 SecureKeyProvider 类中 ---");
        System.out.println("private static final byte[][] KEY_PARTS = new byte[][]{");
        for (int i = 0; i < obfuscatedParts.length; i++) {
            System.out.print("    new byte[]{" + formatBytes(obfuscatedParts[i]) + "}");
            if (i < obfuscatedParts.length - 1) {
                System.out.println(",");
            } else {
                System.out.println();
            }
        }
        System.out.println("};");

    }

    // 辅助方法，将byte数组格式化成 "12, 34, -56, ..." 的形式
    private static String formatBytes(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            sb.append(bytes[i]);
            if (i < bytes.length - 1) {
                sb.append(", ");
            }
        }
        return sb.toString();
    }
    */

}
