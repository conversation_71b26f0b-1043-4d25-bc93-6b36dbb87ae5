package cn.com.sgcc.util;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CopyOnWriteArraySet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * SseEmitters
 */
@Slf4j
public class SseEmitters {

    private final CopyOnWriteArraySet<SseEmitter> emitters = new CopyOnWriteArraySet<>();

    /**
     * init
     *
     * @return SseEmitters
     */
    public static SseEmitters init() {
        return new SseEmitters();
    }

    /**
     * send
     *
     * @param emitter emitter
     * @param data    data
     */
    public void send(SseEmitter emitter, String data) {
        send(emitter, SseEmitter.event().name("default").data(data));
    }

    /**
     * send
     *
     * @param data data
     */
    public void send(String data) {
        send(null, SseEmitter.event().name("default").data(data));
    }

    /**
     * send
     *
     * @param targetEmitter targetEmitter
     * @param eventBuilder  eventBuilder
     */
    public void send(SseEmitter targetEmitter, SseEmitter.SseEventBuilder eventBuilder) {
        (targetEmitter != null ? List.of(targetEmitter) : emitters).forEach(emitter -> {
            try {
                emitter.send(eventBuilder);
            } catch (Exception e) {
                log.debug("remove emitter {}", emitter);
                emitters.remove(emitter);
                emitter.completeWithError(e);
            }
        });
    }

    /**
     * createNew
     *
     * @param srcClass srcClass
     * @return SseEmitter
     */
    public SseEmitter createNew(Class<?> srcClass) {
        var emitter = new SseEmitter(0L);
        try {
            emitter.send(SseEmitter.event().comment("init"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        log.debug("add new emitter {} from {}", emitter, srcClass.getName());
        emitters.add(emitter);
        return emitter;
    }
}
