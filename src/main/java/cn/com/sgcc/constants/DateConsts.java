package cn.com.sgcc.constants;

import java.time.format.DateTimeFormatter;

public interface DateConsts {
    DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    DateTimeFormatter TIME_FORMAT = DateTimeFormatter.ofPattern("HH:mm:ss");
    DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    DateTimeFormatter DATE_TIME_HH_MM_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    DateTimeFormatter MONTH_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM");
    DateTimeFormatter MONTH_SHORT_STR_FORMAT = DateTimeFormatter.ofPattern("yyyy年M月");
    DateTimeFormatter TIME_HH_MM_FORMAT = DateTimeFormatter.ofPattern("HH:mm");
    DateTimeFormatter SGCC_DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS+0800");
    DateTimeFormatter SGCC_DATE_TIME_FORMAT_2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
    DateTimeFormatter SGCC_DATE_TIME_FORMAT_UTC = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS+0000");
    DateTimeFormatter JX_DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd H:mm:ss");
}
