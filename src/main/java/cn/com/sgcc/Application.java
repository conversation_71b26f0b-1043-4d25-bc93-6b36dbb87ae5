package cn.com.sgcc;

import cn.com.sgcc.config.HintsRegistrar;
import cn.com.sgcc.util.NativeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportRuntimeHints;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * App
 */
@Slf4j
@EnableAsync
@EnableScheduling
@EnableJpaRepositories
@EnableTransactionManagement
@ImportRuntimeHints(HintsRegistrar.class)
@SpringBootApplication
public class Application {

    /**
     * main
     *
     * @param args 参数
     */
    public static void main(String[] args) {
        log.info("current is native: {}", NativeUtil.isNative());
        SpringApplication.run(Application.class, args);
    }
}
