package cn.com.sgcc.config;

import static com.alibaba.fastjson2.JSONWriter.Feature.BrowserCompatible;
import static com.alibaba.fastjson2.JSONWriter.Feature.FieldBased;
import static com.alibaba.fastjson2.JSONWriter.Feature.WriteBigDecimalAsPlain;

import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.support.config.FastJsonConfig;
import com.alibaba.fastjson2.support.spring6.http.converter.FastJsonHttpMessageConverter;
import com.alibaba.fastjson2.support.spring6.webservlet.view.FastJsonJsonView;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.ViewResolverRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Config
 */
@Configuration
public class WebMvc implements WebMvcConfigurer {
    /*, AsyncConfigurer, WebServerFactoryCustomizer<ConfigurableTomcatWebServerFactory>*/

    private final FastJsonConfig fastJsonConfig = new FastJsonConfig();

    {
        fastJsonConfig.setCharset(StandardCharsets.UTF_8);
        fastJsonConfig.setDateFormat("yyyy-MM-dd HH:mm:ss");
        fastJsonConfig.setReaderFeatures(JSONReader.Feature.FieldBased);
        fastJsonConfig.setWriterFeatures(FieldBased, BrowserCompatible, WriteBigDecimalAsPlain);
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        FastJsonHttpMessageConverter converter = new FastJsonHttpMessageConverter();
        converter.setFastJsonConfig(fastJsonConfig);
        converter.setDefaultCharset(StandardCharsets.UTF_8);
        converter.setSupportedMediaTypes(Collections.singletonList(MediaType.APPLICATION_JSON));
        converters.addFirst(converter);
    }

    @Override
    public void configureViewResolvers(ViewResolverRegistry registry) {
        FastJsonJsonView fastJsonJsonView = new FastJsonJsonView();
        fastJsonJsonView.setFastJsonConfig(fastJsonConfig);
        registry.enableContentNegotiation(fastJsonJsonView);
    }



/*
    @Override
    public Executor getAsyncExecutor() {
        // eg: @Async
        return new TaskExecutorAdapter(Executors.newThreadPerTaskExecutor(Thread.ofVirtual().name("a-", 1).factory()));
    }

    @Override
    public void customize(ConfigurableTomcatWebServerFactory factory) {
 factory.addProtocolHandlerCustomizers(protocolHandler -> protocolHandler.setExecutor(new VirtualThreadExecutor("t-")));
    }
*/

}
