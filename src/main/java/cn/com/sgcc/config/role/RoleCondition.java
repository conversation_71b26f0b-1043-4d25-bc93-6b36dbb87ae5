package cn.com.sgcc.config.role;

import cn.com.sgcc.business.system.model.SystemAccountConfig;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.business.system.repository.SystemAccountConfigRepo;
import cn.com.sgcc.business.system.repository.SystemUnitConfigRepo;
import cn.com.sgcc.business.system.service.SystemService;
import cn.com.sgcc.constants.DateConsts;
import cn.com.sgcc.constants.RoleConstants;
import cn.com.sgcc.util.EncryptUtil;
import cn.com.sgcc.util.LockUtil;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RoleCondition implements Condition {

    @Value("${custom.auth.url}")
    String authUrl;
    @Resource
    private SystemAccountConfigRepo accountConfigRepo;
    @Resource
    SystemUnitConfigRepo unitConfigRepo;

    /**
     * Native 不吃这一套，暂时没用
     */
    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        /*
        var annotationAttributes = metadata.getAnnotationAttributes(HasRole.class.getName());
        if (annotationAttributes == null
            || annotationAttributes.isEmpty()
            || annotationAttributes.get("value") == null) {
            return false;
        }
        var type = (RoleConstants.Type) annotationAttributes.get("value");
        return !SystemService.ROLES.getJSONArray(type.name()).isEmpty();
        */
        return true;
    }

    /**
     * 先用这个代替
     *
     * @param type type
     * @return boolean
     */
    public static boolean hasRole(RoleConstants.Type type) {
        return !SystemService.ROLES.getJSONArray(type.name()).isEmpty();
    }

    // privateKey: d40c605826a2fa72146508ff41bd2c54dff130cd32c36159d7fb9b1d9e7df2b3
    //  publicKey: 04
    //             fa46a5bef28e423b1623552c4dab48341ab57eaed1badd1ee43a171ce8084b66
    //             52dadd0c3bd1200ec3a8bc4d8a2eee64ef0cd5db209e36f1906b31969dde5d76
    private static final byte[][] PRI_KEY_PARTS = new byte[][]{
        new byte[]{26, 31, 0, 74, 80, 72, 11, 12, 16, 120, 81, 66, 11, 30, 87, 1},
        new byte[]{28, 29, 2, 30, 6, 75, 12, 10, 78, 43, 89, 68, 93, 24, 87, 84},
        new byte[]{17, 29, 80, 26, 81, 74, 15, 13, 76, 46, 81, 71, 89, 72, 5, 1},
        new byte[]{27, 74, 83, 74, 6, 30, 88, 7, 76, 124, 3, 77, 10, 72, 84, 1}
    };

    // privateKey: 3f021f9bc5f94ed110f1d8997e41bae291e4fb091cb0483d6944572d51c05001
    //  publicKey: 04
    //             10d8cf255931e40670ff2677f7cc1454ca52eaa1d30e7a4557c92e68b8cd0b7c
    //             4b393cd61b5d38db8e2ac10a4189026408373deec01e83a0375274d4e6f2989a
    private static final byte[][] PUB_KEY_PARTS = new byte[][]{
        new byte[]{28, 77, 80, 31, 91, 28, 8, 12, 78, 46, 89, 16, 88, 31, 87, 85},
        new byte[]{28, 25, 2, 79, 85, 79, 10, 9, 30, 127, 7, 18, 88, 25, 85, 85},
        new byte[]{73, 31, 4, 28, 81, 77, 12, 95, 73, 40, 83, 65, 9, 77, 87, 80},
        new byte[]{74, 24, 5, 79, 90, 75, 5, 8, 77, 127, 88, 23, 95, 27, 86, 81},
        new byte[]{16, 27, 5, 25, 0, 24, 11, 90, 75, 126, 88, 71, 10, 26, 0, 82},
        new byte[]{26, 24, 88, 20, 83, 29, 92, 14, 25, 46, 0, 70, 13, 22, 1, 1},
        new byte[]{27, 16, 4, 29, 82, 74, 88, 91, 76, 126, 86, 71, 80, 30, 87, 83},
        new byte[]{16, 17, 83, 74, 84, 76, 9, 90, 28, 122, 83, 65, 95, 29, 83, 4},
        new byte[]{73, 17}
    };

    private static final String SECRET = "((a,b)=>(Math.ceil(a/b)))($a,$b)";

    /**
     * updatePermission
     */
    public void updatePermission() {
        LockUtil.lock(RoleCondition.class, "updatePermission", k -> {
            var jsonObject = new JSONObject();
            Arrays.stream(RoleConstants.Type.values())
                  .forEach(type -> jsonObject.put(type.name(), new JSONArray()));
            try {
                log.info("更新权限列表");
                var body = JSONObject.of();
                {
                    var accounts = accountConfigRepo.findAll().stream().map(SystemAccountConfig::getUserName).toList();
                    var units = unitConfigRepo.findAll().stream().map(SystemUnitConfig::getUnitId).toList();
                    // body = JSONObject.of("accounts", accounts, "units", units);
                    body.put("accounts", accounts);
                    body.put("units", units);
                }
                var encSm2 = new SM2(null, EncryptUtil.getKey(PUB_KEY_PARTS, SECRET));
                var bodyStr = EncryptUtil.encrypt(encSm2.setMode(SM2Engine.Mode.C1C2C3), body.toJSONString());
                try (HttpResponse execute = HttpUtil.createPost(authUrl)
                                                    .body(bodyStr, "text/plain")
                                                    .timeout(30000)
                                                    .execute()) {
                    var respStr = execute.body();
                    log.info("resp: {}", respStr);
                    var resp = JSONObject.parse(respStr);
                    var success = resp.getBoolean("success");
                    if (!success) {
                        log.error("获取权限失败：{}", resp.getString("msg"));
                        return;
                    }
                    var decSm2 = new SM2(EncryptUtil.getKey(PRI_KEY_PARTS, SECRET), null);
                    var obj = EncryptUtil.decrypt(decSm2.setMode(SM2Engine.Mode.C1C2C3), resp.getString("obj"));
                    var parsed = JSONObject.parse(obj);
                    var nowStr = LocalDate.now().format(DateConsts.DATE_FORMAT);
                    for (String roleType : parsed.keySet()) {
                        var accounts = parsed.getJSONObject(roleType);
                        for (String account : accounts.keySet()) {
                            var expDateStr = accounts.getString(account);
                            if (expDateStr.compareTo(nowStr) > 0) {
                                jsonObject.getJSONArray(roleType).add(account);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取权限失败", e);
                SystemService.ROLES.clear();
                SystemService.MENUS.clear();
            } finally {
                SystemService.ROLES.clear();
                SystemService.ROLES.putAll(jsonObject);
                SystemService.MENUS.clear();
                for (String key : jsonObject.keySet()) {
                    SystemService.MENUS.put(key, jsonObject.getJSONArray(key).size());
                }
                log.info("当前用户权限: {}", jsonObject);
            }
        });
    }
}
