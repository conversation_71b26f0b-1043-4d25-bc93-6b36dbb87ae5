package cn.com.sgcc.config.role;

import cn.com.sgcc.constants.RoleConstants;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * HasRole
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface HasRole {
    /**
     * value
     *
     * @return RoleConstants.Type
     */
    RoleConstants.Type value();
}
