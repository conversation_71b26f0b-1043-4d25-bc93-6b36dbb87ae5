package cn.com.sgcc.config;

import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingConfigDetail;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingTimeInfo;
import cn.com.sgcc.business.sgcc.opbd.model.UserOpbdConfigDetail;
import cn.com.sgcc.business.sgcc.opid.model.UserOpidConfigDetail;
import cn.com.sgcc.business.sgcc.opid.model.excel.StrategyConfigImport;
import cn.com.sgcc.business.sgcc.profitanalysis.model.DataProfitAnalysis;
import cn.com.sgcc.business.sgcc.profitanalysis.model.DataProfitAnalysisShow;
import org.hibernate.dialect.H2Dialect;
import org.springframework.aot.hint.MemberCategory;
import org.springframework.aot.hint.RuntimeHints;
import org.springframework.aot.hint.RuntimeHintsRegistrar;

public class HintsRegistrar implements RuntimeHintsRegistrar {
    @Override
    public void registerHints(RuntimeHints hints, ClassLoader classLoader) {
        hints.reflection().registerType(H2Dialect.class, MemberCategory.values());
        // Bean
        hints.reflection().registerType(StrategyConfigImport.class, MemberCategory.values());
        hints.reflection().registerType(UserDayRollingConfigDetail.class, MemberCategory.values());
        hints.reflection().registerType(UserDayRollingTimeInfo.class, MemberCategory.values());
        hints.reflection().registerType(UserOpbdConfigDetail.class, MemberCategory.values());
        hints.reflection().registerType(UserOpidConfigDetail.class, MemberCategory.values());
        hints.reflection().registerType(DataProfitAnalysis.class, MemberCategory.values());
        hints.reflection().registerType(DataProfitAnalysisShow.class, MemberCategory.values());
        hints.resources().registerPattern("javascript/**");
    }
}
