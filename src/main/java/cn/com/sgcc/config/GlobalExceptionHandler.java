package cn.com.sgcc.config;

import cn.com.sgcc.business.common.model.dto.Result;
import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

/**
 * GlobalExceptionHandler
 */
@Slf4j
@Component
public class GlobalExceptionHandler implements HandlerExceptionResolver {

    @Override
    public ModelAndView resolveException(@Nullable HttpServletRequest request,
                                         @Nullable HttpServletResponse response,
                                         Object handler,
                                         @Nullable Exception ex) {
        ModelAndView modelAndView = new ModelAndView(new MappingJackson2JsonView());
        Result<Object> fail = Result.fail("请求异常");
        ErrMsg errMsg = null;
        if (handler instanceof HandlerMethod) {
            /*NativeResp nativeResp = ((HandlerMethod) handler).getMethod().getAnnotation(NativeResp.class);
            if (nativeResp != null) {
                return null;
            }*/
            errMsg = ((HandlerMethod) handler).getMethod().getAnnotation(ErrMsg.class);
        }
        if (errMsg != null) {
            fail.setCode(errMsg.code());
            fail.setMsg(errMsg.value());
            log.error(errMsg.value(), ex);
        } else {
            log.error("未知异常", ex);
        }
        modelAndView.addAllObjects(JSONObject.from(fail));
        return modelAndView;
    }
}
