package cn.com.sgcc.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * ErrMsg
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ErrMsg {
    /**
     * code
     *
     * @return int
     */
    int code() default -1;

    /**
     * value
     *
     * @return String
     */
    String value() default "请求异常";
}
