package cn.com.sgcc.business.common.service.impl;

import cn.com.sgcc.business.common.model.UserConfig;
import cn.com.sgcc.business.common.model.UserConfigEnum;
import cn.com.sgcc.business.common.repository.UserConfigRepo;
import cn.com.sgcc.business.common.service.UserConfigService;
import cn.com.sgcc.constants.DateConsts;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserConfigServiceImpl implements UserConfigService {

    @Resource
    UserConfigRepo userConfigRepo;

    private static final ConcurrentHashMap<UserConfigEnum, String> CONFIGS = new ConcurrentHashMap<>();

    /**
     * init
     */
    @PostConstruct
    public void init() {
        var configList = userConfigRepo.findAll();
        for (UserConfig it : configList) {
            try {
                CONFIGS.put(UserConfigEnum.valueOf(it.getK()), it.getV());
            } catch (Exception e) {
                log.warn("未解析的配置：{}", it.getK());
            }
        }
    }

    @Override
    public String get(UserConfigEnum userConfigEnum) {
        var config = CONFIGS.get(userConfigEnum);
        if (config == null && userConfigEnum.getDefaultValue() != null) {
            set(userConfigEnum, userConfigEnum.getDefaultValue());
            config = userConfigEnum.getDefaultValue();
        }
        return config;
    }

    @Override
    public void set(UserConfigEnum userConfigEnum, String value) {
        var configOptional = userConfigRepo.findById(userConfigEnum.name());
        var userConfig = configOptional.orElse(new UserConfig(userConfigEnum.name()));
        userConfig.setV(value);
        userConfig.setUpdateTime(LocalDateTime.now().format(DateConsts.DATE_TIME_FORMAT));
        userConfigRepo.saveAndFlush(userConfig);
        if (value != null) {
            CONFIGS.put(userConfigEnum, value);
        } else {
            CONFIGS.remove(userConfigEnum);
        }
    }

    @Override
    public void del(UserConfigEnum userConfigEnum) {
        set(userConfigEnum, null);
    }
}
