package cn.com.sgcc.business.common.model.dto;

import java.io.Serializable;
import lombok.Data;

@Data
public class Result<T> implements Serializable {
    private int code;
    private String msg;
    private T data;

    /**
     * Result Constructor
     *
     * @param code code
     * @param msg  msg
     * @param data data
     */
    public Result(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * fail
     *
     * @param msg msg
     * @param <T> T
     * @return Result<T>
     */
    public static <T> Result<T> fail(String msg) {
        return new Result<>(-1, msg, null);
    }

    /**
     * ok
     *
     * @param data data
     * @param <T>  T
     * @return Result<T>
     */
    public static <T> Result<T> ok(T data) {
        return new Result<>(1, "success", data);
    }

    /**
     * ok
     *
     * @param <T> T
     * @return Result<T>
     */
    public static <T> Result<T> ok() {
        return Result.ok(null);
    }
}
