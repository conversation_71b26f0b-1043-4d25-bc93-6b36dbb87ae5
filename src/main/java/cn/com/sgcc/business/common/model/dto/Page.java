package cn.com.sgcc.business.common.model.dto;

import lombok.Data;

@Data
public class Page<T> {
    private int page;
    private int pageSize;
    private long total;
    private T data;

    /**
     * Page Constructor
     */
    public Page() {
    }

    /**
     * Page Constructor
     *
     * @param page     page
     * @param pageSize pageSize
     */
    public Page(int page, int pageSize) {
        this.page = page;
        this.pageSize = pageSize;
    }
}
