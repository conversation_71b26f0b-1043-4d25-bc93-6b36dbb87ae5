package cn.com.sgcc.business.common.service;

import cn.com.sgcc.business.common.model.UserConfigEnum;

public interface UserConfigService {

    /**
     * get
     *
     * @param userConfigEnum userConfigEnum
     * @return String
     */
    String get(UserConfigEnum userConfigEnum);

    /**
     * set
     *
     * @param userConfigEnum userConfigEnum
     * @param value          value
     */
    void set(UserConfigEnum userConfigEnum, String value);

    /**
     * del
     *
     * @param userConfigEnum userConfigEnum
     */
    void del(UserConfigEnum userConfigEnum);

}
