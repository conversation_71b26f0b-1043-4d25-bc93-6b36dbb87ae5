package cn.com.sgcc.business.common.model;

import java.io.Serializable;
import lombok.Getter;

@Getter
public enum UserConfigEnum implements Serializable {
    /**
     * 省间日前根据申报结束时间提前的分钟数，如果超出开始时间，则取开始结束时间中间值
     */
    OPBD_RANGE_END("15"),
    /**
     * 省间日前申报结束时间
     */
    OPBD_RANGE_END_TIME(null),
    /**
     * 黑龙江限定，省间日内申报时间，用于配置 D+1 统一全部申报时间
     */
    HLJ_OPID_TIME_CONFIG(null),
    /**
     * 黑龙江限定，上次全部申报的日期及状态，用于判断是否执行过全部申报
     */
    HLJ_OPID_LAST_ALL_DECLARE(""),
    ;

    final String defaultValue;

    /**
     * UserConfigEnum Constructor
     *
     * @param defaultValue defaultValue
     */
    UserConfigEnum(String defaultValue) {
        this.defaultValue = defaultValue;
    }

}
