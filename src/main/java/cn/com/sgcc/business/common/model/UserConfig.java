package cn.com.sgcc.business.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Table(name = "user_config")
@NoArgsConstructor
public class UserConfig implements Serializable {
    @Id
    @Column
    private String k;
    @Column
    private String v;
    @Column
    private String updateTime;

    /**
     * UserConfig Constructor
     *
     * @param k key
     */
    public UserConfig(String k) {
        this.k = k;
    }
}
