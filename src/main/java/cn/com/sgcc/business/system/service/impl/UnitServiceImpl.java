package cn.com.sgcc.business.system.service.impl;

import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.business.system.repository.SystemUnitConfigRepo;
import cn.com.sgcc.business.system.service.SystemService;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.constants.RoleConstants;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UnitServiceImpl implements UnitService {

    @Resource
    private SystemUnitConfigRepo unitConfigRepo;
    @Resource
    private SystemService systemService;

    @Override
    public List<SystemUnitConfig> getUnitList(RoleConstants.Type type) {
        if (type == null) {
            return new ArrayList<>();
        }
        var set = systemService.getRole(type);
        return unitConfigRepo.findAll().stream().filter(unit -> set.contains(unit.getUnitId())).toList();
    }

    @Override
    public Map<String, SystemUnitConfig> getUnitMap(RoleConstants.Type type) {
        return getUnitList(type).stream().collect(Collectors.toMap(SystemUnitConfig::getUnitId, Function.identity()));
    }

    @Override
    public Optional<SystemUnitConfig> findFirstByUnitId(String unitId) {
        return unitConfigRepo.findFirstByUnitId(unitId);
    }

    @Override
    public Optional<SystemUnitConfig> findFirstByUnitId(RoleConstants.Type type, String unitId) {
        var set = systemService.getRole(type);
        if (set.contains(unitId)) {
            return findFirstByUnitId(unitId);
        }
        return Optional.empty();
    }
}
