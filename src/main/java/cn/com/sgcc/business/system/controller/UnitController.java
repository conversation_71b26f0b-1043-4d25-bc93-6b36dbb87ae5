package cn.com.sgcc.business.system.controller;

import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.config.ErrMsg;
import cn.com.sgcc.constants.RoleConstants;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/unit")
@CrossOrigin
@Slf4j
public class UnitController {
    @Resource
    private UnitService unitService;

    /**
     * getUnitList
     *
     * @param type type
     * @return Result<List < SystemUnitConfig>>
     */
    @GetMapping("/unitList")
    @ErrMsg("获取交易单元失败")
    public Result<List<SystemUnitConfig>> getUnitList(RoleConstants.Type type) {
        return Result.ok(unitService.getUnitList(type));
    }

}
