package cn.com.sgcc.business.system.service;

import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.constants.RoleConstants;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * UnitService
 */
public interface UnitService {
    /**
     * getUnitMap
     *
     * @param type type
     * @return Map<String, SystemUnitConfig>
     */
    Map<String, SystemUnitConfig> getUnitMap(RoleConstants.Type type);

    /**
     * getUnitList
     *
     * @param type type
     * @return List<SystemUnitConfig>
     */
    List<SystemUnitConfig> getUnitList(RoleConstants.Type type);

    /**
     * findFirstByUnitId
     *
     * @param unitId unitId
     * @return Optional<SystemUnitConfig>
     */
    Optional<SystemUnitConfig> findFirstByUnitId(String unitId);

    /**
     * findFirstByUnitId
     *
     * @param type   type
     * @param unitId unitId
     * @return Optional<SystemUnitConfig>
     */
    Optional<SystemUnitConfig> findFirstByUnitId(RoleConstants.Type type, String unitId);
}
