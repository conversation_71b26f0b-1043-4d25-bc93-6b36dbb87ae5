package cn.com.sgcc.business.system.controller;

import ch.qos.logback.classic.pattern.TargetLengthBasedClassNameAbbreviator2;
import ch.qos.logback.classic.spi.LoggingEvent;
import ch.qos.logback.classic.spi.ThrowableProxyUtil;
import cn.com.sgcc.config.ErrMsg;
import cn.com.sgcc.util.ListenLogAppender;
import cn.com.sgcc.util.SseEmitters;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.PostConstruct;
import java.util.concurrent.ConcurrentLinkedQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * LogController
 */
@RestController
@RequestMapping("/api/v1/log")
@CrossOrigin
@Slf4j
public class LogController {

    public static final SseEmitters EMITTERS = SseEmitters.init();
    public static final ConcurrentLinkedQueue<String> QUEUE = new ConcurrentLinkedQueue<>();
    public static final int CACHE_SIZE = 100;

    /**
     * stream
     *
     * @return SseEmitter
     */
    @GetMapping("/sse")
    @ErrMsg("SSE更新Log失败")
    public SseEmitter stream() {
        var emitter = EMITTERS.createNew(LogController.class);
        for (Object o : QUEUE.toArray()) {
            EMITTERS.send(emitter, (String) o);
        }
        return emitter;
    }

    /**
     * init
     */
    @PostConstruct
    public void init() {
        var nameAbbreviator2 = new TargetLengthBasedClassNameAbbreviator2(50);
        new Thread(() -> {
            for (; ; ) {
                try {
                    LoggingEvent event = ListenLogAppender.EVENTS.take();
                    var message = new JSONObject()
                            .fluentPut("time", event.getTimeStamp())
                            .fluentPut("thread", event.getThreadName())
                            .fluentPut("level", event.getLevel().toString())
                            .fluentPut("loggerName", nameAbbreviator2.abbreviate(event.getLoggerName()))
                            .fluentPut("message", event.getFormattedMessage())
                            .fluentPut("throwableStr", ThrowableProxyUtil.asString(event.getThrowableProxy()))
                            .toString();
                    EMITTERS.send(message);
                    QUEUE.offer(message);
                    if (QUEUE.size() > CACHE_SIZE) {
                        for (int i = 0; i < QUEUE.size() - CACHE_SIZE; i++) {
                            QUEUE.poll();
                        }
                    }
                } catch (Exception ignored) {
                }
            }
        }).start();
    }
}
