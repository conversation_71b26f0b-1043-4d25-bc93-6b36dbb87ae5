package cn.com.sgcc.business.system.service;

import cn.com.sgcc.constants.RoleConstants;
import com.alibaba.fastjson2.JSONObject;
import java.util.Set;

/**
 * SystemService
 */
public interface SystemService {

    JSONObject MENUS = new JSONObject();
    JSONObject ROLES = new JSONObject();

    /**
     * getMenus
     *
     * @return JSONObject
     */
    JSONObject getMenus();

    /**
     * refreshRole
     */
    void refreshRole();

    /**
     * getRole
     *
     * @param type type
     * @return Set<String>
     */
    Set<String> getRole(RoleConstants.Type type);
}
