package cn.com.sgcc.business.system.service.impl;

import cn.com.sgcc.business.system.service.SystemService;
import cn.com.sgcc.config.role.RoleCondition;
import cn.com.sgcc.constants.RoleConstants;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.HashSet;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SystemServiceImpl implements SystemService {

    @Resource
    RoleCondition roleCondition;

    @Override
    public JSONObject getMenus() {
        return MENUS;
    }

    @Order(1)
    @PostConstruct
    @Override
    public void refreshRole() {
        roleCondition.updatePermission();
    }

    @Override
    public Set<String> getRole(RoleConstants.Type type) {
        log.info("getRoleWithType: {}", type.name());
        var set = new HashSet<>(ROLES.getJSONArray(type.name()).toJavaList(String.class));
        MENUS.put(type.name(), set.size());
        return set;
    }
}
