package cn.com.sgcc.business.system.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "system_account_config")
public class SystemAccountConfig implements Serializable {
    @Id
    @Column
    private String plantId;
    @Column
    private String plantName;
    @Column
    private String uKeyCode;
    @Column
    private String userName;
    @Column
    private String passWord;
    @Column
    private String uKeyPrivate;
    @Column
    private String uKeyPassword;
    @Column
    private String grayTag;
    @Column
    private String phone;
    @Column
    private Integer useUKey;
    @Column
    private Integer useSMS;
    @Column
    private String smsRegex;
    @Column
    private String proxy;
}
