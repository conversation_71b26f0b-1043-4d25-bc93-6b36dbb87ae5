package cn.com.sgcc.business.system.service;

import cn.com.sgcc.business.system.model.SystemAccountConfig;
import cn.com.sgcc.constants.RoleConstants;
import java.util.List;

/**
 * 账户服务接口，提供系统账户配置相关的业务逻辑
 */
public interface AccountService {

    /**
     * 根据角色类型获取账户列表
     *
     * @param type 角色类型，用于过滤返回的账户列表
     * @return 角色类型对应的账户配置列表
     */
    List<SystemAccountConfig> getAccountList(RoleConstants.Type type);

}

