package cn.com.sgcc.business.system.service.impl;

import cn.com.sgcc.business.system.model.SystemAccountConfig;
import cn.com.sgcc.business.system.repository.SystemAccountConfigRepo;
import cn.com.sgcc.business.system.service.AccountService;
import cn.com.sgcc.business.system.service.SystemService;
import cn.com.sgcc.constants.RoleConstants;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 账户服务实现类
 */
@Slf4j
@Service
public class AccountServiceImpl implements AccountService {

    // 注入系统账户配置仓库
    @Resource
    private SystemAccountConfigRepo accountConfigRepo;

    // 注入系统服务
    @Resource
    private SystemService systemService;

    /**
     * 根据角色类型获取账户列表
     *
     * @param type 角色类型
     * @return 角色对应的账户配置列表
     */
    @Override
    public List<SystemAccountConfig> getAccountList(RoleConstants.Type type) {
        // 如果角色类型为空，返回空列表
        if (type == null) {
            return new ArrayList<>();
        }
        // 获取指定角色类型的角色集合
        var set = systemService.getRole(type);
        // 从所有账户配置中过滤出角色集合中包含的账户
        return accountConfigRepo.findAll().stream()
                .filter(account -> set.contains(account.getUserName()))
                .toList();
    }
}
