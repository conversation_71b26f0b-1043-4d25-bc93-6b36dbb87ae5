package cn.com.sgcc.business.system.controller;

import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.system.service.SystemService;
import cn.com.sgcc.config.ErrMsg;
import cn.com.sgcc.constants.ProvConsts;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/system")
@CrossOrigin
@Slf4j
public class SystemController {

    @Value("${custom.pmos.provinceCode}")
    String provinceCode;
    @Value("${custom.pmos.queryLimit}")
    String queryLimit;
    @Resource
    private SystemService systemService;

    /**
     * getMenus
     *
     * @return Result<JSONObject>
     */
    @GetMapping("/menus")
    @ErrMsg("获取菜单权限失败")
    public Result<JSONObject> getMenus() {
        return Result.ok(systemService.getMenus());
    }

    /**
     * getConfigs
     *
     * @return Result<Map < String, String>>
     */
    @GetMapping("/configs")
    @ErrMsg("获取配置信息失败")
    public Result<Map<String, Object>> getConfigs() {
        return Result.ok(Map.of(
                "provinceCode", provinceCode,
                "queryLimit", queryLimit,
                "provinceList", ProvConsts.getList()
                                          .stream()
                                          .filter(it -> provinceCode.equals(it.get("value")))
                                          .toList()
        ));
    }

}
