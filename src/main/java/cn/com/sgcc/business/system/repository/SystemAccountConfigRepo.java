package cn.com.sgcc.business.system.repository;

import cn.com.sgcc.business.system.model.SystemAccountConfig;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SystemAccountConfigRepo extends JpaRepository<SystemAccountConfig, Void> {
    /**
     * findFirstByPlantId
     *
     * @param plantId plantId
     * @return Optional<SystemAccountConfig>
     */
    Optional<SystemAccountConfig> findFirstByPlantId(String plantId);

    /**
     * findFirstByUserName
     *
     * @param userName userName
     * @return Optional<SystemAccountConfig>
     */
    Optional<SystemAccountConfig> findFirstByUserName(String userName);
}
