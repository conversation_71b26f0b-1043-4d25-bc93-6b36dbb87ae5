package cn.com.sgcc.business.system.controller;

import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.system.model.SystemAccountConfig;
import cn.com.sgcc.business.system.service.AccountService;
import cn.com.sgcc.config.ErrMsg;
import cn.com.sgcc.constants.RoleConstants;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 账号控制器，用于处理与账号相关的HTTP请求
 */
@RestController
@RequestMapping("/api/v1/account")
@CrossOrigin
@Slf4j
public class AccountController {
    @Resource
    private AccountService accountService;

    /**
     * 获取账号列表
     *
     * @param type 角色类型，用于区分不同类型的账号
     * @return 包含账号配置列表的结果对象
     *
     * 该方法调用服务层获取账号列表，并对返回的账号信息进行敏感信息脱敏处理
     */
    @GetMapping("/accountList")
    @ErrMsg("获取账号列表失败")
    public Result<List<SystemAccountConfig>> getAccountList(RoleConstants.Type type) {
        var plantList = accountService.getAccountList(type);
        plantList.forEach(plant -> {
            // 脱敏处理，避免敏感信息泄露
            plant.setPassWord(null);
            plant.setUKeyCode(null);
            plant.setUKeyPrivate(null);
            plant.setUKeyPassword(null);
        });
        return Result.ok(plantList);
    }
}
