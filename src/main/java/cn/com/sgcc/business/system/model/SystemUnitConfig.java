package cn.com.sgcc.business.system.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "system_unit_config")
public class SystemUnitConfig implements Serializable {
    @Id
    @Column
    private String unitId;
    @Column
    private String unitName;
    @Column
    private String plantId;
    @Column
    private Integer type;
    @Column
    private String grayTag;
    @Column
    private String dispatchId;
    @Column
    private String dispatchName;
    @Column
    private Double limitPower;
    @Column
    private String uName;
    @Column
    private String unitIdIlt;
    @Column
    private String unitNameIlt;
    @Column
    private String generatorSetId;

    /**
     * getTypeName
     *
     * @param type type
     * @return String
     */
    public static String getTypeName(Integer type) {
        return switch (type) {
            case 0 -> "风电";
            case 1 -> "光伏";
            case null, default -> null;
        };
    }
}
