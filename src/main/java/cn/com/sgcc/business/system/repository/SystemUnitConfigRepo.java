package cn.com.sgcc.business.system.repository;

import cn.com.sgcc.business.system.model.SystemUnitConfig;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SystemUnitConfigRepo extends JpaRepository<SystemUnitConfig, String> {
    /**
     * findFirstByUnitId
     *
     * @param unitId unitId
     * @return Optional<SystemUnitConfig>
     */
    Optional<SystemUnitConfig> findFirstByUnitId(String unitId);
}
