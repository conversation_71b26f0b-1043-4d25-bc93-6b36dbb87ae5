package cn.com.sgcc.business.shandong.trade.service;

import cn.com.sgcc.business.shandong.trade.model.UserSdSpotConfig;

import java.util.List;

/**
 * 山东现货申报配置服务接口
 */
public interface SdSpotConfigService {

    /**
     * 查询指定账户和策略日期的配置列表
     *
     * @param account      账户ID
     * @param strategyDate 策略日期
     * @return 配置列表
     */
    List<UserSdSpotConfig> queryConfigList(String account, String strategyDate);

    /**
     * 查询指定账户和日期范围的配置列表
     *
     * @param account   账户ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 配置列表
     */
    List<UserSdSpotConfig> queryConfigListByDateRange(String account, String startDate, String endDate);

    /**
     * 保存配置列表
     *
     * @param account      账户ID
     * @param strategyDate 策略日期
     * @param configList   配置列表
     * @return 是否保存成功
     */
    boolean saveConfigList(String account, String strategyDate, List<UserSdSpotConfig> configList);

    /**
     * 更新申报状态
     *
     * @param account      账户ID
     * @param strategyDate 策略日期
     * @param time         时间
     * @param reportPower  实际申报电量
     * @param status       状态
     */
    void updateReportStatus(String account, String strategyDate, String time, Double reportPower, Integer status);

    /**
     * 删除配置
     *
     * @param account      账户ID
     * @param strategyDate 策略日期
     */
    void deleteConfig(String account, String strategyDate);
}
