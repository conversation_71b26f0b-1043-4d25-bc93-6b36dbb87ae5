package cn.com.sgcc.business.shandong.trade.repository;

import cn.com.sgcc.business.shandong.trade.model.UserSdSpotConfig;
import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface UserSdSpotConfigRepo extends JpaRepository<UserSdSpotConfig, Void> {

    /**
     * 根据账户和策略日期查询配置
     */
    List<UserSdSpotConfig> findByAccountAndStrategyDate(String account, String strategyDate);

    /**
     * 根据账户和策略日期范围查询配置
     */
    @Query(value = """
            select *
            from user_sd_spot_config
            where account = :account
              and strategyDate >= :startDate
              and strategyDate <= :endDate
            order by strategyDate, time""", nativeQuery = true)
    List<UserSdSpotConfig> findByAccountAndDateRange(String account, String startDate, String endDate);

    /**
     * 根据多个账户和策略日期范围查询配置
     */
    @Query(value = """
            select *
            from user_sd_spot_config
            where account in :accounts
              and strategyDate >= :startDate
              and strategyDate <= :endDate
            order by strategyDate, time""", nativeQuery = true)
    List<UserSdSpotConfig> findByAccountsAndDateRange(Collection<String> accounts, String startDate, String endDate);

    /**
     * 删除指定账户和策略日期的配置
     */
    @Modifying
    @Transactional
    @Query(value = """
            delete from user_sd_spot_config
            where account = :account
              and strategyDate = :strategyDate""", nativeQuery = true)
    void deleteByAccountAndStrategyDate(String account, String strategyDate);

    /**
     * 更新申报电量和状态
     */
    @Modifying
    @Transactional
    @Query(value = """
            update user_sd_spot_config
               set reportPower = :reportPower,
                   status = :status,
                   updateTime = :updateTime
            where account = :account
              and strategyDate = :strategyDate
              and time = :time""", nativeQuery = true)
    int updateReportPowerAndStatus(String account, String strategyDate, String time,
                                   Double reportPower, Integer status, String updateTime);
}
