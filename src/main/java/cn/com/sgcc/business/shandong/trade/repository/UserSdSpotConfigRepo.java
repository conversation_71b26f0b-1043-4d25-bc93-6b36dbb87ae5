package cn.com.sgcc.business.shandong.trade.repository;

import cn.com.sgcc.business.shandong.trade.model.UserSdSpotConfig;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface UserSdSpotConfigRepo extends JpaRepository<UserSdSpotConfig, Long> {

    List<UserSdSpotConfig> findByUnitIdAndStrategyDate(String unitId, String strategyDate);

    List<UserSdSpotConfig> findByUnitIdAndStrategyDateBetween(String unitId, String startDate, String endDate);

    @Modifying
    @Transactional
    @Query("DELETE FROM UserSdSpotConfig WHERE unitId = :unitId AND strategyDate = :strategyDate")
    void deleteByUnitIdAndStrategyDate(String unitId, String strategyDate);

    @Modifying
    @Transactional
    @Query("DELETE FROM UserSdSpotConfig WHERE unitId = :unitId AND strategyDate >= :currentDate")
    void deleteFutureByUnitId(String unitId, String currentDate);

    @Modifying
    @Transactional
    void deleteByUnitId(String unitId);

    @Modifying
    @Transactional
    @Query("UPDATE UserSdSpotConfig SET sbdl = :sbdl, updateTime = :now, operator = :operator " +
           "WHERE unitId = :unitId AND strategyDate = :strategyDate AND periodId = :periodId")
    int updateSbdl(String unitId, String strategyDate, String periodId, BigDecimal sbdl,
                   LocalDateTime now, String operator);
}
