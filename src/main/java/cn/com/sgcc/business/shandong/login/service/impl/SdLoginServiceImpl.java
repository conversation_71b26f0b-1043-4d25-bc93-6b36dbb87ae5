package cn.com.sgcc.business.shandong.login.service.impl;

import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.shandong.login.service.SdLoginService;
import cn.com.sgcc.business.system.model.SystemAccountConfig;
import cn.com.sgcc.util.http.ProxySgccRequest;
import cn.com.sgcc.util.http.ReqConfig;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SdLoginServiceImpl implements SdLoginService {

    @Resource
    ProxySgccRequest sgccRequest;

    String host = "pmos.sd.sgcc.com.cn:18080";
    String origin = "https://" + host;

    @Override
    public String loginZcq(SystemAccountConfig accountConfig, LoginInfo loginInfo, String indexPath, String menuName) {
        var ticket = loginInfo.getTicket();
        log.info("开始登录山东电力市场交易系统: {}", loginInfo.getUserName());
        var menuPath = findMenuPath(accountConfig, ticket, indexPath, menuName);
        if (menuPath == null) {
            return null;
        }
        return findCsrfToken(accountConfig, ticket, indexPath, menuPath);
    }

    /**
     * findCsrfToken
     *
     * @param accountConfig accountConfig
     * @param ticket        ticket
     * @param indexPath     indexPath
     * @param menuPath      menuPath
     * @return String
     */
    private String findCsrfToken(SystemAccountConfig accountConfig, String ticket, String indexPath, String menuPath) {
        var respHtml = sgccRequest.get(origin + menuPath,
            ReqConfig.of(accountConfig)
                     .header("Host", host)
                     .header("Referer", "%s%s?ticket=%s".formatted(origin, indexPath, ticket))
        );
        log.info("path: {}, html: {}", menuPath, StringUtils.substring(respHtml, 0, 500));
        var document = Jsoup.parse(respHtml);
        var csrfMetaTag = document.select("meta[name=_csrf]").first();
        if (csrfMetaTag == null) {
            log.warn("没有找到 csrfToken");
            return null;
        }
        var csrfToken = csrfMetaTag.attr("content");
        if (StringUtils.isBlank(csrfToken)) {
            log.warn("csrfToken 为空");
            return null;
        }
        return csrfToken;
    }

    /**
     * findMenuPath
     *
     * @param accountConfig accountConfig
     * @param ticket        ticket
     * @param indexPath     indexPath
     * @param name          name
     * @return String
     */
    private String findMenuPath(SystemAccountConfig accountConfig, String ticket, String indexPath, String name) {
        var respHtml = sgccRequest.get("%s%s?ticket=%s".formatted(origin, indexPath, ticket),
            ReqConfig.of(accountConfig).header("Host", host));
        log.info("path: {}, html: {}", indexPath, StringUtils.substring(respHtml, 0, 500));
        var document = Jsoup.parse(respHtml);
        var first = document.select(".sideList a")
                            .stream()
                            .filter(it -> it.text().contains(name))
                            .findFirst();
        if (first.isEmpty()) {
            log.warn("没有找到菜单: {}", name);
            return null;
        }
        var href = first.get().attr("href");
        if (StringUtils.isBlank(href)) {
            log.warn("菜单: {}, 没有地址链接", name);
            return null;
        }
        return href;
    }
}
