package cn.com.sgcc.business.shandong.trade.controller;

import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.shandong.trade.service.TradeService;
import cn.com.sgcc.config.ErrMsg;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录
 */
@RestController
@RequestMapping("/api/v1/login")
@CrossOrigin
@Slf4j
public class TradeController {
    @Resource
    private TradeService tradeService;

    /**
     * 登录
     *
     * @param plantId 场站 ID
     * @return Result
     */
    @GetMapping("/login")
    @ErrMsg("登录失败")
    public Result<Void> login(String plantId) {
        tradeService.check();
        return Result.ok();
    }

}
