package cn.com.sgcc.business.shandong.trade.service;

import cn.com.sgcc.business.shandong.trade.model.UserSdSpotConfig;
import cn.com.sgcc.business.shandong.trade.repository.UserSdSpotConfigRepo;
import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 山东现货交易服务类
 * 负责处理现货交易相关的业务逻辑，包括配置查询、保存、更新和删除操作
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SdSpotService {

    private final UserSdSpotConfigRepo configRepo;
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 根据单位ID和策略日期查询现货配置信息
     *
     * @param unitId       单位ID
     * @param strategyDate 策略日期
     * @return 返回对应的现货配置列表
     */
    public List<UserSdSpotConfig> getConfigs(String unitId, String strategyDate) {
        return configRepo.findByUnitIdAndStrategyDate(unitId, strategyDate);
    }

    /**
     * 根据单位ID和日期范围查询现货配置信息
     *
     * @param unitId    单位ID
     * @param startDate 起始日期（格式：yyyy-MM-dd）
     * @param endDate   结束日期（格式：yyyy-MM-dd）
     * @return 返回指定日期范围内的现货配置列表
     */
    public List<UserSdSpotConfig> getConfigsByDateRange(String unitId, String startDate, String endDate) {
        return configRepo.findByUnitIdAndStrategyDateBetween(unitId, startDate, endDate);
    }

    /**
     * 保存或更新现货配置信息
     * 1. 先删除该单位和策略日期下的所有现有配置
     * 2. 保存新的配置数据
     *
     * @param unitId       单位ID
     * @param strategyDate 策略日期
     * @param dtos         现货声明数据对象列表
     * @param operator     操作人
     */
    @Transactional
    @PostConstruct
    public void saveOrUpdate(String unitId, String strategyDate, List<Object> dtos, String operator) {
        // 先删除原有数据
        configRepo.deleteByUnitIdAndStrategyDate(unitId, strategyDate);

        // 保存新数据
        LocalDateTime now = LocalDateTime.now();
        dtos.forEach(dto -> {
            UserSdSpotConfig config = new UserSdSpotConfig();
            config.setUnitId(unitId);
            config.setStrategyDate(strategyDate);
            // config.setPeriodId(dto.getPeriodId());
            // config.setSbdl(dto.getSbdl());
            config.setOperator(operator);
            config.setCreateTime(now);
            config.setUpdateTime(now);
            configRepo.save(config);
        });
    }

    /**
     * 更新单个时段的现货配置信息
     *
     * @param unitId       单位ID
     * @param strategyDate 策略日期
     * @param periodId     时段ID
     * @param sbdl         申报电量
     * @param operator     操作人
     * @throws RuntimeException 如果未找到对应记录会抛出异常
     */
    @Transactional
    public void updateSinglePeriod(String unitId, String strategyDate, String periodId,
                                   BigDecimal sbdl, String operator) {
        int updated = configRepo.updateSbdl(unitId, strategyDate, periodId, sbdl,
            LocalDateTime.now(), operator);
        if (updated == 0) {
            throw new RuntimeException("更新失败，未找到对应记录");
        }
    }

    /**
     * 删除指定单位的未来时段配置信息
     *
     * @param unitId      单位ID
     * @param currentDate 当前日期（格式：yyyy-MM-dd）
     */
    @Transactional
    public void deleteFutureConfigs(String unitId) {
        String currentDate = LocalDate.now().format(DATE_FORMAT);
        configRepo.deleteFutureByUnitId(unitId, currentDate);
    }
}
