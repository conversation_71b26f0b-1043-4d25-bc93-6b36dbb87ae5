package cn.com.sgcc.business.shandong.trade.controller;

import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.shandong.trade.model.UserSdSpotConfig;
import cn.com.sgcc.business.shandong.trade.service.SdSpotConfigService;
import cn.com.sgcc.config.ErrMsg;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 山东现货申报配置控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/sd-spot")
public class SdSpotController {

    @Resource
    private SdSpotConfigService spotConfigService;

    /**
     * 获取配置列表
     *
     * @param param 请求参数，包含account和strategyDate
     * @return 配置列表
     */
    @GetMapping("/configList")
    @ErrMsg("获取配置列表失败")
    public Result<List<UserSdSpotConfig>> queryConfigList(@RequestParam String param) {
        JSONObject params = JSONObject.parse(param);
        String account = params.getString("account");
        String strategyDate = params.getString("strategyDate");
        return Result.ok(spotConfigService.queryConfigList(account, strategyDate));
    }

    /**
     * 获取指定日期范围的配置列表
     *
     * @param param 请求参数，包含account、startDate、endDate
     * @return 配置列表
     */
    @GetMapping("/configList/range")
    @ErrMsg("获取配置列表失败")
    public Result<List<UserSdSpotConfig>> queryConfigListByDateRange(@RequestParam String param) {
        JSONObject params = JSONObject.parse(param);
        String account = params.getString("account");
        String startDate = params.getString("startDate");
        String endDate = params.getString("endDate");
        return Result.ok(spotConfigService.queryConfigListByDateRange(account, startDate, endDate));
    }

    /**
     * 保存配置列表
     *
     * @param params 请求参数
     * @return 保存结果
     */
    @PostMapping("/configList")
    @ErrMsg("保存配置失败")
    public Result<Boolean> saveConfigList(@RequestBody JSONObject params) {
        String account = params.getString("account");
        String strategyDate = params.getString("strategyDate");
        List<UserSdSpotConfig> configList = params.getList("configList", UserSdSpotConfig.class);

        if (configList.isEmpty()) {
            return Result.fail("数据为空");
        }

        boolean result = spotConfigService.saveConfigList(account, strategyDate, configList);
        return Result.ok(result);
    }

    /**
     * 更新申报状态
     *
     * @param params 请求参数
     * @return 更新结果
     */
    @PostMapping("/updateStatus")
    @ErrMsg("更新状态失败")
    public Result<String> updateReportStatus(@RequestBody JSONObject params) {
        String account = params.getString("account");
        String strategyDate = params.getString("strategyDate");
        String time = params.getString("time");
        Double reportPower = params.getDouble("reportPower");
        Integer status = params.getInteger("status");

        spotConfigService.updateReportStatus(account, strategyDate, time, reportPower, status);
        return Result.ok();
    }

    /**
     * 删除配置
     *
     * @param params 请求参数
     * @return 删除结果
     */
    @DeleteMapping("/configList")
    @ErrMsg("删除配置失败")
    public Result<String> deleteConfig(@RequestBody JSONObject params) {
        String account = params.getString("account");
        String strategyDate = params.getString("strategyDate");

        spotConfigService.deleteConfig(account, strategyDate);
        return Result.ok();
    }
}
