package cn.com.sgcc.business.shandong.trade.controller;

import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.shandong.trade.model.UserSdSpotConfig;
import cn.com.sgcc.business.shandong.trade.service.SdSpotService;
import java.math.BigDecimal;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 山东现货交易控制器
 * 提供山东现货交易相关的API接口，包括配置获取、保存、更新和删除操作
 */
@RestController
@RequestMapping("/api/sd-spot")
@RequiredArgsConstructor
public class SdSpotController {

    private final SdSpotService spotService;

    /**
     * 获取指定单位和策略日期的现货配置信息
     *
     * @param unitId       单位ID，用于标识操作的单位
     * @param strategyDate 策略日期，用于查询特定日期的配置
     * @return 包含用户现货配置的Result对象，包含配置列表
     */
    @GetMapping("/configs")
    public Result<List<UserSdSpotConfig>> getConfigs(
        @RequestParam String unitId,
        @RequestParam String strategyDate) {
        return Result.ok(spotService.getConfigs(unitId, strategyDate));
    }

    /**
     * 获取指定单位在日期范围内的现货配置信息
     *
     * @param unitId    单位ID，用于标识操作的单位
     * @param startDate 起始日期，格式为YYYY-MM-DD
     * @param endDate   结束日期，格式为YYYY-MM-DD
     * @return 包含用户现货配置的Result对象，包含配置列表
     */
    @GetMapping("/configs/range")
    public Result<List<UserSdSpotConfig>> getConfigsByDateRange(
        @RequestParam String unitId,
        @RequestParam String startDate,
        @RequestParam String endDate) {
        return Result.ok(spotService.getConfigsByDateRange(unitId, startDate, endDate));
    }

    /**
     * 保存或更新现货配置信息
     *
     * @param unitId       单位ID，用于标识操作的单位
     * @param strategyDate 策略日期，用于确定配置的生效时间
     * @param dtos         现货声明数据对象列表，包含需要保存或更新的配置数据
     * @param operator     操作人，记录操作者信息
     * @return 操作结果，包含成功或失败的状态信息
     */
    @PostMapping("/save")
    public Result<Void> saveOrUpdate(
        @RequestParam String unitId,
        @RequestParam String strategyDate,
        @RequestBody List<Object> dtos,
        @RequestParam String operator) {
        spotService.saveOrUpdate(unitId, strategyDate, dtos, operator);
        return Result.ok();
    }

    /**
     * 更新单个时段的现货配置信息
     *
     * @param unitId       单位ID，用于标识操作的单位
     * @param strategyDate 策略日期，用于确定配置的生效时间
     * @param periodId     时段ID，需要更新的具体时段标识
     * @param sbdl         申报电量，更新后的电量数值
     * @param operator     操作人，记录操作者信息
     * @return 操作结果，包含成功或失败的状态信息
     */
    @PostMapping("/update")
    public Result<Void> updateSinglePeriod(
        @RequestParam String unitId,
        @RequestParam String strategyDate,
        @RequestParam String periodId,
        @RequestParam BigDecimal sbdl,
        @RequestParam String operator) {
        spotService.updateSinglePeriod(unitId, strategyDate, periodId, sbdl, operator);
        return Result.ok();
    }

    /**
     * 删除指定单位的未来时段配置信息
     *
     * @param unitId 单位ID，用于标识需要删除配置的单位
     * @return 操作结果，包含删除状态信息
     */
    @DeleteMapping("/future")
    public Result<Void> deleteFutureConfigs(@RequestParam String unitId) {
        spotService.deleteFutureConfigs(unitId);
        return Result.ok();
    }
}
