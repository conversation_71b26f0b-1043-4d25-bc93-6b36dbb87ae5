package cn.com.sgcc.business.shandong.trade.model.dto;

import cn.com.sgcc.business.shandong.trade.model.UserSdSpotConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;

/**
 * 山东现货申报配置DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class UserSdSpotConfigDto extends UserSdSpotConfig implements Serializable {
    
    private Boolean disabled; // 是否禁用
    private String accountName; // 账户名称
    private String statusText; // 状态文本
    private String strategyTypeText; // 申报类型文本
    private String reportTypeText; // 申报类型文本

    /**
     * 构造函数
     *
     * @param config 配置对象
     */
    public UserSdSpotConfigDto(UserSdSpotConfig config) {
        BeanUtils.copyProperties(config, this);
    }
}
