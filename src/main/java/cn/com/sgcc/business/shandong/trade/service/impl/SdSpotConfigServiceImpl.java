package cn.com.sgcc.business.shandong.trade.service.impl;

import cn.com.sgcc.business.shandong.trade.model.UserSdSpotConfig;
import cn.com.sgcc.business.shandong.trade.repository.UserSdSpotConfigRepo;
import cn.com.sgcc.business.shandong.trade.service.SdSpotConfigService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 山东现货申报配置服务实现类
 */
@Slf4j
@Service
public class SdSpotConfigServiceImpl implements SdSpotConfigService {

    @Resource
    private UserSdSpotConfigRepo configRepo;

    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public List<UserSdSpotConfig> queryConfigList(String account, String strategyDate) {
        return configRepo.findByAccountAndStrategyDate(account, strategyDate);
    }

    @Override
    public List<UserSdSpotConfig> queryConfigListByDateRange(String account, String startDate, String endDate) {
        return configRepo.findByAccountAndDateRange(account, startDate, endDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveConfigList(String account, String strategyDate, List<UserSdSpotConfig> configList) {
        try {
            // 先删除原有数据
            configRepo.deleteByAccountAndStrategyDate(account, strategyDate);

            // 保存新数据
            String now = LocalDateTime.now().format(DATE_TIME_FORMAT);
            for (UserSdSpotConfig config : configList) {
                config.setAccount(account);
                config.setStrategyDate(strategyDate);
                config.setCreateTime(now);
                config.setUpdateTime(now);
                if (config.getReportType() == null) {
                    config.setReportType(1); // 默认手动
                }
                if (config.getStatus() == null) {
                    config.setStatus(0); // 默认未申报
                }
            }
            configRepo.saveAll(configList);
            return true;
        } catch (Exception e) {
            log.error("保存配置失败: account={}, strategyDate={}", account, strategyDate, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateReportStatus(String account, String strategyDate, String time, Double reportPower, Integer status) {
        String updateTime = LocalDateTime.now().format(DATE_TIME_FORMAT);
        int updated = configRepo.updateReportPowerAndStatus(account, strategyDate, time, reportPower, status, updateTime);
        if (updated == 0) {
            throw new RuntimeException("更新失败，未找到对应记录");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteConfig(String account, String strategyDate) {
        configRepo.deleteByAccountAndStrategyDate(account, strategyDate);
    }
}
