package cn.com.sgcc.business.shandong.trade.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "user_sd_spot_config")
@IdClass(UserSdSpotConfig.PrimaryKey.class)
public class UserSdSpotConfig implements Serializable {
    @Id
    @Column
    private String account;
    @Id
    @Column
    private String strategyDate;
    @Id
    @Column
    private String time;
    @Column
    private Integer strategyType;
    @Column
    private Integer reportType;
    @Column
    private Double power;
    @Column
    private Double reportPower;
    @Column
    private Integer status;
    @Column
    private String createTime;
    @Column
    private String updateTime;

    @Data
    static class PrimaryKey implements Serializable {
        private String account;
        private String strategyDate;
        private String time;
    }
}
