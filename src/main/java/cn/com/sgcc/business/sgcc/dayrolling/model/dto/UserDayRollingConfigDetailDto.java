package cn.com.sgcc.business.sgcc.dayrolling.model.dto;

import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingConfigDetail;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingConfigDetailLog;
import java.util.List;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@Data
public class UserDayRollingConfigDetailDto {
    String unitId;
    String strategyDate;
    String declareDate;
    List<UserDayRollingConfigDetail> details;
    List<UserDayRollingConfigDetailLog> detailLogs;

    /**
     * UserDayRollingConfigDetailDto Constructor
     */
    public UserDayRollingConfigDetailDto() {
    }

    /**
     * UserDayRollingConfigDetailDto Constructor
     *
     * @param detail detail
     */
    public UserDayRollingConfigDetailDto(UserDayRollingConfigDetail detail) {
        BeanUtils.copyProperties(detail, this);
    }
}
