package cn.com.sgcc.business.sgcc.dayrolling.controller;

import cn.com.sgcc.business.common.model.dto.Page;
import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingConfigDetail;
import cn.com.sgcc.business.sgcc.dayrolling.model.dto.UserDayRollingConfigDetailDto;
import cn.com.sgcc.business.sgcc.dayrolling.model.excel.UserDayRollingRecordExport;
import cn.com.sgcc.business.sgcc.dayrolling.service.DayRollingService;
import cn.com.sgcc.config.ErrMsg;
import cn.com.sgcc.config.role.HasRole;
import cn.com.sgcc.config.role.RoleCondition;
import cn.com.sgcc.constants.RoleConstants;
import cn.com.sgcc.util.SseEmitters;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * DayRollingController
 */
@Conditional({RoleCondition.class})
@HasRole(RoleConstants.Type.DAY_ROLLING)
@RestController
@RequestMapping("/api/v1/dayRolling")
@CrossOrigin
@Slf4j
public class DayRollingController {

    public static final SseEmitters EMITTERS = SseEmitters.init();

    @Resource
    private DayRollingService dayRollingService;

    /**
     * stream
     *
     * @return SseEmitter
     */
    @GetMapping("/sse")
    @ErrMsg("SSE日滚动通知失败")
    public SseEmitter stream() {
        return EMITTERS.createNew(DayRollingController.class);
    }

    /**
     * queryConfigList
     *
     * @param param param
     * @return Result<List < UserDayRollingConfigDetail>>
     */
    @GetMapping("/configList")
    @ErrMsg("获取策略列表失败")
    public Result<List<UserDayRollingConfigDetail>> queryConfigList(@RequestParam String param) {
        JSONObject params = JSONObject.parse(param);
        String unitId = params.getString("unitId");
        Integer type = params.getInteger("type");
        String strategyDate = params.getString("strategyDate");
        return Result.ok(dayRollingService.queryConfigList(unitId, strategyDate, type));
    }

    /**
     * queryRecordList
     *
     * @param param param
     * @return Result<Page < List < UserDayRollingConfigDetailDto>>>
     */
    @GetMapping("/recordList")
    @ErrMsg("获取记录失败")
    public Result<Page<List<UserDayRollingConfigDetailDto>>> queryRecordList(@RequestParam String param) {
        JSONObject params = JSONObject.parse(param);
        String unitId = params.getString("unitId");
        Integer type = params.getInteger("type");
        JSONObject pageParam = params.getJSONObject("page");
        Integer page = pageParam.getInteger("page");
        Integer pageSize = pageParam.getInteger("pageSize");
        return Result.ok(dayRollingService.queryRecordList(unitId, type, new Page<>(page, pageSize)));
    }

    /**
     * saveConfigList
     *
     * @param params params
     * @return Result<Boolean>
     */
    @PutMapping("/configList")
    @ErrMsg("新增策略失败")
    public Result<Boolean> saveConfigList(@RequestBody JSONObject params) {
        String strategyDate = params.getString("strategyDate");
        String declareDate = params.getString("declareDate");
        Integer type = params.getInteger("type");
        List<String> unitIdList = params.getList("unitIdList", String.class);
        List<UserDayRollingConfigDetail> configList = params.getList("configList", UserDayRollingConfigDetail.class);
        if (configList.isEmpty()) {
            return Result.fail("数据为空");
        }
        return Result.ok(dayRollingService.saveConfigList(strategyDate, declareDate, unitIdList, type, configList));
    }

    /**
     * exportRecordList
     *
     * @param param param
     * @return Result<Map < String, Collection < UserDayRollingRecordExport>>>
     */
    @PostMapping("/exportRecordList")
    @ErrMsg("导出失败")
    public Result<Map<String, Collection<UserDayRollingRecordExport>>> exportRecordList(@RequestBody String param) {
        JSONObject params = JSONObject.parse(param);
        List<String> unitIdList = params.getList("unitIdList", String.class);
        String strategyDate = params.getString("strategyDate");
        Integer type = params.getInteger("type");
        return Result.ok(dayRollingService.exportRecordData(unitIdList, strategyDate, type));
    }

    /**
     * exportAllRecordList
     *
     * @param param param
     * @return Result<Map < String, Collection < UserDayRollingRecordExport>>>
     */
    @PostMapping("/exportAllRecordList")
    @ErrMsg("导出失败")
    public Result<Map<String, Collection<UserDayRollingRecordExport>>> exportAllRecordList(@RequestBody String param) {
        JSONObject params = JSONObject.parse(param);
        Integer type = params.getInteger("type");
        return Result.ok(dayRollingService.exportRecordData(null, null, type));
    }
}
