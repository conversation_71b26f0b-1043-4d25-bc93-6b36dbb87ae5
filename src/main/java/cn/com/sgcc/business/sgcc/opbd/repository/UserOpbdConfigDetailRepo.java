package cn.com.sgcc.business.sgcc.opbd.repository;

import cn.com.sgcc.business.sgcc.opbd.model.UserOpbdConfigDetail;
import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * UserOpbdConfigDetailRepo
 */
@Repository
public interface UserOpbdConfigDetailRepo extends JpaRepository<UserOpbdConfigDetail, Void> {
    /**
     * findAllByParams
     *
     * @param unitIds   unitIds
     * @param startDate startDate
     * @param endDate   endDate
     * @return List<UserOpbdConfigDetail>
     */
    @Query(value = """
            select *
            from user_opbd_config_detail
            where unitId in :unitIds
              and strategyDate >= :startDate
              and strategyDate <= :endDate
            order by strategyDate, createTime""", nativeQuery = true)
    List<UserOpbdConfigDetail> findAllByParams(Collection<String> unitIds, String startDate, String endDate);

    /**
     * findAllByStrategyDateGreaterThanEqual
     *
     * @param strategyDate strategyDate
     * @return List<UserOpbdConfigDetail>
     */
    List<UserOpbdConfigDetail> findAllByStrategyDateGreaterThanEqual(String strategyDate);

    /**
     * findAllByUnitIdAndStrategyDateIn
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @return List<UserOpbdConfigDetail>
     */
    List<UserOpbdConfigDetail> findAllByUnitIdAndStrategyDateIn(String unitId, Collection<String> strategyDate);

    /**
     * findAllNotSuccess
     *
     * @param strategyDate strategyDate
     * @return List<UserOpbdConfigDetail>
     */
    @Query(value = """
            select *
            from user_opbd_config_detail
            where strategyDate = :strategyDate
              and status != 1""", nativeQuery = true)
    List<UserOpbdConfigDetail> findAllNotSuccess(String strategyDate);

    /**
     * removeByParams
     *
     * @param unitIds   unitIds
     * @param startDate startDate
     * @param endDate   endDate
     * @param timeParts timeParts
     */
    @Modifying
    @Transactional
    @Query(value = """
            delete
            from user_opbd_config_detail
            where unitId in :unitIds
              and strategyDate >= :startDate
              and strategyDate <= :endDate
              and timePart in :timeParts
            """, nativeQuery = true)
    void removeByParams(Collection<String> unitIds, String startDate, String endDate, Collection<Integer> timeParts);

    /**
     * removeByUnitIdAndStrategyDate
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     */
    void removeByUnitIdAndStrategyDate(String unitId, String strategyDate);

    /**
     * updatePowerLimit
     *
     * @param detail detail
     */
    @Modifying
    @Transactional
    @Query(value = """
            update user_opbd_config_detail
               set powerLimit = :#{#detail.powerLimit}
            where unitId = :#{#detail.unitId}
              and strategyDate = :#{#detail.strategyDate}
              and startTime = :#{#detail.startTime}
              and endTime = :#{#detail.endTime}""", nativeQuery = true)
    void updatePowerLimit(UserOpbdConfigDetail detail);

    /**
     * findAllSucceed
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @return List<UserOpbdConfigDetail>
     */
    @Query(value = """
            select *
            from user_opbd_config_detail
            where unitId = :unitId
              and strategyDate = :strategyDate
              and status = 1""", nativeQuery = true)
    List<UserOpbdConfigDetail> findAllSucceed(String unitId, String strategyDate);
}
