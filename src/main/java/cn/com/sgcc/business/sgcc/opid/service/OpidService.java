package cn.com.sgcc.business.sgcc.opid.service;

import cn.com.sgcc.business.common.model.dto.Page;
import cn.com.sgcc.business.sgcc.opid.model.UserOpidConfigDetail;
import cn.com.sgcc.business.sgcc.opid.model.dto.UserOpidConfigDto;
import cn.com.sgcc.business.sgcc.opid.model.excel.StrategyConfigExport;
import cn.com.sgcc.business.sgcc.opid.model.excel.StrategyConfigImport;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * OpidService接口定义了用于管理和查询OPID（OnePlus ID）服务配置的接口方法
 */
public interface OpidService {

    /**
     * 查询指定单位ID的配置列表
     *
     * @param unitId    单位ID，用于标识查询的目标单位
     * @param startDate 开始日期，查询范围的起始时间
     * @return 返回配置详情列表
     */
    List<UserOpidConfigDetail> queryConfigList(String unitId, String startDate);

    /**
     * 分页查询指定单位ID的记录列表
     *
     * @param unitId 单位ID，用于标识查询的目标单位
     * @param page   分页对象，包含分页信息和数据
     * @return 返回分页的记录列表
     */
    Page<List<UserOpidConfigDto>> queryRecordList(String unitId, Page<List<UserOpidConfigDto>> page);

    /**
     * deleteRecordList
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     */
    void deleteRecordList(String unitId, String strategyDate);

    /**
     * 保存或更新配置列表
     *
     * @param unitIdList   单位ID列表，标识需要保存配置的单位集合
     * @param strategyType 类型，标识配置的类型
     * @param reportType   类型，标识配置的类型
     * @param startDate    开始日期，配置生效的起始时间
     * @param endDate      结束日期，配置生效的结束时间
     * @param timePartList 时间段列表，配置生效的具体时间段
     * @param configList   配置详情列表，包含具体的配置信息
     * @return 返回保存操作是否成功
     */
    boolean saveConfigList(List<String> unitIdList, Integer strategyType, Integer reportType,
                           String startDate, String endDate,
                           List<Integer> timePartList, List<UserOpidConfigDetail> configList);

    /**
     * 导出记录数据
     *
     * @param unitIdList 单位ID列表，标识需要导出记录数据的单位集合
     * @param startDate  开始日期，导出数据的时间范围起始
     * @param endDate    结束日期，导出数据的时间范围结束
     * @return 返回导出的数据，以单位ID为键，对应的策略配置集合为值
     */
    Map<String, Collection<StrategyConfigExport>> exportRecordData(List<String> unitIdList,
                                                                   String startDate, String endDate);

    /**
     * 导入记录数据
     *
     * @param unitIdList   单位ID列表，标识需要导入记录数据的单位集合
     * @param strategyType 类型，标识导入数据的类型
     * @param reportType   类型，标识导入数据的类型
     * @param importList   导入的数据列表，包含具体的导入信息
     * @return 返回导入操作是否成功
     */
    boolean importRecordData(List<String> unitIdList, Integer strategyType, Integer reportType,
                             List<StrategyConfigImport> importList);
}

