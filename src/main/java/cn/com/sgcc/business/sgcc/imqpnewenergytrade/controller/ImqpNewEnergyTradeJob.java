package cn.com.sgcc.business.sgcc.imqpnewenergytrade.controller;

import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.sgcc.imqpnewenergytrade.model.ImqpNewEnergyTradeConfig;
import cn.com.sgcc.business.sgcc.imqpnewenergytrade.service.ImqpNewEnergyTradeJobService;
import cn.com.sgcc.config.ErrMsg;
import cn.com.sgcc.config.role.HasRole;
import cn.com.sgcc.config.role.RoleCondition;
import cn.com.sgcc.constants.DateConsts;
import cn.com.sgcc.constants.RoleConstants;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Conditional;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * ImqpNewEnergyTradeJob
 */
@Conditional({RoleCondition.class})
@HasRole(RoleConstants.Type.IMQP_NEW_ENERGY_TRADE)
@RestController
@RequestMapping("/api/v1/job/imqpNewEnergyTrade")
@CrossOrigin
@Slf4j
public class ImqpNewEnergyTradeJob {

    @Resource
    private ImqpNewEnergyTradeJobService jobService;

    /**
     * detect
     *
     * @param param param
     * @return Result<String>
     */
    @GetMapping("/detectJobByUnitId")
    @ErrMsg("更新失败")
    public Result<String> detect(@RequestParam String param) {
        JSONObject params = JSONObject.parse(param);
        var unitId = params.getString("unitId");
        var strategyDate = params.getString("strategyDate");
        log.info("更新月内保量保价新能源交易任务开始, unitId={}, strategyDate={}", unitId, strategyDate);
        var declareDate = LocalDate.now().format(DateConsts.DATE_FORMAT);
        jobService.updateTridList(unitId, strategyDate, declareDate);
        log.info("更新月内保量保价新能源交易任务结束");
        if (declareDate.startsWith(strategyDate)) {
            var tradeConfig = new ImqpNewEnergyTradeConfig();
            tradeConfig.setUnitId(unitId);
            tradeConfig.setStrategyDate(strategyDate);
            tradeConfig.setDeclareDate(declareDate);
            jobService.detectJob(tradeConfig);
        }
        return Result.ok();
    }

    /**
     * detect
     */
    @Scheduled(cron = "${custom.imqpNewEnergyTrade.listCron}")
    public void detect() {
        if (!RoleCondition.hasRole(RoleConstants.Type.IMQP_NEW_ENERGY_TRADE)) {
            return;
        }
        var strategyDate = LocalDate.now().format(DateConsts.MONTH_FORMAT);
        log.info("获取月内保量保价新能源交易定时任务开始, strategyDate={}", strategyDate);
        detect(JSONObject.of("strategyDate", strategyDate).toString());
        jobService.updateTridList(null, strategyDate, null);
        jobService.detectJob(null);
        log.info("获取月内保量保价新能源交易定时任务结束");
    }

    /**
     * 启动时判断是否有未完成的交易，根据开始时间创建定时任务
     */
    @PostConstruct
    @Order
    public void declare() {
        if (!RoleCondition.hasRole(RoleConstants.Type.IMQP_NEW_ENERGY_TRADE)) {
            return;
        }
        jobService.detectJob(null);
    }
}
