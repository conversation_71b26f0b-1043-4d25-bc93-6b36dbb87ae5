package cn.com.sgcc.business.sgcc.autologin.service.impl;

import ar.com.hjg.pngj.ImageLineHelper;
import ar.com.hjg.pngj.PngReader;
import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.sgcc.autologin.service.SgccLoginService;
import cn.com.sgcc.business.system.model.SystemAccountConfig;
import cn.com.sgcc.util.ImageComparator;
import cn.com.sgcc.util.SgccResp;
import cn.com.sgcc.util.http.ProxySgccRequest;
import cn.com.sgcc.util.http.ReqConfig;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * SgccLoginServiceImpl
 */
@Slf4j
@Service
public class SgccLoginServiceImpl implements SgccLoginService {

    @Value("${custom.pmos.login.httpWaitMilliSecond}")
    long httpWaitMilliSecond;
    @Value("${custom.pmos.login.blockPuzzle.retry}")
    int retry;
    @Value("${custom.pmos.login.blockPuzzle.maxPixelMatch}")
    int maxPixelMatch;
    @Value("${custom.pmos.login.blockPuzzle.filter.r}")
    int maxR;
    @Value("${custom.pmos.login.blockPuzzle.filter.g}")
    int maxG;
    @Value("${custom.pmos.login.blockPuzzle.filter.b}")
    int maxB;
    @Value("${custom.pmos.login.blockPuzzle.image.width}")
    int width;
    @Value("${custom.pmos.login.blockPuzzle.image.height}")
    int height;
    @Value("${custom.pmos.login.blockPuzzle.image.jigsawWidth}")
    int jigsawWidth;
    @Value("${custom.pmos.login.blockPuzzle.image.showWidth}")
    int showWidth;
    @Value("${custom.pmos.login.smsUrl}")
    String smsUrl;

    @Resource
    private ProxySgccRequest proxySgccRequest;

    @Override
    public LoginInfo login(SystemAccountConfig accountConfig) {
        var reqConfig = ReqConfig.of(accountConfig, "/outNet", httpWaitMilliSecond);
        var loginObj = new JSONObject();
        loginObj.put("cookieTicketKey", "Admin-Token");
        loginObj.put("loginName", accountConfig.getUserName());
        loginObj.put("username", accountConfig.getUserName());
        loginObj.put("authKey", "");
        loginObj.put("secureCode", "");
        loginObj.put("dnInfo", accountConfig.getUKeyCode());
        loginObj.put("isCfcaLogin", false);
        loginObj.put("loginFrom", 1);
        loginObj.put("clientTag", "OUTNET_BROWSE");
        loginObj.put("randomCode", "ERWEFX");
        loginObj.put("twoFactorType", 11);
        loginObj.put("captchaVerification", "");
        loginObj.put("origin", "");
        var captchaResp = captcha(accountConfig);
        if (captchaResp == null) {
            log.warn("解析验证码失败");
            return null;
        }
        loginObj.put("captchaVerification", captchaResp.getString("captchaVerification"));
        // 获取登录加密所需参数
        var secureKeyGetRespStr = proxySgccRequest.post("/px-common-authcenter/auth/v2/secureKey/get",
            JSONObject.of(), reqConfig);
        var secureKeyGetResp = JSONObject.parseObject(secureKeyGetRespStr);
        if (SgccResp.fail(secureKeyGetResp)) {
            log.warn("获取登录加密所需参数失败: {}", secureKeyGetRespStr);
            return null;
        } else {
            var secureKeyGetRespData = SgccResp.jsonData(secureKeyGetResp);
            loginObj.put("secureCode", secureKeyGetRespData.getString("secureCode"));
            var authKey = SmUtil.sm2(null, secureKeyGetRespData.getString("pubKey"))
                                .encryptHex(accountConfig.getPassWord().getBytes(StandardCharsets.UTF_8),
                                    KeyType.PublicKey);
            loginObj.put("authKey", authKey);
            log.info("authKey: {}", authKey);
        }
        var loginObj2 = new JSONObject();
        loginObj2.put("authKey", loginObj.getString("authKey"));
        loginObj2.put("caLogin", false);
        loginObj2.put("captchaCheck", loginObj.getString("captchaVerification"));
        loginObj2.put("cookieTicketKey", loginObj.getString("cookieTicketKey"));
        loginObj2.put("dnInfo", "");
        loginObj2.put("loginName", loginObj.getString("loginName"));
        loginObj2.put("loginType", "PASSWD");
        loginObj2.put("mfAuthService", "NULL");
        loginObj2.put("origin", "");
        loginObj2.put("randomCode", "");
        loginObj2.put("reqDoc", "");
        loginObj2.put("secureCode", loginObj.getString("secureCode"));
        loginObj2.put("signValue", "");
        var loginRespStr = proxySgccRequest.post("/px-common-authcenter/auth/v2/login", loginObj2, reqConfig);
        var loginResp = JSONObject.parseObject(loginRespStr);
        if (SgccResp.fail(loginResp)) {
            log.warn("登录失败: {}", loginRespStr);
            return null;
        }
        var loginRespData = SgccResp.jsonData(loginResp);
        if (!loginRespData.getBooleanValue("nextValidationStep", true)) {
            var token = loginRespData.getString("token");
            if (accountConfig.getUseUKey() == 0 && accountConfig.getUseSMS() == 0 && StringUtils.isNotBlank(token)) {
                log.info("登录成功: {}", loginRespStr);
                return new LoginInfo(accountConfig, new LoginInfo.LoginResp(0, "Success", token));
            }
            log.warn("登录失败: {}", loginRespStr);
            return null;
        }
        var jwt = loginRespData.getString("jwt");
        if (accountConfig.getUseUKey() == 1 && accountConfig.getUseSMS() == 0) {
            var verifyRespStr = proxySgccRequest.post("/px-common-authcenter/auth/v2/verify", JSONObject.of(
                "cookieTicketKey", "Admin-Token",
                "jwt", jwt,
                "loginType", "CF_CA",
                "secretKey", accountConfig.getUKeyCode(),
                "skipRealNameAuth", false
            ), reqConfig);
            var verifyResp = JSONObject.parseObject(verifyRespStr);
            if (SgccResp.ok(verifyResp)) {
                log.info("登录成功: {}", verifyRespStr);
                var verifyRespData = SgccResp.jsonData(verifyResp);
                var token = verifyRespData.getString("token");
                return new LoginInfo(accountConfig, new LoginInfo.LoginResp(0, "Success", token));
            } else {
                log.warn("登录验证失败: {}", verifyRespStr);
                return null;
            }
        } else if (accountConfig.getUseUKey() == 0 && accountConfig.getUseSMS() == 1) {
            var mobile = loginRespData.getString("mobile");
            if (!accountConfig.getPhone().equals(mobile)) {
                log.warn("配置的手机号与交易中心不一致: {}", mobile);
                return null;
            }
            var sendSmsRespStr = HttpUtil.post(smsUrl + "/sms/send", JSONObject.of(
                "phone", mobile,
                "account", accountConfig.getUserName(),
                "expireSecond", 300,
                "lockSecond", 60
            ).toString());
            var sendSmsResp = JSONObject.parseObject(sendSmsRespStr);
            if (!sendSmsResp.getBooleanValue("success", false)) {
                log.warn("发送验证码请求失败: {}", sendSmsRespStr);
                return null;
            }
            var smsId = sendSmsResp.getString("obj");
            var smsRespStr = proxySgccRequest.post("/px-common-authcenter/auth/v2/smsCode/get", JSONObject.of(
                "jwt", jwt,
                "mobile", mobile
            ), reqConfig);
            var smsResp = JSONObject.parseObject(smsRespStr);
            if (SgccResp.fail(smsResp)) {
                log.warn("发送验证码失败: {}", smsRespStr);
                return null;
            }
            var smsRespData = SgccResp.jsonData(smsResp);
            var expireTimeInSecond = smsRespData.getInteger("ExpireTime");
            var smsCode = "";
            var pattern = Pattern.compile(accountConfig.getSmsRegex());
            for (int i = 0; i < expireTimeInSecond / 5; i++) {
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("异常中断", e);
                }
                var getSmsRespStr = HttpUtil.post(smsUrl + "/sms/get", JSONObject.of(
                    "id", smsId,
                    "phone", mobile,
                    "lockSecond", 60
                ).toString(), (int) TimeUnit.SECONDS.toMillis(expireTimeInSecond));
                try {
                    var getSmsResp = JSONObject.parseObject(getSmsRespStr);
                    if (!getSmsResp.getBooleanValue("success", false)) {
                        log.warn("获取短信验证码时失败: {}", getSmsRespStr);
                        continue;
                    }
                    var obj = getSmsResp.getString("obj");
                    var matcher = pattern.matcher(obj);
                    if (matcher.find()) {
                        smsCode = matcher.group(1);
                        break;
                    }
                } catch (Exception e) {
                    log.warn("解析验证码失败: {}", getSmsRespStr);
                }
            }
            if (StringUtils.isBlank(smsCode)) {
                log.warn("没有获取到短信验证码");
                return null;
            }
            var verifyRespStr = proxySgccRequest.post("/px-common-authcenter/auth/v2/verify", JSONObject.of(
                "cookieTicketKey", "Admin-Token",
                "jwt", jwt,
                "loginType", "SMS",
                "secretKey", smsCode,
                "skipRealNameAuth", false
            ), reqConfig);
            var sgccVerifyResp = JSONObject.parseObject(verifyRespStr);
            if (SgccResp.ok(sgccVerifyResp)) {
                log.info("登录成功: {}", verifyRespStr);
                var sgccVerifyData = SgccResp.jsonData(sgccVerifyResp);
                var token = sgccVerifyData.getString("token");
                return new LoginInfo(accountConfig, new LoginInfo.LoginResp(0, "Success", token));
            } else {
                log.warn("登录验证失败: {}", verifyRespStr);
                return null;
            }
        } else {
            log.warn("暂不支持UKey+SMS");
            return null;
        }
    }

    /**
     * captcha
     *
     * @param accountConfig accountConfig
     * @return JSONObject
     */
    private JSONObject captcha(SystemAccountConfig accountConfig) {
        for (int i = 0; i < retry; i++) {
            log.info("第 {} 次解析验证码", i + 1);
            JSONObject resJson;
            String token;
            {
                var respStr = proxySgccRequest.post("/px-common-authcenter/auth/v2/captcha/get",
                    JSONObject.of("captchaType", "blockPuzzle"),
                    ReqConfig.of(accountConfig, "/outNet", httpWaitMilliSecond));
                var sgccResp = JSONObject.parseObject(respStr);
                if (SgccResp.fail(sgccResp)) {
                    log.warn("获取验证码失败: {}", respStr);
                    continue;
                }
                var jsonData = SgccResp.jsonData(sgccResp);
                var repData = jsonData.getJSONObject("repData");
                token = repData.getString("token");
                var originalImageBase64 = repData.getString("originalImageBase64");
                var jigsawImageBase64 = repData.getString("jigsawImageBase64");
                var secretKey = repData.getString("secretKey");
                try {
                    resJson = blockPuzzle(secretKey, token, jigsawImageBase64, originalImageBase64);
                    log.info("resJson: {}", resJson);
                } catch (Exception e) {
                    log.error("解析验证码失败", e);
                    continue;
                }
            }
            var url = "/px-common-authcenter/auth/v2/captcha/check";
            var respStr = proxySgccRequest.post(url, JSONObject.of(
                "captchaType", "blockPuzzle",
                "pointJson", resJson.getString("pointJson"),
                "token", token
            ), ReqConfig.of(accountConfig, httpWaitMilliSecond));
            var sgccResp = JSONObject.parseObject(respStr);
            if (SgccResp.ok(sgccResp) && "0000".equals(SgccResp.jsonData(sgccResp).getString("repCode"))) {
                return JSONObject.of("sgccResp", sgccResp,
                    "captchaVerification", resJson.getString("captchaVerification"));
            } else {
                log.warn("验证码验证失败: {}", respStr);
            }
        }
        return null;
    }

    /**
     * blockPuzzle
     *
     * @param secretKey           secretKey
     * @param token               token
     * @param jigsawImageBase64   jigsawImageBase64
     * @param originalImageBase64 originalImageBase64
     * @return JSONObject
     * @throws IOException IOException
     */
    private JSONObject blockPuzzle(String secretKey, String token,
                                   String jigsawImageBase64, String originalImageBase64) throws IOException {
        // var original = ImageIO.read(new ByteArrayInputStream(Base64.getDecoder().decode(originalImageBase64)));
        var original = new PngReader(new ByteArrayInputStream(Base64.getDecoder().decode(originalImageBase64)));
        var originalData = getRGBAData(original);
        saveImage(originalData, original.imgInfo.cols, original.imgInfo.rows, "original");
        // var jigsaw = ImageIO.read(new ByteArrayInputStream(Base64.getDecoder().decode(jigsawImageBase64)));
        var jigsaw = new PngReader(new ByteArrayInputStream(Base64.getDecoder().decode(jigsawImageBase64)));
        var jigsawData = getRGBAData(jigsaw);
        saveImage(jigsawData, jigsaw.imgInfo.cols, jigsaw.imgInfo.rows, "jigsaw");

        var greyOriginalImage = imageFilter(originalData);
        saveImage(greyOriginalImage, original.imgInfo.cols, original.imgInfo.rows, "greyOriginalImage");

        var finalMovePixel = 0;
        for (var movePixel = 0; movePixel < width - jigsawWidth; movePixel++) {
            var tempImage = Arrays.copyOf(originalData, originalData.length);
            bitblt(jigsawData, tempImage, movePixel);
            var greyTempImage = imageFilter(tempImage);
            saveImage(greyTempImage, original.imgInfo.cols, original.imgInfo.rows,
                "%03d.greyTempImage".formatted(movePixel));
            int compare;
            try {
                compare = ImageComparator.compare(greyOriginalImage, greyTempImage, null, width, height);
                if (compare < maxPixelMatch) {
                    finalMovePixel = movePixel;
                }
            } catch (Exception e) {
                log.error("验证码解析失败", e);
                throw e;
            }
        }
        var offset = (width * Math.ceil((finalMovePixel * showWidth * 1D) / width)) / showWidth;
        var bigDecimal = new BigDecimal(offset);
        offset = bigDecimal.setScale(16 - bigDecimal.toPlainString().indexOf('.') + 1, RoundingMode.HALF_UP)
                           .doubleValue();
        log.info("min: {}, offset: {}", finalMovePixel, offset);
        return JSONObject.of("pointJson", ff(JSONObject.of("x", offset, "y", 5).toString(), secretKey),
            "captchaVerification", ff("%s---%s".formatted(token,
                    JSONObject.of("x", offset,
                        "y", 5).toString()
                ),
                secretKey),
            "offset", offset);
    }

    /**
     * bitblt
     *
     * @param src    src
     * @param dest   dest
     * @param offset offset
     */
    private void bitblt(byte[] src, byte[] dest, int offset) {
        for (var h = 0; h < height; h++) {
            for (var w = 0; w < jigsawWidth; w++) {
                var srcPosition = w + h * jigsawWidth;
                var destPosition = w + h * width + offset;
                var alphaIndex = srcPosition * 4 + 3;
                if ((src[alphaIndex] & 0xFF) != 0) {
                    dest[destPosition * 4] = src[srcPosition * 4];
                    dest[destPosition * 4 + 1] = src[srcPosition * 4 + 1];
                    dest[destPosition * 4 + 2] = src[srcPosition * 4 + 2];
                    dest[destPosition * 4 + 3] = src[alphaIndex];
                }
            }
        }
    }

    /**
     * imageFilter
     *
     * @param data data
     * @return byte[]
     */
    private byte[] imageFilter(byte[] data) {
        for (var i = 0; i < data.length; i += 4) {
            if ((data[i] & 0xFF) > maxR && (data[i + 1] & 0xFF) > maxG && (data[i + 2] & 0xFF) > maxB) {
                data[i] = (byte) 255;
                data[i + 1] = (byte) 255;
                data[i + 2] = (byte) 255;
            } else {
                data[i] = 0;
                data[i + 1] = 0;
                data[i + 2] = 0;
            }
        }
        return data;
    }

    /*
    private byte[] getRGBAData(BufferedImage image) {
        var width = image.getWidth();
        var height = image.getHeight();
        var rgbaData = new byte[width * height * 4];
        var index = 0;
        for (var y = 0; y < height; y++) {
            for (var x = 0; x < width; x++) {
                var rgb = image.getRGB(x, y);
                // 获取 RGBA 分量
                var red = (byte) ((rgb >> 16) & 0xFF);
                var green = (byte) ((rgb >> 8) & 0xFF);
                var blue = (byte) (rgb & 0xFF);
                var alpha = (byte) ((rgb >> 24) & 0xFF);
                // 存储到数组中
                rgbaData[index++] = red;
                rgbaData[index++] = green;
                rgbaData[index++] = blue;
                rgbaData[index++] = alpha;
            }
        }
        return rgbaData;
    }
    */

    /**
     * getRGBAData
     *
     * @param pngReader pngReader
     * @return byte[]
     */
    private byte[] getRGBAData(PngReader pngReader) {
        var width = pngReader.imgInfo.cols;
        var height = pngReader.imgInfo.rows;
        var rgbaData = new byte[width * height * 4];
        var index = 0;
        for (var y = 0; y < height; y++) {
            var iImageLine = pngReader.readRow(y);
            for (var x = 0; x < width; x++) {
                if (pngReader.imgInfo.alpha) {
                    var rgb = ImageLineHelper.getPixelARGB8(iImageLine, x);
                    // 获取 RGBA 分量
                    var red = (byte) ((rgb >> 16) & 0xFF);
                    var green = (byte) ((rgb >> 8) & 0xFF);
                    var blue = (byte) (rgb & 0xFF);
                    var alpha = (byte) ((rgb >> 24) & 0xFF);
                    // 存储到数组中
                    rgbaData[index++] = red;
                    rgbaData[index++] = green;
                    rgbaData[index++] = blue;
                    rgbaData[index++] = alpha;
                } else {
                    var rgb = ImageLineHelper.getPixelRGB8(iImageLine, x);
                    // 获取 RGBA 分量
                    var red = (byte) ((rgb >> 16) & 0xFF);
                    var green = (byte) ((rgb >> 8) & 0xFF);
                    var blue = (byte) (rgb & 0xFF);
                    var alpha = (byte) (0xFF);
                    // 存储到数组中
                    rgbaData[index++] = red;
                    rgbaData[index++] = green;
                    rgbaData[index++] = blue;
                    rgbaData[index++] = alpha;
                }
            }
        }
        return rgbaData;
    }

    /**
     * f
     *
     * @param content content
     * @param key     key
     * @return String
     */
    private String ff(String content, String key) {
        key = StringUtils.isNotBlank(key) ? key : "XwKsGlMcdPMEhR1B";
        /*if (NativeUtil.isNative()) {
            return JsUtils.execute(JsUtils.JsFile.CRYPTO_JS, new JsUtils.CryptoJsParams(
                JsUtils.CryptoJsParams.Func.F, JSONObject.of("content", content, "key", key)));
        } else {
        }*/
        var aes = new SymmetricCrypto(SymmetricAlgorithm.AES, key.getBytes(StandardCharsets.UTF_8));
        return aes.encryptBase64(content.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * saveImage
     *
     * @param rgbaData rgbaData
     * @param width    width
     * @param height   height
     * @param file     file
     */
    private void saveImage(byte[] rgbaData, int width, int height, String file) {
        /*
        try {
            var image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
            for (var y = 0; y < height; y++) {
                for (var x = 0; x < width; x++) {
                    var pos = (y * width + x) * 4;
                    var rgba = ((rgbaData[pos + 3] & 0xFF) << 24)
                               | ((rgbaData[pos] & 0xFF) << 16)
                               | ((rgbaData[pos + 1] & 0xFF) << 8)
                               | (rgbaData[pos + 2] & 0xFF);
                    image.setRGB(x, y, rgba);
                }
            }
            var output = new File("R:\\Files\\Temp\\blockPuzzle\\" + file + ".png");
            ImageIO.write(image, "png", output);
        } catch (IOException e) {
            log.error("save image err", e);
        }
        */
    }
}
