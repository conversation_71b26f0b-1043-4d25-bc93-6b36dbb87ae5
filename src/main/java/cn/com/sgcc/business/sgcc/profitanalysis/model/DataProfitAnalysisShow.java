package cn.com.sgcc.business.sgcc.profitanalysis.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.Data;

/**
 * DataProfitAnalysisShow
 */
@Data
public class DataProfitAnalysisShow implements Serializable {
    private String unitId;
    private String date;
    private String time;
    private BigDecimal ipDayAheadPrice;
    private BigDecimal ipRealTimePrice;
    private BigDecimal opbdReportPower;
    private BigDecimal opbdBidPower;
    private BigDecimal opbdBidPercent;
    private BigDecimal opbdFee;
    private BigDecimal opbdDayAheadBidPrice;
    private BigDecimal opbdBidProfit;
    private BigDecimal opidReportPower;
    private BigDecimal opidBidPower;
    private BigDecimal opidBidPercent;
    private BigDecimal opidRealTimeBidPrice;
    private BigDecimal opidBidProfit;
    private BigDecimal opidFee;
    private BigDecimal totalBidProfit;
    private String createTime;

    /**
     * calc
     */
    public void calc() {
        if (opbdReportPower != null && opbdBidPower != null) {
            if (opbdReportPower.doubleValue() == 0D) {
                opbdBidPercent = BigDecimal.ZERO;
            } else {
                opbdBidPercent = opbdBidPower.divide(opbdReportPower, 4, RoundingMode.HALF_UP);
            }
        }
        if (opbdDayAheadBidPrice != null && ipDayAheadPrice != null && opbdBidPower != null) {
            opbdBidProfit = opbdDayAheadBidPrice.subtract(ipDayAheadPrice)
                                                .multiply(opbdBidPower)
                                                .divide(new BigDecimal(4), 2, RoundingMode.HALF_UP);
        }
        if (opidReportPower != null && opidBidPower != null) {
            if (opidReportPower.doubleValue() == 0D) {
                opidBidPercent = BigDecimal.ZERO;
            } else {
                opidBidPercent = opidBidPower.divide(opidReportPower, 4, RoundingMode.HALF_UP);
            }
        }
        if (opidRealTimeBidPrice != null && ipRealTimePrice != null && opidBidPower != null) {
            opidBidProfit = opidRealTimeBidPrice.subtract(ipRealTimePrice)
                                                .multiply(opidBidPower)
                                                .divide(new BigDecimal(4), 2, RoundingMode.HALF_UP);
        }
        if (opbdBidProfit != null && opidBidProfit != null) {
            totalBidProfit = opidBidProfit.add(opbdBidProfit);
        } else if (opbdBidProfit != null) {
            totalBidProfit = opbdBidProfit;
        } else if (opidBidProfit != null) {
            totalBidProfit = opidBidProfit;
        }
    }
}
