package cn.com.sgcc.business.sgcc.opid.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "system_opid_time_config")
public class SystemOpidTimeConfig implements Serializable {
    @Id
    @Column
    private Long tradeTimePart;
    @Column
    private String orderNumber;
    @Column
    private String startPart;
    @Column
    private String endPart;
    @Column
    private String bidStartTime;
    @Column
    private String bidEndTime;
}
