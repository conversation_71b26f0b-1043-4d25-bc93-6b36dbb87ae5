package cn.com.sgcc.business.sgcc.opbd.controller;

import static cn.com.sgcc.business.common.model.UserConfigEnum.OPBD_RANGE_END;
import static cn.com.sgcc.business.common.model.UserConfigEnum.OPBD_RANGE_END_TIME;
import static cn.com.sgcc.constants.DateConsts.DATE_FORMAT;
import static cn.com.sgcc.constants.DateConsts.TIME_FORMAT;

import cn.com.sgcc.business.common.model.dto.Page;
import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.common.service.UserConfigService;
import cn.com.sgcc.business.sgcc.opbd.model.UserOpbdConfigDetail;
import cn.com.sgcc.business.sgcc.opbd.model.dto.UserOpbdConfigDto;
import cn.com.sgcc.business.sgcc.opbd.service.OpbdService;
import cn.com.sgcc.business.sgcc.opid.model.excel.StrategyConfigExport;
import cn.com.sgcc.business.sgcc.opid.model.excel.StrategyConfigImport;
import cn.com.sgcc.config.ErrMsg;
import cn.com.sgcc.config.role.HasRole;
import cn.com.sgcc.config.role.RoleCondition;
import cn.com.sgcc.constants.RoleConstants;
import cn.com.sgcc.util.TimeUtil;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * OpbdController
 */
@Conditional({RoleCondition.class})
@HasRole(RoleConstants.Type.OPBD)
@RestController
@RequestMapping("/api/v1/opbd")
@CrossOrigin
@Slf4j
public class OpbdController {
    @Resource
    private OpbdService opbdService;
    @Resource
    private UserConfigService configService;

    /**
     * queryConfigList
     *
     * @param param param
     * @return Result<List < UserOpbdConfigDetail>>
     */
    @GetMapping("/configList")
    @ErrMsg("获取策略列表失败")
    public Result<List<UserOpbdConfigDetail>> queryConfigList(@RequestParam String param) {
        JSONObject params = JSONObject.parse(param);
        String unitId = params.getString("unitId");
        String startDate = params.getString("startDate");
        return Result.ok(opbdService.queryConfigList(unitId, startDate));
    }

    /**
     * queryRecordList
     *
     * @param param param
     * @return Result<Page < List < UserOpbdConfigDto>>>
     */
    @GetMapping("/recordList")
    @ErrMsg("获取记录失败")
    public Result<Page<List<UserOpbdConfigDto>>> queryRecordList(@RequestParam String param) {
        JSONObject params = JSONObject.parse(param);
        String unitId = params.getString("unitId");
        JSONObject pageParam = params.getJSONObject("page");
        Integer page = pageParam.getInteger("page");
        Integer pageSize = pageParam.getInteger("pageSize");
        return Result.ok(opbdService.queryRecordList(unitId, new Page<>(page, pageSize)));
    }

    /**
     * deleteRecord
     *
     * @param params params
     * @return Result<Page < List < UserOpbdConfigDto>>>
     */
    @DeleteMapping("/recordList")
    @ErrMsg("删除记录失败")
    public Result<Integer> deleteRecord(@RequestBody JSONObject params) {
        String unitId = params.getString("unitId");
        String strategyDate = params.getString("strategyDate");
        opbdService.deleteRecordList(unitId, strategyDate);
        return Result.ok();
    }

    /**
     * saveConfigList
     *
     * @param params params
     * @return Result<Boolean>
     */
    @PutMapping("/configList")
    @ErrMsg("新增策略失败")
    public Result<Boolean> saveConfigList(@RequestBody JSONObject params) {
        List<String> unitIdList = params.getList("unitIdList", String.class);
        String startDate = params.getString("startDate");
        String endDate = params.getString("endDate");
        Integer type = params.getInteger("type");
        List<Integer> timePartList = params.getList("timeList", Integer.class);
        List<UserOpbdConfigDetail> configList = params.getList("configList", UserOpbdConfigDetail.class);
        if (configList.isEmpty()) {
            return Result.fail("数据为空");
        }
        return Result.ok(opbdService.saveConfigList(unitIdList, type, startDate, endDate, timePartList, configList));
    }

    /**
     * exportRecordList
     *
     * @param params params
     * @return Result<Map < String, Collection < StrategyConfigExport>>>
     */
    @PostMapping("/exportRecordList")
    @ErrMsg("导出失败")
    public Result<Map<String, Collection<StrategyConfigExport>>> exportRecordList(@RequestBody JSONObject params) {
        List<String> unitIdList = params.getList("unitIdList", String.class);
        String startDate = params.getString("startDate");
        String endDate = params.getString("endDate");
        return Result.ok(opbdService.exportRecordData(unitIdList, startDate, endDate));
    }

    /**
     * exportConfigTemplate
     *
     * @param params params
     * @return Result<List < StrategyConfigImport>>
     */
    @PostMapping("/exportConfigTemplate")
    @ErrMsg("获取模板失败")
    public Result<List<StrategyConfigImport>> exportConfigTemplate(@RequestBody JSONObject params) {
        String startDate = params.getString("startDate");
        String endDate = params.getString("endDate");
        List<StrategyConfigImport> list = new ArrayList<>();
        LocalDateTime startDay = LocalDate.parse(startDate, DATE_FORMAT).atTime(0, 0);
        LocalDateTime endDay = LocalDate.parse(endDate, DATE_FORMAT).atTime(0, 0).plusDays(1);
        List<String> allTime = TimeUtil.customTimeAxis(startDay, endDay, 60 * 15, "yyyy-MM-dd HH:mm", true, true);
        for (String time : allTime) {
            StrategyConfigImport configImport = new StrategyConfigImport();
            configImport.setStrategyDate(time.substring(0, 10));
            configImport.setStartTime(time.substring(11));
            configImport.setPercent(1D);
            configImport.setPrice(0D);
            list.add(configImport);
        }
        return Result.ok(list);
    }

    /**
     * importExcel
     *
     * @param params params
     * @return Result<Boolean>
     */
    @SneakyThrows
    @PostMapping("/importConfigList")
    @ErrMsg("导入策略失败")
    public Result<Boolean> importExcel(@RequestBody JSONObject params) {
        var unitIdList = params.getList("unitIdList", String.class);
        var dataList = params.getJSONArray("sheetJson");
        var type = 4;
        if (dataList.isEmpty()) {
            return Result.fail("数据为空");
        }
        var importArrayList = dataList.toJavaList(StrategyConfigImport.class);
        return Result.ok(opbdService.importRecordData(unitIdList, type, importArrayList));
    }

    /**
     * queryRecordList
     *
     * @return Result<Page < List < UserOpbdConfigDto>>>
     */
    @GetMapping("/rangeEnd")
    @ErrMsg("获取记录失败")
    public Result<JSONObject> queryRangeEnd() {
        var rangeEnd = configService.get(OPBD_RANGE_END);
        var rangeEndTime = configService.get(OPBD_RANGE_END_TIME);
        if (StringUtils.isNotBlank(rangeEndTime)) {
            var time = LocalTime.parse(rangeEndTime, TIME_FORMAT);
            rangeEndTime = LocalDate.now().atTime(time).plusMinutes(-Integer.parseInt(rangeEnd)).format(TIME_FORMAT);
        }
        return Result.ok(JSONObject.of("rangeEnd", rangeEnd, "rangeEndTime", rangeEndTime));
    }

    /**
     * saveRangeEnd
     *
     * @param params params
     * @return Result<String>
     */
    @PutMapping("/rangeEnd")
    @ErrMsg("保存记录失败")
    public Result<String> saveRangeEnd(@RequestBody JSONObject params) {
        configService.set(OPBD_RANGE_END, params.getInteger("value").toString());
        return Result.ok();
    }
}
