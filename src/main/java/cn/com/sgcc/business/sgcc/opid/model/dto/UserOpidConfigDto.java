package cn.com.sgcc.business.sgcc.opid.model.dto;

import cn.com.sgcc.business.sgcc.opid.model.UserOpidConfig;
import cn.com.sgcc.business.sgcc.opid.model.UserOpidConfigDetail;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * UserOpidConfigDto
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class UserOpidConfigDto extends UserOpidConfig implements Serializable {
    private List<UserOpidConfigDetail> details;
    private Boolean disabled;

    /**
     * UserOpidConfigDto Constructor
     *
     * @param config config
     */
    public UserOpidConfigDto(UserOpidConfig config) {
        BeanUtils.copyProperties(config, this);
    }
}
