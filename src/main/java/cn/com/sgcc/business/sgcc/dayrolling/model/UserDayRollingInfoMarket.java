package cn.com.sgcc.business.sgcc.dayrolling.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "user_day_rolling_info_market")
@IdClass(UserDayRollingInfoMarket.PrimaryKey.class)
public class UserDayRollingInfoMarket implements Serializable {

    @Id
    @Column
    String strategyDate;
    @Id
    @Column
    String tradeseqId;
    @Id
    @Column
    Integer timeCode;
    @Column
    String tradeseqName;
    @Column
    String total;
    @Column
    String maxPrice;
    @Column
    String minPrice;
    @Column
    String weightedPrice;
    @Column
    String midPrice;
    @Column
    String createTime;

    @Data
    static class PrimaryKey implements Serializable {
        String strategyDate;
        String tradeseqId;
        Integer timeCode;
    }
}
