package cn.com.sgcc.business.sgcc.profitanalysis.controller;

import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.sgcc.profitanalysis.model.DataProfitAnalysis;
import cn.com.sgcc.business.sgcc.profitanalysis.service.ProfitAnalysisService;
import cn.com.sgcc.config.ErrMsg;
import cn.com.sgcc.config.role.HasRole;
import cn.com.sgcc.config.role.RoleCondition;
import cn.com.sgcc.constants.DateConsts;
import cn.com.sgcc.constants.RoleConstants;
import cn.com.sgcc.job.service.ProfitAnalysisJobService;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * ProfitAnalysisController
 */
@Conditional({RoleCondition.class})
@HasRole(RoleConstants.Type.PROFIT_ANALYSIS)
@RestController
@RequestMapping("/api/v1/profitAnalysis")
@CrossOrigin
@Slf4j
public class ProfitAnalysisController {

    @Resource
    ProfitAnalysisService service;
    @Resource
    ProfitAnalysisJobService jobService;

    /**
     * queryList
     *
     * @param unitId    unitId
     * @param startDate startDate
     * @param endDate   endDate
     * @return Result<Map < String, Object>>
     */
    @GetMapping("/list")
    @ErrMsg("获取收益分析列表失败")
    public Result<Map<String, Object>> queryList(String unitId, String startDate, String endDate) {
        return Result.ok(service.queryList(unitId, startDate, endDate));
    }

    /**
     * update
     *
     * @param analysis analysis
     * @return Result<Boolean>
     */
    @PostMapping("/update")
    @ErrMsg("更新收益分析列表失败")
    public Result<Boolean> update(@RequestBody DataProfitAnalysis analysis) {
        LocalDate.parse(analysis.getDate(), DateConsts.DATE_FORMAT);
        jobService.crawler(analysis.getUnitId(), analysis.getDate(), false);
        return Result.ok();
    }
}
