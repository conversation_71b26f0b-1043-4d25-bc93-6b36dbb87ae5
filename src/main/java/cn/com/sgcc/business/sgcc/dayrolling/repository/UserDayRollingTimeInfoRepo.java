package cn.com.sgcc.business.sgcc.dayrolling.repository;

import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingTimeInfo;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * UserDayRollingTimeInfoRepo
 */
@Repository
public interface UserDayRollingTimeInfoRepo extends JpaRepository<UserDayRollingTimeInfo, Void> {
    /**
     * findAllByStrategyDate
     *
     * @param strategyDate strategyDate
     * @return List<UserDayRollingTimeInfo>
     */
    List<UserDayRollingTimeInfo> findAllByStrategyDate(String strategyDate);
}
