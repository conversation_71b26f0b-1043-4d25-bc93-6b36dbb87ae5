package cn.com.sgcc.business.sgcc.profitanalysis.service.impl;

import static cn.com.sgcc.constants.DateConsts.DATE_FORMAT;
import static cn.com.sgcc.util.TimeUtil.FIFTEEN_MINUTE_MAP;

import cn.com.sgcc.business.sgcc.profitanalysis.model.DataProfitAnalysis;
import cn.com.sgcc.business.sgcc.profitanalysis.model.DataProfitAnalysisShow;
import cn.com.sgcc.business.sgcc.profitanalysis.repository.DataProfitAnalysisRepo;
import cn.com.sgcc.business.sgcc.profitanalysis.service.ProfitAnalysisService;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.constants.RoleConstants;
import cn.com.sgcc.util.DecimalUtil;
import cn.com.sgcc.util.TimeUtil;
import com.alibaba.fastjson2.JSONArray;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ProfitAnalysisServiceImpl implements ProfitAnalysisService {

    @Resource
    DataProfitAnalysisRepo repo;
    @Resource
    UnitService unitService;

    @Override
    public Map<String, Object> queryList(String unitId, String startDate, String endDate) {
        LocalDateTime startDay = LocalDate.parse(startDate, DATE_FORMAT).atTime(0, 0);
        LocalDateTime endDay = LocalDate.parse(endDate, DATE_FORMAT).atTime(0, 0).plusDays(1);
        var dateTimeStrList = TimeUtil.customTimeAxis(startDay, endDay, 60 * 15, "yyyy-MM-dd HH:mm", true, true);
        var unitMap = unitService.getUnitMap(RoleConstants.Type.PROFIT_ANALYSIS);

        Map<String, List<DataProfitAnalysis>> allByParamsByUnitId;
        if (unitId == null) {
            var allByParams = repo.findAllByParams(startDate, endDate);
            allByParamsByUnitId = allByParams.stream().collect(Collectors.groupingBy(DataProfitAnalysis::getUnitId));
        } else {
            var allByParams = repo.findAllByParams(unitId, startDate, endDate);
            allByParamsByUnitId = new HashMap<>();
            allByParamsByUnitId.put(unitId, allByParams);
        }
        var stringObjectHashMap = new HashMap<String, Object>();
        for (String uId : unitMap.keySet()) {
            if (unitId != null && !unitId.equals(uId)) {
                continue;
            }
            var pointListMap = new LinkedHashMap<String, DataProfitAnalysisShow>();
            for (String dateTimeStr : dateTimeStrList) {
                var analysisShow = new DataProfitAnalysisShow();
                analysisShow.setDate(dateTimeStr.substring(0, 10));
                analysisShow.setTime(dateTimeStr.substring(11));
                pointListMap.put(dateTimeStr, analysisShow);
            }
            var allByParams = allByParamsByUnitId.get(uId);
            if (allByParams == null) {
                allByParams = new ArrayList<>();
            }
            for (DataProfitAnalysis analysis : allByParams) {
                var ipDayAheadPriceArr = JSONArray.parse(analysis.getIpDayAheadPrice());
                var ipRealTimePriceArr = JSONArray.parse(analysis.getIpRealTimePrice());
                var opbdReportPowerArr = JSONArray.parse(analysis.getOpbdReportPower());
                var opbdBidPowerArr = JSONArray.parse(analysis.getOpbdBidPower());
                var opbdDayAheadBidPriceArr = JSONArray.parse(analysis.getOpbdDayAheadBidPrice());
                var opidReportPowerArr = JSONArray.parse(analysis.getOpidReportPower());
                var opidBidPowerArr = JSONArray.parse(analysis.getOpidBidPower());
                var opidRealTimeBidPriceArr = JSONArray.parse(analysis.getOpidRealTimeBidPrice());
                var dateStr = analysis.getDate();
                for (int i = 0; i < 96; i++) {
                    var timeStr = FIFTEEN_MINUTE_MAP.get(i);
                    var analysisShow = pointListMap.get(dateStr + " " + timeStr);
                    analysisShow.setUnitId(analysis.getUnitId());
                    analysisShow.setDate(dateStr);
                    analysisShow.setTime(timeStr);
                    analysisShow.setCreateTime(analysis.getCreateTime());
                    analysisShow.setIpDayAheadPrice(ipDayAheadPriceArr.getBigDecimal(i));
                    analysisShow.setIpRealTimePrice(ipRealTimePriceArr.getBigDecimal(i));
                    analysisShow.setOpbdReportPower(opbdReportPowerArr.getBigDecimal(i));
                    analysisShow.setOpbdBidPower(opbdBidPowerArr.getBigDecimal(i));
                    analysisShow.setOpbdDayAheadBidPrice(opbdDayAheadBidPriceArr.getBigDecimal(i));
                    analysisShow.setOpidReportPower(opidReportPowerArr.getBigDecimal(i));
                    analysisShow.setOpidBidPower(opidBidPowerArr.getBigDecimal(i));
                    analysisShow.setOpidRealTimeBidPrice(opidRealTimeBidPriceArr.getBigDecimal(i));
                    analysisShow.calc();
                }
            }
            var dayListMap = calcDay(startDay, endDay, pointListMap.values());
            var analysisShow = calcPointTotal(pointListMap.values());
            pointListMap.putFirst("total", analysisShow);
            dayListMap.putFirst("total", analysisShow);
            stringObjectHashMap.put(unitMap.get(uId).getUName(), Map.of("pointList", pointListMap.values(),
                                                                        "dayList", dayListMap.values()));
        }
        return stringObjectHashMap;
    }

    /**
     * calcDay
     *
     * @param startDay startDay
     * @param endDay   endDay
     * @param values   values
     * @return LinkedHashMap<String, DataProfitAnalysisShow>
     */
    private LinkedHashMap<String, DataProfitAnalysisShow> calcDay(LocalDateTime startDay,
                                                                  LocalDateTime endDay,
                                                                  Collection<DataProfitAnalysisShow> values) {
        var dayListMap = new LinkedHashMap<String, DataProfitAnalysisShow>();
        var dateStrList = TimeUtil.customTimeAxis(startDay, endDay, 60 * 60 * 24, "yyyy-MM-dd", false, false);
        for (String dateStr : dateStrList) {
            var analysisShow = calcPointTotal(values.stream()
                                                    .filter(point -> point.getDate().equals(dateStr)).toList());
            analysisShow.setDate(dateStr);
            dayListMap.put(dateStr, analysisShow);
        }
        return dayListMap;
    }

    /**
     * calcPointTotal
     *
     * @param values values
     * @return DataProfitAnalysisShow
     */
    public static DataProfitAnalysisShow calcPointTotal(Collection<DataProfitAnalysisShow> values) {
        var pointTotal = new DataProfitAnalysisShow();
        pointTotal.setDate("合计");
        pointTotal.setIpDayAheadPrice(DecimalUtil.avg(values.stream()
                                                            .map(DataProfitAnalysisShow::getIpDayAheadPrice)
                                                            .toArray()));
        pointTotal.setIpRealTimePrice(DecimalUtil.avg(values.stream()
                                                            .map(DataProfitAnalysisShow::getIpRealTimePrice)
                                                            .toArray()));
        pointTotal.setOpbdReportPower(DecimalUtil.add(values.stream()
                                                            .map(DataProfitAnalysisShow::getOpbdReportPower)
                                                            .toArray()));
        pointTotal.setOpbdBidPower(DecimalUtil.add(values.stream()
                                                         .map(DataProfitAnalysisShow::getOpbdBidPower)
                                                         .toArray()));
        pointTotal.setOpbdDayAheadBidPrice(DecimalUtil.divide(DecimalUtil.add(
                values.stream()
                      .map(point -> DecimalUtil.multiply(point.getOpbdBidPower(), point.getOpbdDayAheadBidPrice()))
                      .toArray()), pointTotal.getOpbdBidPower()));
        pointTotal.setOpidReportPower(DecimalUtil.add(values.stream()
                                                            .map(DataProfitAnalysisShow::getOpidReportPower)
                                                            .toArray()));
        pointTotal.setOpidBidPower(DecimalUtil.add(values.stream()
                                                         .map(DataProfitAnalysisShow::getOpidBidPower)
                                                         .toArray()));
        pointTotal.setOpidRealTimeBidPrice(DecimalUtil.divide(DecimalUtil.add(
                values.stream()
                      .map(point -> DecimalUtil.multiply(point.getOpidBidPower(), point.getOpidRealTimeBidPrice()))
                      .toArray()), pointTotal.getOpidBidPower()));
        pointTotal.calc();
        pointTotal.setOpbdBidProfit(DecimalUtil.add(values.stream()
                                                          .map(DataProfitAnalysisShow::getOpbdBidProfit)
                                                          .toArray()));
        pointTotal.setOpidBidProfit(DecimalUtil.add(values.stream()
                                                          .map(DataProfitAnalysisShow::getOpidBidProfit)
                                                          .toArray()));
        pointTotal.setTotalBidProfit(DecimalUtil.add(values.stream()
                                                           .map(DataProfitAnalysisShow::getTotalBidProfit)
                                                           .toArray()));
        return pointTotal;
    }
}
