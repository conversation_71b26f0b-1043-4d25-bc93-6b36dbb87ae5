package cn.com.sgcc.business.sgcc.opid.repository;

import cn.com.sgcc.business.sgcc.opid.model.UserOpidConfigDetail;
import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * UserOpidConfigDetailRepo
 */
@Repository
public interface UserOpidConfigDetailRepo extends JpaRepository<UserOpidConfigDetail, Void> {
    /**
     * findAllByParams
     *
     * @param unitIds   unitIds
     * @param startDate startDate
     * @param endDate   endDate
     * @return List<UserOpidConfigDetail>
     */
    @Query(value = """
            select *
            from user_opid_config_detail
            where unitId in :unitIds
              and strategyDate >= :startDate
              and strategyDate <= :endDate
            order by strategyDate, createTime""", nativeQuery = true)
    List<UserOpidConfigDetail> findAllByParams(Collection<String> unitIds, String startDate, String endDate);

    /**
     * findAllByUnitIdAndStrategyDateGreaterThanEqual
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @return List<UserOpidConfigDetail>
     */
    List<UserOpidConfigDetail> findAllByUnitIdAndStrategyDateGreaterThanEqual(String unitId, String strategyDate);

    /**
     * findAllByUnitIdAndStrategyDateIn
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @return List<UserOpidConfigDetail>
     */
    List<UserOpidConfigDetail> findAllByUnitIdAndStrategyDateIn(String unitId, Collection<String> strategyDate);

    /**
     * findAllNotSuccess
     *
     * @param strategyDate strategyDate
     * @param startTime    startTime
     * @param endTime      endTime
     * @return List<UserOpidConfigDetail>
     */
    @Query(value = """
            select *
            from user_opid_config_detail
            where strategyDate = :strategyDate
              and startTime >= :startTime
              and endTime <= :endTime
              and status != 1""", nativeQuery = true)
    List<UserOpidConfigDetail> findAllNotSuccess(String strategyDate, String startTime, String endTime);

    /**
     * removeByParams
     *
     * @param unitIds   unitIds
     * @param startDate startDate
     * @param endDate   endDate
     * @param timeParts timeParts
     */
    @Modifying
    @Transactional
    @Query(value = """
            delete
            from user_opid_config_detail
            where unitId in :unitIds
              and strategyDate >= :startDate
              and strategyDate <= :endDate
              and timePart in :timeParts
            """, nativeQuery = true)
    void removeByParams(Collection<String> unitIds, String startDate, String endDate, Collection<Integer> timeParts);

    /**
     * removeByUnitIdAndStrategyDate
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     */
    void removeByUnitIdAndStrategyDate(String unitId, String strategyDate);

    /**
     * updatePowerLimit
     *
     * @param detail detail
     */
    @Modifying
    @Transactional
    @Query(value = """
            update user_opid_config_detail
               set powerLimit = :#{#detail.powerLimit}
            where unitId = :#{#detail.unitId}
              and strategyDate = :#{#detail.strategyDate}
              and startTime = :#{#detail.startTime}
              and endTime = :#{#detail.endTime}""", nativeQuery = true)
    void updatePowerLimit(UserOpidConfigDetail detail);

    /**
     * findAllSucceed
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @return List<UserOpidConfigDetail>
     */
    @Query(value = """
            select *
            from user_opid_config_detail
            where unitId = :unitId
              and strategyDate = :strategyDate
              and status = 1""", nativeQuery = true)
    List<UserOpidConfigDetail> findAllSucceed(String unitId, String strategyDate);
}
