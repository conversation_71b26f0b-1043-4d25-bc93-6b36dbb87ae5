package cn.com.sgcc.business.sgcc.autologin.service;

import cn.com.sgcc.business.sgcc.autologin.model.dto.Cookie;
import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.system.model.SystemAccountConfig;
import com.alibaba.fastjson2.JSONObject;
import java.util.List;
import java.util.Optional;
import lombok.NonNull;

public interface LoginService {
    /**
     * login
     *
     * @param unitId unitId
     * @param force  force
     * @return LoginInfo
     */
    LoginInfo login(String unitId, boolean force);

    /**
     * login
     *
     * @param accountConfig accountConfig
     * @param force         force
     * @return LoginInfo
     */
    LoginInfo login(@NonNull SystemAccountConfig accountConfig, boolean force);

    /**
     * loginByPlantId
     *
     * @param plantId plantId
     * @param force   force
     * @return LoginInfo
     */
    LoginInfo loginByPlantId(String plantId, boolean force);

    /**
     * loginByUserName
     *
     * @param userName userName
     * @param force    force
     * @return LoginInfo
     */
    LoginInfo loginByUserName(String userName, boolean force);

    /**
     * addLogin
     *
     * @param loginInfo loginInfo
     */
    void addLogin(JSONObject loginInfo);

    /**
     * getCookies
     *
     * @param userName userName
     * @return List<Cookie>
     */
    List<Cookie> getCookies(String userName);

    /**
     * getLoginData
     *
     * @param accountConfig accountConfig
     * @param friendly      friendly
     * @return LoginInfo
     */
    LoginInfo getLoginData(@NonNull SystemAccountConfig accountConfig, boolean friendly);

    /**
     * getOne
     *
     * @return Optional<LoginInfo>
     */
    Optional<LoginInfo> getOne();
}
