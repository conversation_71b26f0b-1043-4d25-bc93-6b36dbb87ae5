package cn.com.sgcc.business.sgcc.opbd.repository;

import cn.com.sgcc.business.sgcc.opbd.model.UserOpbdConfig;
import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * UserOpbdConfigRepo
 */
@Repository
public interface UserOpbdConfigRepo extends JpaRepository<UserOpbdConfig, Void> {
    /**
     * findAllByUnitIdPage
     *
     * @param unitId unitId
     * @param limit  limit
     * @param offset offset
     * @return List<UserOpbdConfig>
     */
    @Query(value = """
            select *
            from user_opbd_config
            where unitId = :unitId
            order by strategyDate desc
            limit :limit offset :offset""", nativeQuery = true)
    List<UserOpbdConfig> findAllByUnitIdPage(String unitId, Integer limit, Integer offset);

    /**
     * countAllByUnitId
     *
     * @param unitId unitId
     * @return long
     */
    long countAllByUnitId(String unitId);

    /**
     * removeByParams
     *
     * @param unitIds   unitIds
     * @param startDate startDate
     * @param endDate   endDate
     */
    @Modifying
    @Transactional
    @Query(value = """
            delete from user_opbd_config
            where unitId in :unitIds
              and strategyDate >= :startDate
              and strategyDate <= :endDate""", nativeQuery = true)
    void removeByParams(Collection<String> unitIds, String startDate, String endDate);

    /**
     * removeByUnitIdAndStrategyDate
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     */
    void removeByUnitIdAndStrategyDate(String unitId, String strategyDate);

    /**
     * findAllByStrategyDate
     *
     * @param strategyDate strategyDate
     * @return List<UserOpbdConfig>
     */
    List<UserOpbdConfig> findAllByStrategyDate(String strategyDate);
}
