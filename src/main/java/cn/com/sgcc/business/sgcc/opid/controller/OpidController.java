package cn.com.sgcc.business.sgcc.opid.controller;

import static cn.com.sgcc.business.common.model.UserConfigEnum.HLJ_OPID_TIME_CONFIG;
import static cn.com.sgcc.constants.DateConsts.DATE_FORMAT;

import cn.com.sgcc.business.common.model.dto.Page;
import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.common.service.UserConfigService;
import cn.com.sgcc.business.sgcc.opid.model.UserOpidConfigDetail;
import cn.com.sgcc.business.sgcc.opid.model.dto.UserOpidConfigDto;
import cn.com.sgcc.business.sgcc.opid.model.excel.StrategyConfigExport;
import cn.com.sgcc.business.sgcc.opid.model.excel.StrategyConfigImport;
import cn.com.sgcc.business.sgcc.opid.service.OpidService;
import cn.com.sgcc.config.ErrMsg;
import cn.com.sgcc.config.role.HasRole;
import cn.com.sgcc.config.role.RoleCondition;
import cn.com.sgcc.constants.RoleConstants;
import cn.com.sgcc.util.TimeUtil;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 策略配置
 */
@Conditional({RoleCondition.class})
@HasRole(RoleConstants.Type.OPID)
@RestController
@RequestMapping("/api/v1/opid")
@CrossOrigin
@Slf4j
public class OpidController {
    @Resource
    private OpidService opidService;
    @Resource
    private UserConfigService configService;

    /**
     * 使用GET请求获取策略列表
     *
     * @param param 请求参数，包含unitId和startDate
     * @return 策略列表，封装在Result对象中
     * <p>
     * 说明：
     * 1. 该方法用于查询并返回策略列表，通过解析输入参数获取单位ID和开始日期
     * 2. 调用opidService的queryConfigList方法进行实际查询
     * 3. 返回一个成功的Result对象，其中包含查询到的策略列表
     */
    @GetMapping("/configList")
    @ErrMsg("获取策略列表失败")
    public Result<List<UserOpidConfigDetail>> queryConfigList(@RequestParam String param) {
        // 解析输入的请求参数
        JSONObject params = JSONObject.parse(param);
        // 从解析的参数中获取单位ID
        String unitId = params.getString("unitId");
        // 从解析的参数中获取开始日期
        String startDate = params.getString("startDate");
        // 调用服务层方法查询策略列表，并返回查询结果
        return Result.ok(opidService.queryConfigList(unitId, startDate));
    }

    /**
     * 使用GET方法从指定URL获取记录列表
     * 该方法首先解析请求参数中的JSON字符串，然后调用opidService查询与单位ID和分页参数匹配的记录列表
     *
     * @param param 请求参数中的JSON字符串，包含单位ID和分页信息
     * @return 包含记录列表的分页结果
     * <p>
     * 使用@GetMapping注解指定处理GET请求的URL
     * 使用@ErrMsg注解指定在发生异常时返回的错误消息
     */
    @GetMapping("/recordList")
    @ErrMsg("获取记录失败")
    public Result<Page<List<UserOpidConfigDto>>> queryRecordList(@RequestParam String param) {
        // 解析请求参数中的JSON字符串
        JSONObject params = JSONObject.parse(param);
        // 获取单位ID
        String unitId = params.getString("unitId");
        // 获取分页参数
        JSONObject pageParam = params.getJSONObject("page");
        Integer page = pageParam.getInteger("page");
        Integer pageSize = pageParam.getInteger("pageSize");
        // 调用opidService查询与单位ID和分页参数匹配的记录列表，并返回成功结果
        return Result.ok(opidService.queryRecordList(unitId, new Page<>(page, pageSize)));
    }

    /**
     * deleteRecord
     *
     * @param params params
     * @return Result<Page < List < UserOpbdConfigDto>>>
     */
    @DeleteMapping("/recordList")
    @ErrMsg("删除记录失败")
    public Result<Integer> deleteRecord(@RequestBody JSONObject params) {
        String unitId = params.getString("unitId");
        String strategyDate = params.getString("strategyDate");
        opidService.deleteRecordList(unitId, strategyDate);
        return Result.ok();
    }

    /**
     * 新增策略接口
     * 该接口用于保存新的策略列表，包括单元ID列表、时间范围、类型及具体配置详情
     *
     * @param params JSON对象，包含以下参数：
     *               - unitIdList 单元ID列表
     *               - startDate 策略开始日期
     *               - endDate 策略结束日期
     *               - type 策略类型
     *               - timeList 时间段列表
     *               - configList 策略配置详情列表
     * @return 返回操作结果，包含一个布尔值表示是否保存成功
     * 如果保存成功，返回Result.ok(true)，否则返回Result.fail("数据为空")
     */
    @PutMapping("/configList")
    @ErrMsg("新增策略失败")
    public Result<Boolean> saveConfigList(@RequestBody JSONObject params) {
        // 从参数中提取单元ID列表
        List<String> unitIdList = params.getList("unitIdList", String.class);
        // 从参数中提取策略开始日期
        String startDate = params.getString("startDate");
        // 从参数中提取策略结束日期
        String endDate = params.getString("endDate");
        // 从参数中提取策略类型
        Integer strategyType = params.getInteger("strategyType");
        // 从参数中提取策略类型
        Integer reportType = params.getInteger("reportType");
        // 从参数中提取时间段列表
        List<Integer> timePartList = params.getList("timeList", Integer.class);
        // 从参数中提取策略配置详情列表
        List<UserOpidConfigDetail> configList = params.getList("configList", UserOpidConfigDetail.class);
        // 检查策略配置详情列表是否为空
        if (configList.isEmpty()) {
            // 如果为空，则返回失败结果，数据为空
            return Result.fail("数据为空");
        }
        // 调用opidService的保存策略列表方法，并将结果包装后返回
        return Result.ok(opidService.saveConfigList(
                unitIdList, strategyType, reportType, startDate, endDate, timePartList, configList));
    }

    /**
     * 导出记录列表接口
     *
     * @param params 请求参数，包括单位ID列表、开始日期和结束日期
     * @return 包含导出策略配置的Map集合
     * <p>
     * 该接口接收前端发送的POST请求，根据提供的单位ID列表和时间范围，
     * 从数据库中检索相应的记录数据并导出。使用@ErrMsg注解来处理可能的导出失败情况。
     */
    @PostMapping("/exportRecordList")
    @ErrMsg("导出失败")
    public Result<Map<String, Collection<StrategyConfigExport>>> exportRecordList(@RequestBody JSONObject params) {
        // 从请求参数中提取单位ID列表
        List<String> unitIdList = params.getList("unitIdList", String.class);
        // 提取开始日期
        String startDate = params.getString("startDate");
        // 提取结束日期
        String endDate = params.getString("endDate");
        // 调用服务层方法导出记录数据，并返回结果
        return Result.ok(opidService.exportRecordData(unitIdList, startDate, endDate));
    }

    /**
     * 导出配置模板
     *
     * @param params 请求参数，包括startDate和endDate
     * @return 返回导出的策略配置列表
     * <p>
     * 通过此接口，可以根据指定的日期范围导出配置模板列表该方法首先从请求参数中获取开始日期和结束日期，
     * 然后生成在这段日期范围内的所有时间点（以15分钟为间隔），并为每个时间点创建一个策略配置对象，
     * 设置对应的日期、开始时间和相关参数最后，将这些策略配置对象列表返回给调用者
     */
    @PostMapping("/exportConfigTemplate")
    @ErrMsg("获取模板失败")
    public Result<List<StrategyConfigImport>> exportConfigTemplate(@RequestBody JSONObject params) {
        // 获取开始日期
        String startDate = params.getString("startDate");
        // 获取结束日期
        String endDate = params.getString("endDate");
        // 初始化策略配置列表
        List<StrategyConfigImport> list = new ArrayList<>();
        // 解析开始日期并设置时间为0点
        LocalDateTime startDay = LocalDate.parse(startDate, DATE_FORMAT).atTime(0, 0);
        // 解析结束日期并设置时间为次日0点
        LocalDateTime endDay = LocalDate.parse(endDate, DATE_FORMAT).atTime(0, 0).plusDays(1);
        // 生成从开始日期到结束日期的每15分钟时间点列表
        List<String> allTime = TimeUtil.customTimeAxis(startDay, endDay, 60 * 15, "yyyy-MM-dd HH:mm", true, true);
        // 遍历所有时间点，为每个时间点创建策略配置对象
        for (String time : allTime) {
            StrategyConfigImport configImport = new StrategyConfigImport();
            // 设置策略日期
            configImport.setStrategyDate(time.substring(0, 10));
            // 设置开始时间
            configImport.setStartTime(time.substring(11));
            // 设置百分比参数
            configImport.setPercent(1D);
            // 设置价格参数
            configImport.setPrice(0D);
            // 将策略配置对象添加到列表中
            list.add(configImport);
        }
        // 返回策略配置列表
        return Result.ok(list);
    }

    /**
     * 导入配置列表接口
     * 该方法用于导入策略配置，支持从Excel文件中读取数据并导入到系统中
     * 注入SneakyThrows用于简化异常处理，避免过多的try-catch块
     * PostMapping注解指定该方法处理POST请求，请求路径为/importConfigList
     * ErrMsg注解用于当操作失败时提供统一的错误信息
     *
     * @param params 请求体中传入的参数，包括unitIdList和sheetJson两个字段
     * @return 返回一个Result对象，其中包含一个布尔值，表示导入是否成功
     * <p>
     * 重要步骤：
     * 1. 从params中提取unitIdList和sheetJson数据
     * 2. 检查sheetJson是否为空，如果为空则返回失败结果
     * 3. 将sheetJson转换为StrategyConfigImport对象列表
     * 4. 调用opidService的importRecordData方法进行数据导入
     * <p>
     * 使用@SneakyThrows简化异常处理，避免方法签名中声明throws异常
     * 使用@RequestBody将HTTP请求的body部分绑定到方法参数params上
     * 使用Result包装返回值，提供统一的API响应格式
     */
    @SneakyThrows
    @PostMapping("/importConfigList")
    @ErrMsg("导入策略失败")
    public Result<Boolean> importExcel(@RequestBody JSONObject params) {
        // 提取单位ID列表和Excel数据JSON数组
        var unitIdList = params.getList("unitIdList", String.class);
        var dataList = params.getJSONArray("sheetJson");
        var strategyType = params.getInteger("strategyType");
        var reportType = params.getInteger("reportType");
        // 如果数据列表为空，则返回失败结果
        if (dataList.isEmpty()) {
            return Result.fail("数据为空");
        }
        // 将JSON数组转换为StrategyConfigImport实体类列表
        var importArrayList = dataList.toJavaList(StrategyConfigImport.class);
        // 调用opidService进行数据导入，并返回结果
        return Result.ok(opidService.importRecordData(unitIdList, strategyType, reportType, importArrayList));
    }

    /**
     * queryTimeConfig
     *
     * @return Result<JSONObject>
     */
    @GetMapping("/timeConfig")
    @ErrMsg("获取记录失败")
    public Result<JSONObject> queryTimeConfig() {
        var timeConfig = configService.get(HLJ_OPID_TIME_CONFIG);
        return Result.ok(JSONObject.of("timeConfig", timeConfig));
    }

    /**
     * saveTimeConfig
     *
     * @param params params
     * @return Result<String>
     */
    @PutMapping("/timeConfig")
    @ErrMsg("保存记录失败")
    public Result<String> saveTimeConfig(@RequestBody JSONObject params) {
        configService.set(HLJ_OPID_TIME_CONFIG, params.getString("value"));
        return Result.ok();
    }
}
