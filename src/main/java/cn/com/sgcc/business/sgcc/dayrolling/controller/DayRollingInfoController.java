package cn.com.sgcc.business.sgcc.dayrolling.controller;

import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingInfoMarket;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingInfoUnit;
import cn.com.sgcc.business.sgcc.dayrolling.service.DayRollingInfoService;
import cn.com.sgcc.config.ErrMsg;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * DayRollingInfoController
 */
@RestController
@RequestMapping("/api/v1/dayRollingInfo")
@CrossOrigin
@Slf4j
public class DayRollingInfoController {

    @Resource
    private DayRollingInfoService dayRollingInfoService;

    /**
     * tradeList
     *
     * @param param param
     * @return Result<Map < String, String>>
     */
    @GetMapping("/tradeList")
    public Result<Map<String, String>> tradeList(@RequestParam String param) {
        JSONObject params = JSONObject.parse(param);
        String date = params.getString("date");
        return Result.ok(dayRollingInfoService.tradeList(date));
    }

    /**
     * marketList
     *
     * @param param param
     * @return Result<List < UserDayRollingInfoMarket>>
     */
    @GetMapping("/marketList")
    public Result<List<UserDayRollingInfoMarket>> marketList(@RequestParam String param) {
        JSONObject params = JSONObject.parse(param);
        String date = params.getString("date");
        String tradeseqId = params.getString("tradeseqId");
        return Result.ok(dayRollingInfoService.marketList(date, tradeseqId));
    }

    /**
     * userList
     *
     * @param param param
     * @return Result<List < UserDayRollingInfoUnit>>
     */
    @GetMapping("/unitList")
    public Result<List<UserDayRollingInfoUnit>> userList(@RequestParam String param) {
        JSONObject params = JSONObject.parse(param);
        String unitId = params.getString("unitId");
        String date = params.getString("date");
        String tradeseqId = params.getString("tradeseqId");
        return Result.ok(dayRollingInfoService.unitList(unitId, date, tradeseqId));
    }

    /**
     * importMarketList
     *
     * @param params params
     * @return Result<String>
     */
    @PostMapping("/importMarketList")
    @ErrMsg("导入失败")
    public Result<String> importMarketList(@RequestBody JSONObject params) {
        var list = params.getList("list", UserDayRollingInfoMarket.class);
        var msg = dayRollingInfoService.importMarketList(list);
        if (msg == null) {
            return Result.ok();
        } else {
            return Result.fail(msg);
        }
    }

    /**
     * importUnitList
     *
     * @param params params
     * @return Result<String>
     */
    @PostMapping("/importUnitList")
    @ErrMsg("导入失败")
    public Result<String> importUnitList(@RequestBody JSONObject params) {
        var list = params.getList("list", UserDayRollingInfoUnit.class);
        var msg = dayRollingInfoService.importUnitList(list);
        if (msg == null) {
            return Result.ok();
        } else {
            return Result.fail(msg);
        }
    }

}
