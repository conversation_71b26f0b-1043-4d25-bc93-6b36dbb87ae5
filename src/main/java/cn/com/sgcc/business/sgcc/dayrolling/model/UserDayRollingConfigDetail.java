package cn.com.sgcc.business.sgcc.dayrolling.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "user_day_rolling_config_detail")
@IdClass(UserDayRollingConfigDetail.PrimaryKey.class)
public class UserDayRollingConfigDetail implements Serializable {
    @Id
    @Column
    String unitId;
    @Id
    @Column
    String strategyDate;
    @Id
    @Column
    String declareDate;
    @Id
    @Column
    Integer timeCode;
    @Id
    @Column
    Integer type;
    @Column
    String tradeSeqId;
    @Column
    String tradeRole;
    @Column
    Double power;
    @Column
    Double price;
    @Column
    Double reportPower;
    @Column
    Double reportPrice;
    @Column
    Double buyEnergyLimit;
    @Column
    Double sellEnergyLimit;
    @Column
    Double priceLowerLimit;
    @Column
    Double priceUpperLimit;
    @Column
    Integer status;
    @Column
    String createTime;
    @Column
    String updateTime;
    @Column
    String tradeRoleLock;

    @Data
    static class PrimaryKey implements Serializable {
        String unitId;
        String strategyDate;
        String declareDate;
        Integer timeCode;
        Integer type;
    }
}
