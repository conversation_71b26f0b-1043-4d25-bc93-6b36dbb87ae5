package cn.com.sgcc.business.sgcc.dayrolling.repository;

import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingConfigDetailLog;
import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UserDayRollingConfigDetailLogRepo extends JpaRepository<UserDayRollingConfigDetailLog, Void> {
    /**
     * findAllByUnitIdAndStrategyDateInAndDeclareDateInAndTypeOrderByCreateTimeDesc
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @param declareDate  declareDate
     * @param type         type
     * @return List<UserDayRollingConfigDetailLog>
     */
    List<UserDayRollingConfigDetailLog> findAllByUnitIdAndStrategyDateInAndDeclareDateInAndTypeOrderByCreateTimeDesc(
            String unitId, Collection<String> strategyDate, Collection<String> declareDate, Integer type);
}
