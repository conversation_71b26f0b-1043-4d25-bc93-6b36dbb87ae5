package cn.com.sgcc.business.sgcc.opbd.service;

import cn.com.sgcc.business.common.model.dto.Page;
import cn.com.sgcc.business.sgcc.opbd.model.UserOpbdConfigDetail;
import cn.com.sgcc.business.sgcc.opbd.model.dto.UserOpbdConfigDto;
import cn.com.sgcc.business.sgcc.opid.model.excel.StrategyConfigExport;
import cn.com.sgcc.business.sgcc.opid.model.excel.StrategyConfigImport;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * OpbdService
 */
public interface OpbdService {
    /**
     * queryConfigList
     *
     * @param unitId    unitId
     * @param startDate startDate
     * @return List<UserOpbdConfigDetail>
     */
    List<UserOpbdConfigDetail> queryConfigList(String unitId, String startDate);

    /**
     * queryRecordList
     *
     * @param unitId unitId
     * @param page   page
     * @return Page<List < UserOpbdConfigDto>>
     */
    Page<List<UserOpbdConfigDto>> queryRecordList(String unitId, Page<List<UserOpbdConfigDto>> page);

    /**
     * deleteRecordList
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     */
    void deleteRecordList(String unitId, String strategyDate);

    /**
     * saveConfigList
     *
     * @param unitIdList   unitIdList
     * @param type         type
     * @param startDate    startDate
     * @param endDate      endDate
     * @param timePartList timePartList
     * @param configList   configList
     * @return boolean
     */
    boolean saveConfigList(List<String> unitIdList,
                           Integer type,
                           String startDate,
                           String endDate,
                           List<Integer> timePartList,
                           List<UserOpbdConfigDetail> configList);

    /**
     * exportRecordData
     *
     * @param unitIdList unitIdList
     * @param startDate  startDate
     * @param endDate    endDate
     * @return Map<String, Collection < StrategyConfigExport>>
     */
    Map<String, Collection<StrategyConfigExport>> exportRecordData(List<String> unitIdList,
                                                                   String startDate,
                                                                   String endDate);

    /**
     * importRecordData
     *
     * @param unitIdList unitIdList
     * @param type       type
     * @param importList importList
     * @return boolean
     */
    boolean importRecordData(List<String> unitIdList, Integer type, List<StrategyConfigImport> importList);
}
