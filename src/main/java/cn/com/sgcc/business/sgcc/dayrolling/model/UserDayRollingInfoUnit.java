package cn.com.sgcc.business.sgcc.dayrolling.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "user_day_rolling_info_unit")
@IdClass(UserDayRollingInfoUnit.PrimaryKey.class)
public class UserDayRollingInfoUnit implements Serializable {

    @Id
    @Column
    String unitId;
    @Id
    @Column
    String strategyDate;
    @Id
    @Column
    String tradeSeqId;
    @Id
    @Column
    Integer timeCode;
    @Column
    String tradeRole;
    @Column
    String power;
    @Column
    String price;
    @Column
    String detail;
    @Column
    String createTime;

    @Data
    static class PrimaryKey implements Serializable {
        String unitId;
        String strategyDate;
        String tradeSeqId;
        Integer timeCode;
    }
}
