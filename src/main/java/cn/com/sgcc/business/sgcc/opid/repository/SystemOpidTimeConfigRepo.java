package cn.com.sgcc.business.sgcc.opid.repository;

import cn.com.sgcc.business.sgcc.opid.model.SystemOpidTimeConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface SystemOpidTimeConfigRepo extends JpaRepository<SystemOpidTimeConfig, Void> {
    /**
     * updateTimeConfig
     *
     * @param bidStartTime  bidStartTime
     * @param bidEndTime    bidEndTime
     * @param tradeTimePart tradeTimePart
     */
    @Modifying
    @Transactional
    @Query(value = "update system_opid_time_config set bidStartTime = :bidStartTime, bidEndTime = :bidEndTime"
                   + " where tradeTimePart = :tradeTimePart", nativeQuery = true)
    void updateTimeConfig(String bidStartTime, String bidEndTime, String tradeTimePart);

}
