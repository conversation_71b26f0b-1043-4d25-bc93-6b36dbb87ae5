package cn.com.sgcc.business.sgcc.imqpnewenergytrade.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "user_imqp_new_energy_trade_config_log")
public class ImqpNewEnergyTradeConfigLog implements Serializable {
    @Id
    @Column
    String logId;
    @Column
    String unitId;
    @Column
    String strategyDate;
    @Column
    String declareDate;
    @Column
    Double zcPower;
    @Column
    Double jcPower;
    @Column
    Double yfPower;
    @Column
    String tradeRole;
    @Column
    Double power;
    @Column
    Integer status;
    @Column
    String createTime;
    @Column
    String updateTime;
}
