package cn.com.sgcc.business.sgcc.opid.repository;

import cn.com.sgcc.business.sgcc.opid.model.UserOpidConfig;
import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * UserOpidConfigRepo
 */
@Repository
public interface UserOpidConfigRepo extends JpaRepository<UserOpidConfig, Void> {
    /**
     * findAllByUnitIdPage
     *
     * @param unitId unitId
     * @param limit  limit
     * @param offset offset
     * @return List<UserOpidConfig>
     */
    @Query(value = """
            select *
            from user_opid_config
            where unitId = :unitId
            order by strategyDate desc
            limit :limit offset :offset""", nativeQuery = true)
    List<UserOpidConfig> findAllByUnitIdPage(String unitId, Integer limit, Integer offset);

    /**
     * countAllByUnitId
     *
     * @param unitId unitId
     * @return long
     */
    long countAllByUnitId(String unitId);

    /**
     * removeByParams
     *
     * @param unitIds   unitIds
     * @param startDate startDate
     * @param endDate   endDate
     */
    @Modifying
    @Transactional
    @Query(value = """
            delete from user_opid_config
            where unitId in :unitIds
              and strategyDate >= :startDate
              and strategyDate <= :endDate""", nativeQuery = true)
    void removeByParams(Collection<String> unitIds, String startDate, String endDate);

    /**
     * removeByUnitIdAndStrategyDate
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     */
    void removeByUnitIdAndStrategyDate(String unitId, String strategyDate);
}
