package cn.com.sgcc.business.sgcc.dayrolling.controller;

import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.sgcc.dayrolling.service.DayRollingInfoService;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.config.role.RoleCondition;
import cn.com.sgcc.constants.DateConsts;
import cn.com.sgcc.constants.RoleConstants;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * DayRollingInfoJob
 */
@RestController
@RequestMapping("/api/v1/dayRollingInfoJob")
@CrossOrigin
@Slf4j
public class DayRollingInfoJob {

    @Resource
    UnitService unitService;
    @Resource
    private DayRollingInfoService dayRollingInfoService;

    /**
     * crawler
     *
     * @param param param
     * @return Result<Object>
     */
    @GetMapping("/crawler")
    public Result<Object> crawler(@RequestParam String param) {
        JSONObject params = JSONObject.parse(param);
        String unitId = params.getString("unitId");
        String date = params.getString("date");
        var firstByUnitId = unitService.findFirstByUnitId(RoleConstants.Type.DAY_ROLLING, unitId);
        log.info("手动调用爬取日滚动交易任务开始");
        if (firstByUnitId.isPresent()) {
            dayRollingInfoService.crawler(unitId, date);
        }
        log.info("手动调用爬取日滚动交易任务结束");
        return Result.ok();
    }

    /**
     * job
     */
    @Scheduled(cron = "${custom.dayRolling.info.cron}")
    public void job() {
        if (!RoleCondition.hasRole(RoleConstants.Type.DAY_ROLLING)) {
            return;
        }
        log.info("爬取日滚动交易任务开始");
        var unitList = unitService.getUnitList(RoleConstants.Type.DAY_ROLLING);
        var yesterday = LocalDate.now().plusDays(-1).format(DateConsts.DATE_FORMAT);
        for (SystemUnitConfig unitConfig : unitList) {
            try {
                dayRollingInfoService.crawler(unitConfig.getUnitId(), yesterday);
            } catch (Exception e) {
                log.warn("爬取失败: {}", unitConfig.getUnitId());
            }
        }
        log.info("爬取日滚动交易数据任务结束");
    }

    /**
     * 查询未来两日的交易时间
     */
    @Scheduled(cron = "${custom.dayRolling.info.dateCorn}")
    public void updateDateJob() {
        var start = LocalDate.now().plusDays(0).format(DateConsts.DATE_FORMAT);
        var end = LocalDate.now().plusDays(2).format(DateConsts.DATE_FORMAT);
        updateDateJob(start, end);
    }

    /**
     * 查询未来两日的交易时间
     *
     * @param startDate startDate
     * @param endDate   endDate
     */
    @RequestMapping("/update")
    public void updateDateJob(String startDate, String endDate) {
        if (!RoleCondition.hasRole(RoleConstants.Type.DAY_ROLLING)) {
            return;
        }
        log.info("爬取日滚动日期及价格任务开始: {} - {}", startDate, endDate);
        var unitList = unitService.getUnitList(RoleConstants.Type.DAY_ROLLING);
        if (unitList.isEmpty()) {
            log.warn("爬取日滚动日期及价格任务失败，没有找到交易单元");
            return;
        }
        var start = LocalDate.parse(startDate, DateConsts.DATE_FORMAT);
        var end = LocalDate.parse(endDate, DateConsts.DATE_FORMAT);
        var unitConfig = unitList.getFirst();
        try {
            var count = 0;
            while (!end.isBefore(start)) {
                var date1 = start.format(DateConsts.DATE_FORMAT);
                dayRollingInfoService.syncData(unitConfig.getUnitId(), date1, count == 0);
                start = start.plusDays(1);
                count++;
                if (count > 10) {
                    log.info("调用超过 10 次，结束");
                    break;
                }
            }
        } catch (Exception e) {
            log.warn("爬取失败: {}", unitConfig.getUnitId());
        }
        log.info("爬取日滚动日期及价格任务结束");
    }
}
