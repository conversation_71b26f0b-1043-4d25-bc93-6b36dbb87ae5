package cn.com.sgcc.business.sgcc.imqpnewenergytrade.controller;

import cn.com.sgcc.business.common.model.dto.Page;
import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.sgcc.dayrolling.model.excel.UserDayRollingRecordExport;
import cn.com.sgcc.business.sgcc.imqpnewenergytrade.model.ImqpNewEnergyTradeConfig;
import cn.com.sgcc.business.sgcc.imqpnewenergytrade.service.ImqpNewEnergyTradeService;
import cn.com.sgcc.config.ErrMsg;
import cn.com.sgcc.config.role.HasRole;
import cn.com.sgcc.config.role.RoleCondition;
import cn.com.sgcc.constants.RoleConstants;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * ImqpNewEnergyTradeController
 */
@Conditional({RoleCondition.class})
@HasRole(RoleConstants.Type.IMQP_NEW_ENERGY_TRADE)
@RestController
@RequestMapping("/api/v1/imqpNewEnergyTrade")
@CrossOrigin
@Slf4j
public class ImqpNewEnergyTradeController {
    @Resource
    private ImqpNewEnergyTradeService service;

    /**
     * queryConfigList
     *
     * @param param param
     * @return Result<List < ImqpNewEnergyTradeConfig>>
     */
    @GetMapping("/configList")
    @ErrMsg("获取配置列表失败")
    public Result<List<ImqpNewEnergyTradeConfig>> queryConfigList(@RequestParam String param) {
        JSONObject params = JSONObject.parse(param);
        String unitId = params.getString("unitId");
        String strategyDate = params.getString("strategyDate");
        return Result.ok(service.queryConfigList(unitId, strategyDate));
    }

    /**
     * queryRecordList
     *
     * @param param param
     * @return Result<Page < List < ImqpNewEnergyTradeConfig>>>
     */
    @GetMapping("/recordList")
    @ErrMsg("获取记录失败")
    public Result<Page<List<ImqpNewEnergyTradeConfig>>> queryRecordList(@RequestParam String param) {
        JSONObject params = JSONObject.parse(param);
        String unitId = params.getString("unitId");
        JSONObject pageParam = params.getJSONObject("page");
        Integer page = pageParam.getInteger("page");
        Integer pageSize = pageParam.getInteger("pageSize");
        return Result.ok(service.queryRecordList(unitId, new Page<>(page, pageSize)));
    }

    /**
     * saveConfigList
     *
     * @param params params
     * @return Result<Boolean>
     */
    @PutMapping("/configList")
    @ErrMsg("新增配置失败")
    public Result<Boolean> saveConfigList(@RequestBody JSONObject params) {
        String unitId = params.getString("unitId");
        String strategyDate = params.getString("strategyDate");
        List<ImqpNewEnergyTradeConfig> configList = params.getList("configList", ImqpNewEnergyTradeConfig.class);
        if (configList == null || configList.isEmpty()) {
            return Result.fail("数据为空");
        }
        service.saveConfigList(unitId, strategyDate, configList);
        return Result.ok();
    }

    /**
     * exportRecordList
     *
     * @param param param
     * @return Result<Map < String, Collection < UserDayRollingRecordExport>>>
     */
    @PostMapping("/exportRecordList")
    @ErrMsg("导出失败")
    public Result<Map<String, Collection<UserDayRollingRecordExport>>> exportRecordList(@RequestBody String param) {
        JSONObject params = JSONObject.parse(param);
        String unitId = params.getString("unitId");
        String strategyDate = params.getString("strategyDate");
        return Result.ok(service.exportRecordData(unitId, strategyDate));
    }

    /**
     * exportAllRecordList
     *
     * @return Result<Map < String, Collection < UserDayRollingRecordExport>>>
     */
    @PostMapping("/exportAllRecordList")
    @ErrMsg("导出失败")
    public Result<Map<String, Collection<UserDayRollingRecordExport>>> exportAllRecordList() {
        return Result.ok(service.exportRecordData(null, null));
    }

}
