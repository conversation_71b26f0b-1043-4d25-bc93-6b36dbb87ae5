package cn.com.sgcc.business.sgcc.dayrolling.repository;

import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingInfoUnit;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * UserDayRollingInfoUnitRepo
 */
@Repository
public interface UserDayRollingInfoUnitRepo extends JpaRepository<UserDayRollingInfoUnit, Void> {
    /**
     * findByUnitIdAndStrategyDateAndTradeSeqId
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @param tradeSeqId   tradeSeqId
     * @return List<UserDayRollingInfoUnit>
     */
    List<UserDayRollingInfoUnit> findByUnitIdAndStrategyDateAndTradeSeqId(String unitId,
                                                                          String strategyDate,
                                                                          String tradeSeqId);
}
