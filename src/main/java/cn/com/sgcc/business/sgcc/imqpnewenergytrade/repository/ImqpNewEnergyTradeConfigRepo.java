package cn.com.sgcc.business.sgcc.imqpnewenergytrade.repository;

import cn.com.sgcc.business.sgcc.imqpnewenergytrade.model.ImqpNewEnergyTradeConfig;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * ImqpNewEnergyTradeConfigRepo
 */
@Repository
public interface ImqpNewEnergyTradeConfigRepo
        extends JpaRepository<ImqpNewEnergyTradeConfig, ImqpNewEnergyTradeConfig.PrimaryKey> {
    /**
     * findByUnitIdAndStrategyDateAndDeclareDate
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @param declareDate  declareDate
     * @return Optional<ImqpNewEnergyTradeConfig>
     */
    Optional<ImqpNewEnergyTradeConfig> findByUnitIdAndStrategyDateAndDeclareDate(String unitId,
                                                                                 String strategyDate,
                                                                                 String declareDate);

    /**
     * findAllByStrategyDateAndDeclareDate
     *
     * @param strategyDate strategyDate
     * @param declareDate  declareDate
     * @return List<ImqpNewEnergyTradeConfig>
     */
    List<ImqpNewEnergyTradeConfig> findAllByStrategyDateAndDeclareDate(String strategyDate, String declareDate);

    /**
     * findAllByUnitIdAndStrategyDate
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @return List<ImqpNewEnergyTradeConfig>
     */
    List<ImqpNewEnergyTradeConfig> findAllByUnitIdAndStrategyDate(String unitId, String strategyDate);

    /**
     * countAllByUnitId
     *
     * @param unitId unitId
     * @return long
     */
    long countAllByUnitId(String unitId);

    /**
     * findAllByUnitIdPage
     *
     * @param unitId unitId
     * @param limit  limit
     * @param offset offset
     * @return List<ImqpNewEnergyTradeConfig>
     */
    @Query(value = """
            select *
            from user_imqp_new_energy_trade_config
            where unitId = :unitId
            order by strategyDate desc, declareDate desc
            limit :limit offset :offset
            """, nativeQuery = true)
    List<ImqpNewEnergyTradeConfig> findAllByUnitIdPage(String unitId, Integer limit, Integer offset);

}
