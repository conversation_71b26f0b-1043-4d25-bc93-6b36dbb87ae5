package cn.com.sgcc.business.sgcc.imqpnewenergytrade.repository;

import cn.com.sgcc.business.sgcc.imqpnewenergytrade.model.ImqpNewEnergyTradeConfigLog;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ImqpNewEnergyTradeConfigLogRepo extends JpaRepository<ImqpNewEnergyTradeConfigLog, String> {
    /**
     * findByUnitId
     *
     * @param unitId unitId
     * @return List<ImqpNewEnergyTradeConfigLog>
     */
    List<ImqpNewEnergyTradeConfigLog> findByUnitId(String unitId);
}
