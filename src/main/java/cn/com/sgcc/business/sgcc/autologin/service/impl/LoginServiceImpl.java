package cn.com.sgcc.business.sgcc.autologin.service.impl;

import static cn.com.sgcc.constants.RoleConstants.Type.AUTO_LOGIN;

import cn.com.sgcc.business.sgcc.autologin.model.dto.<PERSON>;
import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.sgcc.autologin.service.LoginService;
import cn.com.sgcc.business.sgcc.autologin.service.SgccLoginService;
import cn.com.sgcc.business.system.model.SystemAccountConfig;
import cn.com.sgcc.business.system.repository.SystemAccountConfigRepo;
import cn.com.sgcc.business.system.service.SystemService;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.constants.ProvConsts;
import cn.com.sgcc.util.LockUtil;
import cn.com.sgcc.util.http.ProxySgccRequest;
import cn.com.sgcc.util.http.ReqConfig;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class LoginServiceImpl implements LoginService {
    private static final ConcurrentHashMap<String, LoginInfo> LOGIN_MAP = new ConcurrentHashMap<>();

    @Value("${custom.pmos.login.retry}")
    int retry;
    @Value("${custom.pmos.provinceCode}")
    String provinceCode;
    @Resource
    private SystemAccountConfigRepo accountRepo;
    @Resource
    private UnitService unitService;
    @Resource
    private ProxySgccRequest proxySgccRequest;
    @Resource
    private SgccLoginService sgccLoginService;
    @Resource
    private SystemService systemService;

    /**
     * inited
     */
    @PostConstruct
    @Order
    public void inited() {
        retry = retry < 0 ? 0 : retry > 10 ? 10 : retry;
    }

    @Override
    public LoginInfo login(String unitId, boolean force) {
        return unitService.findFirstByUnitId(unitId)
                          .map(unitConfig -> loginByPlantId(unitConfig.getPlantId(), force))
                          .orElse(null);
    }

    @Override
    public LoginInfo login(@NonNull SystemAccountConfig accountConfig, boolean force) {
        var userName = accountConfig.getUserName();
        return LockUtil.lock(getClass(), userName, k -> {
            if (systemService.getRole(AUTO_LOGIN).contains(accountConfig.getUserName())) {
                // 登录最多尝试三次
                for (int i = 0; i < retry; i++) {
                    // 同一个账号同时只登录一次
                    try {
                        if (force && i == 0) {
                            LOGIN_MAP.remove(userName);
                        }
                        log.info("获取登录信息, 账号: {}", userName);
                        if (getLoginData(accountConfig, true) == null) {
                            log.info("账号: {}, 第 {} 次登录", userName, i + 1);
                            var loginInfo = sgccLoginService.login(accountConfig);
                            if (loginInfo == null) {
                                continue;
                            }
                            proxySgccRequest.updateGatewayToken(loginInfo, ReqConfig.of(accountConfig));
                            log.info("账号: {}, 登录结果: {}", userName, loginInfo);
                            LOGIN_MAP.put(userName, loginInfo);
                        } else {
                            break;
                        }
                    } catch (Exception e) {
                        log.error("登录过程中失败", e);
                    }
                }
            } else {
                log.warn("账号: {} 没有自动登录权限", accountConfig.getUserName());
            }
            return getLoginData(accountConfig, false);
        });
    }

    @Override
    public LoginInfo loginByPlantId(String plantId, boolean force) {
        return accountRepo.findFirstByPlantId(plantId)
                          .map(accountConfig -> login(accountConfig, force))
                          .orElse(null);
    }

    @Override
    public LoginInfo loginByUserName(String userName, boolean force) {
        return accountRepo.findFirstByUserName(userName)
                          .map(accountConfig -> login(accountConfig, force))
                          .orElse(null);
    }

    @Override
    public List<Cookie> getCookies(String userName) {
        var cookies = new ArrayList<Cookie>();
        var loginData = loginByUserName(userName, false);
        if (loginData != null) {
            cookies.add(setCookieInfo(new Cookie("Admin-Token", loginData.getTicket())));
            cookies.add(setCookieInfo(new Cookie("X-Ticket", loginData.getTicket())));
            cookies.add(setCookieInfo(new Cookie("Gray-Tag", loginData.getGrayTag())));
            cookies.add(setCookieInfo(new Cookie("CurrentRoute", "/dashboard")));
            cookies.add(setCookieInfo(new Cookie("ClientTag", "OUTNET_BROWSE")));
        } else {
            cookies.add(setCookieInfo(new Cookie("Admin-Token", "null")));
            cookies.add(setCookieInfo(new Cookie("X-Ticket", "null")));
            cookies.add(setCookieInfo(new Cookie("Gray-Tag", "null")));
            cookies.add(setCookieInfo(new Cookie("CurrentRoute", "/dashboard")));
            cookies.add(setCookieInfo(new Cookie("ClientTag", "OUTNET_BROWSE")));
        }
        return cookies;
    }

    /**
     * setCookieInfo
     *
     * @param cookie cookie
     * @return Cookie
     */
    public Cookie setCookieInfo(Cookie cookie) {
        cookie.setUrl(ProvConsts.Prov.valueOf(provinceCode).getOrigin());
        cookie.setDomain(ProvConsts.Prov.valueOf(provinceCode).getDomain());
        cookie.setPath("/");
        cookie.setSecure(false);
        cookie.setHttpOnly(false);
        return cookie;
    }

    @Override
    public void addLogin(JSONObject info) {
        var username = info.getString("username");
        var ticket = info.getString("ticket");
        var accountConfigOptional = accountRepo.findFirstByUserName(username);
        if (accountConfigOptional.isPresent()) {
            var accountConfig = accountConfigOptional.get();
            var loginInfo = new LoginInfo(accountConfig, new LoginInfo.LoginResp(0, "Success", ticket));
            proxySgccRequest.updateGatewayToken(loginInfo, ReqConfig.of(accountConfig));
            LOGIN_MAP.put(accountConfig.getUserName(), loginInfo);
        }
    }

    @Override
    public LoginInfo getLoginData(@NonNull SystemAccountConfig accountConfig, boolean friendly) {
        var userName = accountConfig.getUserName();
        log.info("检测登录状态: {}", accountConfig);
        return LockUtil.lock(getClass(), userName, k -> {
            try {
                LoginInfo loginInfo = LOGIN_MAP.get(userName);
                if (loginInfo != null) {
                    if (StringUtils.isNotBlank(loginInfo.getTicket()) && !loginInfo.getTokenStack().empty()) {
                        proxySgccRequest.updateGatewayToken(loginInfo, ReqConfig.of(accountConfig));
                        return loginInfo;
                    }
                }
            } catch (Exception e) {
                if (friendly) {
                    log.warn("检测登录状态失败，尝试重新登录");
                } else {
                    log.warn("检测登录状态失败", e);
                }
            }
            LOGIN_MAP.remove(userName);
            return null;
        });
    }

    @Override
    public Optional<LoginInfo> getOne() {
        return LOGIN_MAP.values().stream().findFirst();
    }
}
