package cn.com.sgcc.business.sgcc.imqpnewenergytrade.service;

import static cn.com.sgcc.constants.RoleConstants.Type.IMQP_NEW_ENERGY_TRADE;

import cn.com.sgcc.business.common.model.dto.Page;
import cn.com.sgcc.business.sgcc.dayrolling.model.excel.UserDayRollingRecordExport;
import cn.com.sgcc.business.sgcc.imqpnewenergytrade.model.ImqpNewEnergyTradeConfig;
import cn.com.sgcc.business.sgcc.imqpnewenergytrade.model.ImqpNewEnergyTradeConfigLog;
import cn.com.sgcc.business.sgcc.imqpnewenergytrade.repository.ImqpNewEnergyTradeConfigLogRepo;
import cn.com.sgcc.business.sgcc.imqpnewenergytrade.repository.ImqpNewEnergyTradeConfigRepo;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.constants.DateConsts;
import cn.com.sgcc.constants.IdConsts;
import cn.com.sgcc.util.LockUtil;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * ImqpNewEnergyTradeService
 */
@Service
@Slf4j
public class ImqpNewEnergyTradeService {
    @Resource
    private UnitService unitService;
    @Resource
    private ImqpNewEnergyTradeConfigRepo repo;
    @Resource
    private ImqpNewEnergyTradeConfigLogRepo logRepo;
    @Resource
    private ImqpNewEnergyTradeJobService jobService;

    /**
     * queryConfigList
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @return List<ImqpNewEnergyTradeConfig>
     */
    public List<ImqpNewEnergyTradeConfig> queryConfigList(String unitId, String strategyDate) {
        var unitOptional = unitService.findFirstByUnitId(IMQP_NEW_ENERGY_TRADE, unitId);
        if (unitOptional.isEmpty()) {
            return Collections.emptyList();
        }
        return repo.findAllByUnitIdAndStrategyDate(unitId, strategyDate);
    }

    /**
     * queryRecordList
     *
     * @param unitId unitId
     * @param page   page
     * @return Page<List < ImqpNewEnergyTradeConfig>>
     */
    public Page<List<ImqpNewEnergyTradeConfig>> queryRecordList(String unitId,
                                                                Page<List<ImqpNewEnergyTradeConfig>> page) {
        var unitOptional = unitService.findFirstByUnitId(IMQP_NEW_ENERGY_TRADE, unitId);
        if (unitOptional.isPresent()) {
            var total = repo.countAllByUnitId(unitId);
            var data = repo.findAllByUnitIdPage(unitId, page.getPageSize(), (page.getPage() - 1) * page.getPageSize());
            var logList = logRepo.findByUnitId(unitId);
            page.setTotal(total);
            page.setData(data);
            page.getData().forEach(config -> config.setDetails(
                    logList.stream()
                           .filter(configLog ->
                                           config.getStrategyDate().equals(configLog.getStrategyDate())
                                           && config.getDeclareDate().equals(configLog.getDeclareDate()))
                           .sorted(Comparator.comparing(ImqpNewEnergyTradeConfigLog::getCreateTime).reversed())
                           .toList()
            ));
        }
        return page;
    }

    /**
     * saveConfigList
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @param configList   configList
     */
    public void saveConfigList(String unitId, String strategyDate, List<ImqpNewEnergyTradeConfig> configList) {
        for (ImqpNewEnergyTradeConfig tradeConfig : configList) {
            tradeConfig.setUnitId(unitId);
            tradeConfig.setStrategyDate(strategyDate);
            LockUtil.lock(tradeConfig.getLockKey(), k -> {
                var configOptional = repo.findByUnitIdAndStrategyDateAndDeclareDate(unitId, strategyDate,
                                                                                    tradeConfig.getDeclareDate());
                if (configOptional.isEmpty()) {
                    return;
                }
                var config = configOptional.get();
                if (tradeConfig.getPower() == null || tradeConfig.getPower() == 0) {
                    tradeConfig.setPower(null);
                    tradeConfig.setTradeRole(null);
                }
                config.setPower(tradeConfig.getPower());
                config.setTradeRole(tradeConfig.getTradeRole());
                config.setUpdateTime(LocalDateTime.now().format(DateConsts.DATE_TIME_FORMAT));
                ImqpNewEnergyTradeConfigLog configLog;
                if (config.getStatus() == null || config.getStatus() != 0) {
                    config.setStatus(0);
                    config.setLogId(IdConsts.SNOWFLAKE.nextIdStr());
                    configLog = new ImqpNewEnergyTradeConfigLog();
                    BeanUtils.copyProperties(config, configLog);
                    configLog.setCreateTime(config.getUpdateTime());
                } else {
                    var logId = config.getLogId();
                    var configLogOptional = logRepo.findById(logId);
                    if (configLogOptional.isPresent()) {
                        configLog = configLogOptional.get();
                    } else {
                        configLog = new ImqpNewEnergyTradeConfigLog();
                        BeanUtils.copyProperties(config, configLog);
                        configLog.setCreateTime(config.getUpdateTime());
                    }
                }
                configLog.setPower(config.getPower());
                configLog.setTradeRole(config.getTradeRole());
                configLog.setStatus(config.getStatus());
                configLog.setUpdateTime(config.getUpdateTime());
                logRepo.saveAndFlush(configLog);
                repo.saveAndFlush(config);
                var todayStr = LocalDate.now().format(DateConsts.DATE_FORMAT);
                if (todayStr.startsWith(config.getStrategyDate()) && todayStr.equals(config.getDeclareDate())) {
                    jobService.declare(config);
                }
            });
        }
    }

    /**
     * exportRecordData
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @return Map<String, Collection < UserDayRollingRecordExport>>
     */
    public Map<String, Collection<UserDayRollingRecordExport>> exportRecordData(String unitId, String strategyDate) {

        return Map.of();
    }
}
