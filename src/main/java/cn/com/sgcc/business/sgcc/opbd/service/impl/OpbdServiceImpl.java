package cn.com.sgcc.business.sgcc.opbd.service.impl;

import static cn.com.sgcc.constants.DateConsts.DATE_FORMAT;
import static cn.com.sgcc.constants.DateConsts.DATE_TIME_FORMAT;
import static cn.com.sgcc.util.TimeUtil.HOUR_TIME_PART_MAP_REV;

import cn.com.sgcc.business.common.model.dto.Page;
import cn.com.sgcc.business.sgcc.opbd.model.UserOpbdConfig;
import cn.com.sgcc.business.sgcc.opbd.model.UserOpbdConfigDetail;
import cn.com.sgcc.business.sgcc.opbd.model.dto.UserOpbdConfigDto;
import cn.com.sgcc.business.sgcc.opbd.repository.UserOpbdConfigDetailRepo;
import cn.com.sgcc.business.sgcc.opbd.repository.UserOpbdConfigRepo;
import cn.com.sgcc.business.sgcc.opbd.service.OpbdService;
import cn.com.sgcc.business.sgcc.opid.model.excel.StrategyConfigExport;
import cn.com.sgcc.business.sgcc.opid.model.excel.StrategyConfigImport;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.constants.ProvConsts;
import cn.com.sgcc.constants.RoleConstants;
import cn.com.sgcc.util.TimeUtil;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class OpbdServiceImpl implements OpbdService {

    @Resource
    private UnitService unitService;
    @Resource
    private UserOpbdConfigRepo configRepo;
    @Resource
    private UserOpbdConfigDetailRepo configDetailRepo;
    @Value("${custom.opbd.startTime}")
    String reportStartTime;
    @Value("${custom.pmos.provinceCode}")
    String provinceCode;

    @Override
    public List<UserOpbdConfigDetail> queryConfigList(String unitId, String startDate) {
        return configDetailRepo.findAllByParams(Collections.singletonList(unitId), startDate, startDate);
    }

    @Override
    public Page<List<UserOpbdConfigDto>> queryRecordList(String unitId, Page<List<UserOpbdConfigDto>> page) {
        var count = configRepo.countAllByUnitId(unitId);
        if (count == 0) {
            page.setData(new ArrayList<>());
            return page;
        }
        var configDayList = configRepo.findAllByUnitIdPage(unitId, page.getPageSize(),
                                                           (page.getPage() - 1) * page.getPageSize());
        if (configDayList.isEmpty()) {
            page.setData(new ArrayList<>());
            return page;
        }
        var now = LocalDateTime.now();
        LocalDateTime nextSpotTime = getNextSpotTime(now);
        List<String> days = configDayList.stream().map(UserOpbdConfig::getStrategyDate).collect(Collectors.toList());
        List<UserOpbdConfigDetail> configDetails = configDetailRepo.findAllByUnitIdAndStrategyDateIn(unitId, days);
        configDetails.forEach(detail -> {
            LocalDateTime dateTime = LocalDate.parse(detail.getStrategyDate(), DATE_FORMAT)
                                              .atTime(detail.getTimePart() * 2, 15);
            if (detail.getStatus() != 1 && !dateTime.isBefore(nextSpotTime)) {
                detail.setStatus(999);
            }
        });
        Map<String, List<UserOpbdConfigDetail>> configDetailMapByTime =
                configDetails.stream().collect(Collectors.groupingBy(UserOpbdConfigDetail::getStrategyDate));
        List<UserOpbdConfigDto> list = configDayList.stream().map(UserOpbdConfigDto::new).toList();
        list.forEach(it -> it.setDetails(configDetailMapByTime.get(it.getStrategyDate())));
        page.setData(list);
        page.setTotal(count);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void deleteRecordList(String unitId, String strategyDate) {
        configRepo.removeByUnitIdAndStrategyDate(unitId, strategyDate);
        configDetailRepo.removeByUnitIdAndStrategyDate(unitId, strategyDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean saveConfigList(List<String> unitIdList, Integer type, String startDate, String endDate,
                                  List<Integer> timePartList, List<UserOpbdConfigDetail> configList) {
        boolean ignoredConfig = false;
        var unitConfigMap = unitService.getUnitMap(RoleConstants.Type.OPBD);
        var now = LocalDateTime.now();
        var nowTime = now.format(DATE_TIME_FORMAT);
        var saveConfigDetails = new ArrayList<UserOpbdConfigDetail>();
        var saveConfigs = new HashSet<UserOpbdConfig>();
        var startDay = LocalDate.parse(startDate, DATE_FORMAT);
        var endDay = LocalDate.parse(endDate, DATE_FORMAT);
        var nextSpotTime = getNextSpotTime(now);
        while (!startDay.isAfter(endDay)) {
            var strategyDate = startDay.format(DATE_FORMAT);
            var timeParts = new ArrayList<Integer>();
            for (var configDetail : configList) {
                var timePart = configDetail.getTimePart();
                // 跳过下个可申报时段之前的配置
                if (startDay.atTime(timePart * 2, 15).isBefore(nextSpotTime)) {
                    ignoredConfig = true;
                    continue;
                }
                timeParts.add(timePart);
                for (var unitId : unitIdList) {
                    var unitConfig = unitConfigMap.get(unitId);
                    if (unitConfig.getType() == null) {
                        continue;
                    }
                    // 跳过光伏不在 08:15 - 18:00 的配置，黑龙江为06:15-18:00
                    if (ProvConsts.Prov.hlj.name().equals(provinceCode)) {
                        if (unitConfig.getType() == 1 && (timePart < 3 || timePart > 8)) {
                            ignoredConfig = true;
                            continue;
                        }
                    } else if ((timePart < 4 || timePart > 8) && unitConfig.getType() == 1) {
                        ignoredConfig = true;
                        continue;
                    }
                    var saveConfig = new UserOpbdConfig();
                    saveConfig.setUnitId(unitId);
                    saveConfig.setUnitName(unitConfigMap.get(unitId).getUnitName());
                    saveConfig.setStrategyDate(strategyDate);
                    saveConfig.setStrategyType(type);
                    saveConfig.setCreateTime(nowTime);
                    saveConfigs.add(saveConfig);
                    var saveConfigDetail = new UserOpbdConfigDetail();
                    BeanUtils.copyProperties(configDetail, saveConfigDetail);
                    saveConfigDetail.setUnitId(unitId);
                    saveConfigDetail.setDispatchId(unitConfigMap.get(unitId).getDispatchId());
                    saveConfigDetail.setStrategyDate(strategyDate);
                    saveConfigDetail.setStrategyType(type);
                    saveConfigDetail.setStatus(0);
                    saveConfigDetail.setCreateTime(nowTime);
                    saveConfigDetails.add(saveConfigDetail);
                }
            }
            if (!timeParts.isEmpty()) {
                configRepo.removeByParams(unitIdList, strategyDate, strategyDate);
                configDetailRepo.removeByParams(unitIdList, strategyDate, strategyDate, timeParts);
            }
            startDay = startDay.plusDays(1);
        }
        configRepo.saveAllAndFlush(saveConfigs);
        configDetailRepo.saveAllAndFlush(saveConfigDetails);
        return ignoredConfig;
    }

    @Override
    public Map<String, Collection<StrategyConfigExport>> exportRecordData(List<String> unitIdList,
                                                                          String startDate, String endDate) {
        HashMap<String, Collection<StrategyConfigExport>> hashMap = new HashMap<>();
        Map<String, SystemUnitConfig> unitMap = unitService.getUnitMap(RoleConstants.Type.OPBD);
        LocalDateTime startDay = LocalDate.parse(startDate, DATE_FORMAT).atTime(0, 0);
        LocalDateTime endDay = LocalDate.parse(endDate, DATE_FORMAT).atTime(0, 0).plusDays(1);
        List<String> allTime = TimeUtil.customTimeAxis(startDay, endDay, 60 * 15, "yyyy-MM-dd HH:mm", true, true);
        Map<String, List<UserOpbdConfigDetail>> detailMapByUnit =
                configDetailRepo.findAllByParams(unitIdList, startDate, endDate).stream()
                                .collect(Collectors.groupingBy(UserOpbdConfigDetail::getUnitId));
        for (String unitId : unitIdList) {
            SystemUnitConfig systemUnitConfig = unitMap.get(unitId);
            if (systemUnitConfig != null) {
                String unitName = systemUnitConfig.getUName();
                Map<String, UserOpbdConfigDetail> map = new HashMap<>();
                for (UserOpbdConfigDetail detail : detailMapByUnit.getOrDefault(unitId, new ArrayList<>())) {
                    String startTime = detail.getStartTime();
                    map.put(detail.getStrategyDate() + " " + startTime, detail);
                }
                List<StrategyConfigExport> collect = new ArrayList<>();
                for (String dateTime : allTime) {
                    UserOpbdConfigDetail detail = map.get(dateTime);
                    if (detail == null) {
                        detail = new UserOpbdConfigDetail();
                        detail.setStrategyDate(dateTime.substring(0, 10));
                        detail.setStartTime(dateTime.substring(11));
                    }
                    StrategyConfigExport export = new StrategyConfigExport();
                    export.setStrategyDate(detail.getStrategyDate());
                    export.setStartTime(detail.getStartTime());
                    export.setPowerLimit(detail.getPowerLimit());
                    if (detail.getPercent() != null) {
                        export.setPercent(BigDecimal.valueOf(detail.getPercent())
                                                    .divide(new BigDecimal(100), 3, RoundingMode.HALF_UP)
                                                    .doubleValue());
                    }
                    export.setReportPower(detail.getReportPower());
                    export.setReportPrice(detail.getReportPrice());
                    collect.add(export);
                }
                hashMap.put(unitName, collect);
            }
        }
        return hashMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean importRecordData(List<String> unitIdList, Integer type, List<StrategyConfigImport> importList) {
        ArrayList<UserOpbdConfigDetail> configList = new ArrayList<>();
        importList.sort((o1, o2) -> {
            if (o1.getStrategyDate().equals(o2.getStrategyDate())) {
                return o1.getStartTime().compareTo(o2.getStartTime());
            }
            return o1.getStrategyDate().compareTo(o2.getStrategyDate());
        });
        for (StrategyConfigImport configImport : importList) {
            Double percent = configImport.getPercent();
            Double price = configImport.getPrice();
            if (percent != null && price != null) {
                percent = new BigDecimal(percent).multiply(new BigDecimal(100))
                                                 .setScale(2, RoundingMode.HALF_UP)
                                                 .doubleValue();
                price = new BigDecimal(price).setScale(0, RoundingMode.HALF_UP).doubleValue();
                if (price > 3000) {
                    price = 3000D;
                }
                UserOpbdConfigDetail configDetail = new UserOpbdConfigDetail();
                configDetail.setStrategyDate(configImport.getStrategyDate());
                configDetail.setStartTime(configImport.getStartTime());
                configDetail.setEndTime(configImport.getStartTime());
                configDetail.setTimePart(HOUR_TIME_PART_MAP_REV.get(configImport.getStartTime()));
                configDetail.setPercent(percent);
                configDetail.setPrice(price.intValue());
                configList.add(configDetail);
            }
        }
        boolean ignoredConfig = false;
        LocalDateTime now = LocalDateTime.now();
        Map<String, SystemUnitConfig> unitConfigMap = unitService.getUnitMap(RoleConstants.Type.OPBD);
        List<UserOpbdConfigDetail> saveConfigDetails = new ArrayList<>();
        Set<UserOpbdConfig> saveConfigs = new HashSet<>();
        LocalDateTime nextSpotTime = getNextSpotTime(now);
        String lastDay = configList.getFirst().getStrategyDate();
        Integer lastTimePart = configList.getFirst().getTimePart();
        for (UserOpbdConfigDetail configDetail : configList) {
            String day = configDetail.getStrategyDate();
            Integer timePart = configDetail.getTimePart();
            LocalDate startDay = LocalDate.parse(day, DATE_FORMAT);
            for (String unitId : unitIdList) {
                // 跳过下个可申报时段之前的配置
                if (startDay.atTime(timePart * 2, 15).isBefore(nextSpotTime)) {
                    ignoredConfig = true;
                    continue;
                }
                String strategyDate = startDay.format(DATE_FORMAT);
                String nowTime = now.format(DATE_TIME_FORMAT);
                UserOpbdConfig saveConfig = new UserOpbdConfig();
                saveConfig.setUnitId(unitId);
                saveConfig.setUnitName(unitConfigMap.get(unitId).getUnitName());
                saveConfig.setStrategyDate(strategyDate);
                saveConfig.setStrategyType(type);
                saveConfig.setCreateTime(nowTime);
                saveConfigs.add(saveConfig);
                UserOpbdConfigDetail saveConfigDetail = new UserOpbdConfigDetail();
                BeanUtils.copyProperties(configDetail, saveConfigDetail);
                saveConfigDetail.setUnitId(unitId);
                saveConfigDetail.setDispatchId(unitConfigMap.get(unitId).getDispatchId());
                saveConfigDetail.setStrategyDate(strategyDate);
                saveConfigDetail.setStrategyType(type);
                saveConfigDetail.setStatus(0);
                saveConfigDetail.setCreateTime(nowTime);
                saveConfigDetails.add(saveConfigDetail);
            }
            boolean dayChanged = !day.equals(lastDay);
            boolean timePartChanged = !Objects.equals(timePart, lastTimePart);
            if (dayChanged) {
                lastDay = day;
            }
            if (timePartChanged) {
                lastTimePart = timePart;
            }
            if (dayChanged || timePartChanged) {
                configDetailRepo.removeByParams(unitIdList, day, day, Collections.singletonList(timePart));
            }
        }
        configRepo.saveAllAndFlush(saveConfigs);
        configDetailRepo.saveAllAndFlush(saveConfigDetails);
        return ignoredConfig;
    }

    /**
     * 获取下个申报时段申报的数据的开始时间
     *
     * @param time 当前时间
     * @return 时间
     */
    private LocalDateTime getNextSpotTime(LocalDateTime time) {
        var startTime = LocalDateTime.parse(time.format(DATE_FORMAT) + " " + reportStartTime, DATE_TIME_FORMAT);
        // 尚未开始申报
        if (time.isBefore(startTime)) {
            return startTime.toLocalDate().atTime(0, 1);
        } else {
            return startTime.toLocalDate().plusDays(1).atTime(0, 1);
        }
    }
}
