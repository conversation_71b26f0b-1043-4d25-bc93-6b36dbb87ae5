package cn.com.sgcc.business.sgcc.profitanalysis.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "data_profit_analysis")
@IdClass(DataProfitAnalysis.PrimaryKey.class)
public class DataProfitAnalysis implements Serializable {
    @Id
    @Column
    private String unitId;
    @Id
    @Column
    private String date;
    @Column
    private String ipDayAheadPrice;
    @Column
    private String ipRealTimePrice;
    @Column
    private String opbdReportPower;
    @Column
    private String opbdBidPower;
    @Column
    private String opbdDayAheadBidPrice;
    @Column
    private String opidReportPower;
    @Column
    private String opidBidPower;
    @Column
    private String opidRealTimeBidPrice;
    @Column
    private String createTime;

    @Data
    static class PrimaryKey implements Serializable {
        private String unitId;
        private String date;
    }
}
