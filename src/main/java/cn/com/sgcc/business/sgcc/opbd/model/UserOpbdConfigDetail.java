package cn.com.sgcc.business.sgcc.opbd.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "user_opbd_config_detail")
@IdClass(UserOpbdConfigDetail.PrimaryKey.class)
public class UserOpbdConfigDetail implements Serializable {
    @Id
    @Column
    private String unitId;
    @Id
    @Column
    private String strategyDate;
    @Id
    @Column
    private String startTime;
    @Id
    @Column
    private String endTime;
    @Column
    private Integer strategyType;
    @Column
    private Double percent; // 有限额时比例
    @Column
    private Integer price; // 用户输入电价
    @Column
    private Integer reportPower; // 实际申报电量
    @Column
    private Integer reportPrice; // 实际申报电价
    @Column
    private Double powerLimit; // 限额
    @Column
    private Integer status; // 0: 未执行 1: 已执行
    @Column
    private Integer timePart; // 时段 0 - 11
    @Column
    private String dispatchId;
    @Column
    private String createTime;
    @Column
    private String updateTime;

    @Data
    static class PrimaryKey implements Serializable {
        private String unitId;
        private String strategyDate;
        private String endTime;
        private String startTime;
    }
}
