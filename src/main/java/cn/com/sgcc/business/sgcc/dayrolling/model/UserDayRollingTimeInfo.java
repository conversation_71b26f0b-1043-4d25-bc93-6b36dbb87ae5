package cn.com.sgcc.business.sgcc.dayrolling.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

@Data
@Entity
@Table(name = "user_day_rolling_time_info")
@IdClass(UserDayRollingTimeInfo.PrimaryKey.class)
public class UserDayRollingTimeInfo implements Serializable {
    @Id
    @Column
    String strategyDate;
    @Id
    @Column
    String declareDate;
    @Id
    @Column
    String tradeseqId;
    @Column
    String beginDate;
    @Column
    String endDate;
    @Column
    String bidDate;
    @Column
    String startTime;
    @Column
    String endTime;
    @Column
    String startTime1;
    @Column
    String endTime1;
    @Column
    String startTime2;
    @Column
    String endTime2;
    @Column
    String tradeBatchId;
    @Column
    String tradeCycle;
    @Column
    String tradeStage;
    @Column
    String tradeseqName;
    @Column
    String tradeseqStatus;
    @Column
    String createTime;
    @Transient
    LocalDateTime localStartTime1;
    @Transient
    LocalDateTime localEndTime1;
    @Transient
    LocalDateTime localStartTime2;
    @Transient
    LocalDateTime localEndTime2;

    @Data
    static class PrimaryKey implements Serializable {
        String strategyDate;
        String declareDate;
        String tradeseqId;
    }
}
