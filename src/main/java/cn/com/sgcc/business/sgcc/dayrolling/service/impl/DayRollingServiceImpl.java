package cn.com.sgcc.business.sgcc.dayrolling.service.impl;

import static cn.com.sgcc.constants.RoleConstants.Type.DAY_ROLLING;

import cn.com.sgcc.business.common.model.dto.Page;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingConfigDetail;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingConfigDetailLog;
import cn.com.sgcc.business.sgcc.dayrolling.model.dto.UserDayRollingConfigDetailDto;
import cn.com.sgcc.business.sgcc.dayrolling.model.excel.UserDayRollingRecordExport;
import cn.com.sgcc.business.sgcc.dayrolling.repository.UserDayRollingConfigDetailLogRepo;
import cn.com.sgcc.business.sgcc.dayrolling.repository.UserDayRollingConfigDetailRepo;
import cn.com.sgcc.business.sgcc.dayrolling.service.DayRollingService;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.constants.DateConsts;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DayRollingServiceImpl implements DayRollingService {
    @Resource
    UserDayRollingConfigDetailRepo configDetailRepo;
    @Resource
    UserDayRollingConfigDetailLogRepo configDetailLogRepo;
    @Resource
    UnitService unitService;

    @Override
    public List<UserDayRollingConfigDetail> queryConfigList(String unitId, String strategyDate, Integer type) {
        var list = configDetailRepo.findAllByUnitIdAndStrategyDateAndType(unitId, strategyDate, type);
        list.forEach(detail -> {
            if (detail.getTradeRoleLock() != null && !detail.getTradeRoleLock().equals(detail.getTradeRole())) {
                detail.setTradeRole(detail.getTradeRoleLock());
                detail.setPower(null);
                detail.setPrice(null);
            }
        });
        return list;
    }

    @Override
    public Page<List<UserDayRollingConfigDetailDto>> queryRecordList(String unitId, Integer type,
                                                                     Page<List<UserDayRollingConfigDetailDto>> page) {
        var count = configDetailRepo.countAllByUnitIdAndType(unitId, type);
        if (count == 0) {
            page.setData(new ArrayList<>());
            return page;
        }
        var configDayList = configDetailRepo.findAllByUnitIdAndTypePage(unitId, type, page.getPageSize(),
                                                                        (page.getPage() - 1) * page.getPageSize());
        if (configDayList.isEmpty()) {
            page.setData(new ArrayList<>());
            return page;
        }
        var strategyDates = configDayList.stream().map(UserDayRollingConfigDetail::getStrategyDate).distinct().toList();
        var declareDates = configDayList.stream().map(UserDayRollingConfigDetail::getDeclareDate).distinct().toList();
        var configDetails = configDetailRepo.findAllByUnitIdAndStrategyDateInAndDeclareDateInAndType(
                unitId, strategyDates, declareDates, type);
        var configDetailLogs = configDetailLogRepo
                .findAllByUnitIdAndStrategyDateInAndDeclareDateInAndTypeOrderByCreateTimeDesc(
                        unitId, strategyDates, declareDates, type);
        var configDetailMapByTime = configDetails.stream()
                                                 .collect(Collectors.groupingBy(
                                                         it -> it.getStrategyDate() + "_" + it.getDeclareDate()
                                                 ));
        var configDetailLogMapByTime = configDetailLogs.stream()
                                                       .collect(Collectors.groupingBy(
                                                               it -> it.getStrategyDate() + "_" + it.getDeclareDate()
                                                       ));
        var list = configDayList.stream().map(UserDayRollingConfigDetailDto::new).toList();
        list.forEach(it -> {
            var key = it.getStrategyDate() + "_" + it.getDeclareDate();
            it.setDetails(configDetailMapByTime.get(key));
            it.setDetailLogs(configDetailLogMapByTime.get(key));
        });
        page.setData(list);
        page.setTotal(count);
        return page;
    }

    @Override
    public Boolean saveConfigList(String strategyDate, String declareDate, List<String> unitIdList, Integer type,
                                  List<UserDayRollingConfigDetail> configList) {
        var detailsToSave = new ArrayList<UserDayRollingConfigDetail>();
        var detailLogsToSave = new ArrayList<UserDayRollingConfigDetailLog>();
        for (String unitId : unitIdList) {
            for (UserDayRollingConfigDetail detail : configList) {
                var detailToSave = new UserDayRollingConfigDetail();
                BeanUtils.copyProperties(detail, detailToSave);
                detailToSave.setUnitId(unitId);
                detailToSave.setType(type);
                detailToSave.setStrategyDate(strategyDate);
                detailToSave.setDeclareDate(declareDate);
                detailToSave.setCreateTime(LocalDateTime.now().format(DateConsts.DATE_TIME_FORMAT));
                detailToSave.setStatus(0);
                detailsToSave.add(detailToSave);
                var detailLogToSave = new UserDayRollingConfigDetailLog();
                BeanUtils.copyProperties(detailToSave, detailLogToSave);
                detailLogToSave.setCreateTime(LocalDateTime.now().format(DateConsts.DATE_TIME_FORMAT));
                detailLogsToSave.add(detailLogToSave);
            }
        }
        configDetailRepo.saveAllAndFlush(detailsToSave);
        configDetailLogRepo.saveAllAndFlush(detailLogsToSave);
        return true;
    }

    static List<String> timeMap = List.of("00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00",
                                          "07:00", "08:00", "09:00", "10:00", "11:00", "12:00",
                                          "13:00", "14:00", "15:00", "16:00", "17:00", "18:00",
                                          "19:00", "20:00", "21:00", "22:00", "23:00", "24:00");

    @Override
    public Map<String, Collection<UserDayRollingRecordExport>> exportRecordData(List<String> unitIdList,
                                                                                String strategyDate,
                                                                                Integer type) {
        Map<String, Collection<UserDayRollingRecordExport>> map = new TreeMap<>();
        if (unitIdList == null) {
            unitIdList = unitService.getUnitList(DAY_ROLLING).stream().map(SystemUnitConfig::getUnitId).toList();
        }
        for (String unitId : unitIdList) {
            var unitConfig = unitService.findFirstByUnitId(unitId);
            if (unitConfig.isPresent()) {
                List<UserDayRollingConfigDetail> configDayList;
                if (strategyDate == null) {
                    configDayList = configDetailRepo.findAllByUnitIdAndType(unitId, type);
                } else {
                    configDayList = configDetailRepo.findAllByUnitIdAndStrategyDateAndType(unitId, strategyDate, type);
                }
                var mapByDeclareDate = configDayList.stream()
                                                    .collect(Collectors.groupingBy(
                                                            it -> it.getStrategyDate() + " " + it.getDeclareDate()
                                                    ));
                for (String declareDate : mapByDeclareDate.keySet()) {
                    var key = (unitConfig.get().getUName() + " " + declareDate).replace("-", "");
                    map.put(key, mapByDeclareDate.get(declareDate).stream().map(detail -> {
                        var export = new UserDayRollingRecordExport();
                        BeanUtils.copyProperties(detail, export);
                        export.setTimeCode(timeMap.get(detail.getTimeCode() - 1)
                                           + "-" + timeMap.get(detail.getTimeCode()));
                        export.setTradeRole(switch (detail.getTradeRole()) {
                            case "1" -> "买入";
                            case "2" -> "卖出";
                            case null, default -> "";
                        });
                        export.setStatus(switch (detail.getStatus()) {
                            case 0 -> "待执行";
                            case 1 -> "成功";
                            case 2 -> "失败";
                            case null, default -> "";
                        });
                        return export;
                    }).toList());
                }
            }
        }
        return map;
    }
}
