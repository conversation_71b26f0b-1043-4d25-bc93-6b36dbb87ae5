package cn.com.sgcc.business.sgcc.dayrolling.service;

import cn.com.sgcc.business.common.model.dto.Page;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingConfigDetail;
import cn.com.sgcc.business.sgcc.dayrolling.model.dto.UserDayRollingConfigDetailDto;
import cn.com.sgcc.business.sgcc.dayrolling.model.excel.UserDayRollingRecordExport;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * DayRollingService
 */
public interface DayRollingService {
    /**
     * queryConfigList
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @param type         type
     * @return List<UserDayRollingConfigDetail>
     */
    List<UserDayRollingConfigDetail> queryConfigList(String unitId, String strategyDate, Integer type);

    /**
     * queryRecordList
     *
     * @param unitId     unitId
     * @param type       type
     * @param objectPage objectPage
     * @return Page<List < UserDayRollingConfigDetailDto>>
     */
    Page<List<UserDayRollingConfigDetailDto>> queryRecordList(String unitId,
                                                              Integer type,
                                                              Page<List<UserDayRollingConfigDetailDto>> objectPage);

    /**
     * saveConfigList
     *
     * @param strategyDate strategyDate
     * @param declareDate  declareDate
     * @param unitIdList   unitIdList
     * @param type         type
     * @param configList   configList
     * @return Boolean
     */
    Boolean saveConfigList(String strategyDate,
                           String declareDate,
                           List<String> unitIdList,
                           Integer type,
                           List<UserDayRollingConfigDetail> configList);

    /**
     * exportRecordData
     *
     * @param unitIdList   unitIdList
     * @param strategyDate strategyDate
     * @param type         type
     * @return Map<String, Collection < UserDayRollingRecordExport>>
     */
    Map<String, Collection<UserDayRollingRecordExport>> exportRecordData(List<String> unitIdList,
                                                                         String strategyDate,
                                                                         Integer type);
}
