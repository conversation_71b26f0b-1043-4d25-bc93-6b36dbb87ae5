package cn.com.sgcc.business.sgcc.profitanalysis.repository;

import cn.com.sgcc.business.sgcc.profitanalysis.model.DataProfitAnalysis;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface DataProfitAnalysisRepo extends JpaRepository<DataProfitAnalysis, Void> {
    /**
     * findAllByParams
     *
     * @param startDate startDate
     * @param endDate   endDate
     * @return List<DataProfitAnalysis>
     */
    @Query(value = """
            select *
            from data_profit_analysis
            where date >= :startDate
              and date <= :endDate
            order by date""", nativeQuery = true)
    List<DataProfitAnalysis> findAllByParams(String startDate, String endDate);

    /**
     * findAllByParams
     *
     * @param unitId    unitId
     * @param startDate startDate
     * @param endDate   endDate
     * @return List<DataProfitAnalysis>
     */
    @Query(value = """
            select *
            from data_profit_analysis
            where unitId = :unitId
              and date >= :startDate
              and date <= :endDate
            order by date""", nativeQuery = true)
    List<DataProfitAnalysis> findAllByParams(String unitId, String startDate, String endDate);
}
