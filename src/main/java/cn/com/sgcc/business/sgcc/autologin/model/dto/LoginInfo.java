package cn.com.sgcc.business.sgcc.autologin.model.dto;

import cn.com.sgcc.business.system.model.SystemAccountConfig;
import java.util.Stack;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
public class LoginInfo {
    String plantId;
    String grayTag;
    String userName;
    String memberId;
    RespData respData;

    /**
     * LoginInfo Constructor
     *
     * @param accountConfig accountConfig
     * @param loginResp     loginResp
     */
    public LoginInfo(SystemAccountConfig accountConfig, LoginResp loginResp) {
        this.setPlantId(accountConfig.getPlantId());
        this.setGrayTag(accountConfig.getGrayTag());
        this.setUserName(accountConfig.getUserName());
        this.setRespData(new RespData(loginResp, new Stack<>()));
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RespData {
        LoginResp loginResp;
        @ToString.Exclude
        Stack<TokenData> tokenStack;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoginResp {
        Integer status;
        String message;
        String data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TokenData {
        String token;
        long validStartTime;
        long validEndTime;
    }

    /**
     * getTicket
     *
     * @return String
     */
    public String getTicket() {
        return this.respData.loginResp.getData();
    }

    /**
     * getTokenStack
     *
     * @return Stack<TokenData>
     */
    public Stack<TokenData> getTokenStack() {
        return this.respData.tokenStack;
    }
}


