package cn.com.sgcc.business.sgcc.dayrolling.service.impl;

import static cn.com.sgcc.constants.RoleConstants.Type.DAY_ROLLING;

import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.sgcc.autologin.service.LoginService;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingInfoMarket;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingInfoUnit;
import cn.com.sgcc.business.sgcc.dayrolling.repository.UserDayRollingInfoMarketRepo;
import cn.com.sgcc.business.sgcc.dayrolling.repository.UserDayRollingInfoUnitRepo;
import cn.com.sgcc.business.sgcc.dayrolling.service.DayRollingInfoService;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.constants.DateConsts;
import cn.com.sgcc.util.SgccResp;
import cn.com.sgcc.util.http.NewSgccRequest;
import cn.com.sgcc.util.http.ReqConfig;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * DayRollingInfoServiceImpl
 */
@Slf4j
@Service
public class DayRollingInfoServiceImpl implements DayRollingInfoService {
    @Resource
    UserDayRollingInfoMarketRepo infoMarketRepo;
    @Resource
    UserDayRollingInfoUnitRepo infoUnitRepo;
    @Resource
    UnitService unitService;
    @Resource
    LoginService loginService;
    @Resource
    NewSgccRequest newSgccRequest;
    @Value("${custom.dayRolling.info.httpWaitTime}")
    Long httpWaitTime;
    @Value("${custom.dayRolling.info.syncUrl}")
    String syncUrl;

    @Override
    public Map<String, String> tradeList(String date) {
        var marketList = infoMarketRepo.findByStrategyDate(date);
        var map = new HashMap<String, String>();
        marketList.forEach(market -> map.put(market.getTradeseqId(), market.getTradeseqName()));
        return map;
    }

    @Override
    public List<UserDayRollingInfoMarket> marketList(String strategyDate, String tradeseqId) {
        return infoMarketRepo.findByStrategyDateAndTradeseqId(strategyDate, tradeseqId);
    }

    @Override
    public List<UserDayRollingInfoUnit> unitList(String unitId, String strategyDate, String tradeseqId) {
        return infoUnitRepo.findByUnitIdAndStrategyDateAndTradeSeqId(unitId, strategyDate, tradeseqId);
    }

    @Override
    public String importMarketList(List<UserDayRollingInfoMarket> list) {
        if (list == null || list.isEmpty()) {
            return "数据为空";
        }
        list.forEach(info -> info.setCreateTime(LocalDateTime.now().format(DateConsts.DATE_TIME_FORMAT)));
        infoMarketRepo.saveAllAndFlush(list);
        return null;
    }

    @Override
    public String importUnitList(List<UserDayRollingInfoUnit> list) {
        if (list == null || list.isEmpty()) {
            return "数据为空";
        }
        list.forEach(info -> info.setCreateTime(LocalDateTime.now().format(DateConsts.DATE_TIME_FORMAT)));
        infoUnitRepo.saveAllAndFlush(list);
        return null;
    }

    @Override
    public void crawler(String unitId, String date) {
        var unitConfigOptional = unitService.findFirstByUnitId(DAY_ROLLING, unitId);
        if (unitConfigOptional.isEmpty()) {
            log.info("交易单元不存在: {}", unitId);
            return;
        }
        var unitConfig = unitConfigOptional.get();
        var loginInfo = loginService.login(unitId, false);
        var marketInfoList = infoMarketRepo.findByStrategyDateAndTimeCode(date, -1);
        if (marketInfoList.isEmpty()) {
            var monthDataArray = queryAuctionTrade(date, loginInfo);
            if (monthDataArray == null) {
                return;
            }
            var marketInfoToSave = new ArrayList<UserDayRollingInfoMarket>();
            monthDataArray.stream()
                          .map(o -> (JSONObject) o)
                          .forEach(dayArrayInfo -> {
                              var strategyDate = dayArrayInfo.getString("dateTime").substring(0, 10);
                              dayArrayInfo.getJSONArray("trAuctionUnitBoList").stream()
                                          .map(o -> (JSONObject) o)
                                          .forEach(obj -> {
                                              var marketInfo = new UserDayRollingInfoMarket();
                                              marketInfo.setStrategyDate(strategyDate);
                                              marketInfo.setTradeseqId(obj.getString("tradeseqId"));
                                              marketInfo.setTradeseqName(obj.getString("tradeseqName"));
                                              marketInfo.setTimeCode(-1);
                                              marketInfoToSave.add(marketInfo);
                                          });
                          });
            if (!marketInfoToSave.isEmpty()) {
                infoMarketRepo.saveAllAndFlush(marketInfoToSave);
            }
        }
        marketInfoList = infoMarketRepo.findByStrategyDateAndTimeCode(date, -1);
        marketInfoList.forEach(marketInfo -> {
            var tradeseqId = marketInfo.getTradeseqId();
            {
                // 市场交易信息
                var infoMarkets = infoMarketRepo
                        .findByStrategyDateAndTradeseqIdAndTimeCodeNot(date, tradeseqId, -1);
                if (infoMarkets.isEmpty()) {
                    var array = queryTradeStatistics(date, tradeseqId, loginInfo);
                    if (array != null) {
                        var marketToSave = array.stream()
                                                .map(o -> (JSONObject) o)
                                                .map(obj -> {
                                                    var market = new UserDayRollingInfoMarket();
                                                    market.setStrategyDate(marketInfo.getStrategyDate());
                                                    market.setTimeCode(obj.getInteger("reportType"));
                                                    market.setTradeseqId(tradeseqId);
                                                    market.setTradeseqName(marketInfo.getTradeseqName());
                                                    market.setTotal(obj.getString("periodDealAmount"));
                                                    market.setMaxPrice(obj.getString("periodMaxPrice"));
                                                    market.setMinPrice(obj.getString("periodMinPrice"));
                                                    market.setWeightedPrice(obj.getString("periodDealPrice"));
                                                    market.setMidPrice(obj.getString("periodMidPrice"));
                                                    market.setCreateTime(
                                                            LocalDateTime.now().format(DateConsts.DATE_TIME_FORMAT));
                                                    return market;
                                                }).toList();
                        infoMarketRepo.saveAllAndFlush(marketToSave);
                    }
                }
            }
            {
                // 用户交易信息
                var array = querySelfTradeStatistics(unitConfig, date, tradeseqId, loginInfo);
                if (array != null) {
                    array.stream()
                         .map(o -> (JSONObject) o)
                         .forEach(obj -> {
                             var user = new UserDayRollingInfoUnit();
                             user.setUnitId(unitConfig.getUnitId());
                             user.setStrategyDate(marketInfo.getStrategyDate());
                             user.setTimeCode(obj.getInteger("reportType"));
                             user.setTradeSeqId(tradeseqId);
                             user.setTradeRole(obj.getString("tradeRole"));
                             user.setPower(obj.getString("periodSelfDealAmount"));
                             user.setPrice(obj.getString("periodSelfDealPrice"));
                             user.setCreateTime(LocalDateTime.now().format(DateConsts.DATE_TIME_FORMAT));
                             infoUnitRepo.saveAndFlush(user);
                             var timeCode = String.valueOf(user.getTimeCode());
                             var detailArray = queryDealDetailList(unitConfig, tradeseqId, timeCode, loginInfo);
                             if (detailArray != null) {
                                 user.setDetail(detailArray.toJSONString());
                                 infoUnitRepo.saveAndFlush(user);
                             }
                         });
                }
            }
        });
    }

    @Override
    public void syncData(String unitId, String date, boolean price) {
        if (StringUtils.isBlank(syncUrl)) {
            log.info("同步地址为空");
            return;
        }
        var unitConfigOptional = unitService.findFirstByUnitId(DAY_ROLLING, unitId);
        if (unitConfigOptional.isEmpty()) {
            log.info("交易单元不存在: {}", unitId);
            return;
        }
        var loginInfo = loginService.login(unitId, false);
        var seqOutList = querySeqOut(date, loginInfo);
        if (seqOutList == null) {
            return;
        }
        var list = new ArrayList<JSONObject>();
        seqOutList.toList(JSONObject.class).forEach(seqOut -> {
            var beginDate = LocalDate.parse(seqOut.getString("beginDate"), DateConsts.SGCC_DATE_TIME_FORMAT);
            var startTime = LocalTime.parse(seqOut.getString("startTime"), DateConsts.SGCC_DATE_TIME_FORMAT);
            var endTime = LocalTime.parse(seqOut.getString("endTime"), DateConsts.SGCC_DATE_TIME_FORMAT);
            var tradeseqId = seqOut.getString("tradeseqId");
            var tradeseqName = seqOut.getString("tradeseqName");
            var tradeCycle = seqOut.getString("tradeCycle");
            var tradeStage = seqOut.getString("tradeStage");
            var tradeInfo = new JSONObject();
            tradeInfo.put("strategyDate", LocalDate.parse(date, DateConsts.DATE_FORMAT).format(DateConsts.DATE_FORMAT));
            tradeInfo.put("targetDate", beginDate);
            tradeInfo.put("tradeseqId", tradeseqId);
            tradeInfo.put("tradeseqName", tradeseqName);
            tradeInfo.put("tradeCycle", tradeCycle);
            tradeInfo.put("tradeStage", tradeStage);
            tradeInfo.put("startTime", startTime);
            tradeInfo.put("endTime", endTime);
            tradeInfo.put("type", 4);
            price:
            if (price) {
                var statisticsList = queryTradeStatistics(date, tradeseqId, loginInfo);
                if (statisticsList == null) {
                    break price;
                }
                statisticsList.toList(JSONObject.class).forEach(statistics -> {
                    var reportType = statistics.getInteger("reportType");
                    tradeInfo.put("p" + reportType, statistics.getDouble("periodDealPrice"));
                });
            }
            list.add(tradeInfo);
        });
        var body = JSONObject.of("list", list).toString();
        var resp = HttpUtil.post(syncUrl, body, 5000);
        log.info("同步数据：{}，结果：resp: {}", body, resp);
    }

    /**
     * queryAuctionTrade
     *
     * @param date      date
     * @param loginInfo loginInfo
     * @return JSONArray
     */
    private JSONArray queryAuctionTrade(String date, LoginInfo loginInfo) {
        String businessName = "爬取分时段交易月度交易列表日历";
        log.info("开始 {}, date: {}", businessName, date);
        String respStr = newSgccRequest.post("/px-trade-auction/auctionConfig/queryAuctionTrade", JSONObject.of(
                "bidDate", date
        ), ReqConfig.of(loginInfo, "/pxf-trade-auction-extranet/myTransaction/Announcement", httpWaitTime));
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var array = SgccResp.arrayData(sgccResp);
        if (array.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, array.size());
        return array;
    }

    /**
     * queryTradeStatistics
     *
     * @param date       date
     * @param tradeseqId tradeseqId
     * @param loginInfo  loginInfo
     * @return JSONArray
     */
    private JSONArray queryTradeStatistics(String date, String tradeseqId, LoginInfo loginInfo) {
        String businessName = "爬取市场日滚动交易列表";
        log.info("开始 {}, tradeseqId: {}, date: {}", businessName, tradeseqId, date);
        String respStr = newSgccRequest.post("/px-trade-auction/listing/queryTradeStatistics", JSONObject.of(
                "bidDate", date,
                "saleUnitId", "",
                "tradeCycle", "",
                "tradeseqId", tradeseqId
        ), ReqConfig.of(loginInfo, "/pxf-trade-auction-extranet/myTransaction/TradeResult", httpWaitTime));
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var array = SgccResp.arrayData(sgccResp);
        if (array.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, array.size());
        return array;
    }

    /**
     * querySelfTradeStatistics
     *
     * @param unitConfig unitConfig
     * @param date       date
     * @param tradeseqId tradeseqId
     * @param loginInfo  loginInfo
     * @return JSONArray
     */
    private JSONArray querySelfTradeStatistics(SystemUnitConfig unitConfig,
                                               String date, String tradeseqId, LoginInfo loginInfo) {
        String businessName = "爬取用户日滚动交易列表";
        var unitIdIlt = unitConfig.getUnitIdIlt();
        log.info("开始 {}, unitId: {}, tradeseqId: {}, date: {}", businessName, unitIdIlt, tradeseqId, date);
        String respStr = newSgccRequest.post("/px-trade-auction/listing/querySelfTradeStatistics", JSONObject.of(
                "bidDate", date,
                "saleUnitId", unitIdIlt,
                "tradeCycle", "",
                "tradeseqId", tradeseqId
        ), ReqConfig.of(loginInfo, "/pxf-trade-auction-extranet/myTransaction/TradeResult", httpWaitTime));
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var array = SgccResp.arrayData(sgccResp);
        if (array.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, array.size());
        return array;
    }

    /**
     * queryDealDetailList
     *
     * @param unitConfig unitConfig
     * @param tradeseqId tradeseqId
     * @param timeCode   timeCode
     * @param loginInfo  loginInfo
     * @return JSONArray
     */
    private JSONArray queryDealDetailList(SystemUnitConfig unitConfig,
                                          String tradeseqId, String timeCode, LoginInfo loginInfo) {
        String businessName = "爬取用户日滚动交易详情列表";
        var unitIdIlt = unitConfig.getUnitIdIlt();
        log.info("开始 {}, unitId: {}, tradeseqId: {}, timeCode: {}", businessName, unitIdIlt, tradeseqId, timeCode);
        String respStr = newSgccRequest.post("/px-trade-auction/listing/queryDealDetailList", JSONObject.of(
                "data", JSONObject.of(
                        "timeCodeList", JSONArray.of(timeCode),
                        "tradeseqId", tradeseqId
                ),
                "pageInfo", JSONObject.of(
                        "pageNum", 1,
                        "pageSize", 0,
                        "total", 0
                )
        ), ReqConfig.of(loginInfo, "/pxf-trade-auction-extranet/myTransaction/TradeResult", httpWaitTime));
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var object = SgccResp.jsonData(sgccResp);
        if (object.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        var array = object.getJSONArray("list");
        if (array == null || array.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, array.size());
        return array;
    }

    /**
     * querySeqOut
     *
     * @param strategyDate strategyDate
     * @param loginInfo    loginInfo
     * @return JSONArray
     */
    private JSONArray querySeqOut(String strategyDate, LoginInfo loginInfo) {
        String businessName = "查询日滚动交易列表";
        log.info("开始 {}, strategyDate: {}", businessName, strategyDate);
        var respStr = newSgccRequest.post("/px-trade-auction/auctionConfig/querySeqOut", JSONObject.of(
                "data", JSONObject.of(
                        "bidDate", strategyDate, // 标的日
                        "tradeCycle", "", // 交易周期: { 1: "多月", 2: "月度", 3: "旬", 4: "日滚动" }
                        "tradeStage", "2" // 交易类型 (1: 集中竞价, 2: 滚动撮合)
                ),
                "pageInfo", JSONObject.of()
        ), ReqConfig.of(loginInfo, "/pxf-trade-auction-extranet/myTransaction/Announcement"));
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var list = SgccResp.jsonData(sgccResp).getJSONArray("list");
        if (list == null) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        if (list.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        list = new JSONArray(list.stream()
                                 .filter(o -> "2".equals(((JSONObject) o).getString("tradeStage")))
                                 .filter(o -> List.of("3", "4").contains(((JSONObject) o).getString("tradeCycle")))
                                 .toList());
        log.info("{} 结果: {}", businessName, list);
        return list;
    }

}
