package cn.com.sgcc.business.sgcc.dayrolling.repository;

import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingConfigDetail;
import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * UserDayRollingConfigDetailRepo
 */
@Repository
public interface UserDayRollingConfigDetailRepo extends JpaRepository<UserDayRollingConfigDetail, Void> {
    /**
     * findAllByUnitIdAndType
     *
     * @param unitId unitId
     * @param type   type
     * @return List<UserDayRollingConfigDetail>
     */
    List<UserDayRollingConfigDetail> findAllByUnitIdAndType(String unitId, Integer type);

    /**
     * findAllByUnitIdAndStrategyDateAndType
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @param type         type
     * @return List<UserDayRollingConfigDetail>
     */
    List<UserDayRollingConfigDetail> findAllByUnitIdAndStrategyDateAndType(String unitId,
                                                                           String strategyDate,
                                                                           Integer type);

    /**
     * findAllByUnitIdAndStrategyDateAndDeclareDate
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @param declareDate  declareDate
     * @return List<UserDayRollingConfigDetail>
     */
    List<UserDayRollingConfigDetail> findAllByUnitIdAndStrategyDateAndDeclareDate(String unitId,
                                                                                  String strategyDate,
                                                                                  String declareDate);

    /**
     * countAllByUnitIdAndType
     *
     * @param unitId unitId
     * @param type   type
     * @return long
     */
    @Query(value = """
            select count(*)
            from (select strategyDate, declareDate
                  from user_day_rolling_config_detail
                  where unitId = :unitId
                  and type = :type
                  group by strategyDate, declareDate)
            """, nativeQuery = true)
    long countAllByUnitIdAndType(String unitId, Integer type);

    /**
     * findAllByUnitIdAndTypePage
     *
     * @param unitId unitId
     * @param type   type
     * @param limit  limit
     * @param offset offset
     * @return List<UserDayRollingConfigDetail>
     */
    @Query(value = """
            select *
            from (select *
                  from user_day_rolling_config_detail
                  where unitId = :unitId
                  and type = :type
                  group by strategyDate, declareDate)
            order by strategyDate desc, declareDate desc
            limit :limit offset :offset
            """, nativeQuery = true)
    List<UserDayRollingConfigDetail> findAllByUnitIdAndTypePage(String unitId,
                                                                Integer type,
                                                                Integer limit,
                                                                Integer offset);

    /**
     * findAllByUnitIdAndStrategyDateInAndDeclareDateInAndType
     *
     * @param unitId        unitId
     * @param strategyDates strategyDates
     * @param declareDates  declareDates
     * @param type          type
     * @return List<UserDayRollingConfigDetail>
     */
    List<UserDayRollingConfigDetail> findAllByUnitIdAndStrategyDateInAndDeclareDateInAndType(
            String unitId,
            Collection<String> strategyDates,
            Collection<String> declareDates,
            Integer type);

    /**
     * updateTradeRoleLock
     *
     * @param detail detail
     */
    @Modifying
    @Transactional
    @Query(value = """
            update user_day_rolling_config_detail
               set tradeRoleLock = :#{#detail.tradeRoleLock}
            where unitId = :#{#detail.unitId}
              and strategyDate = :#{#detail.strategyDate}
              and declareDate = :#{#detail.declareDate}
              and timeCode = :#{#detail.timeCode}
              and type = :#{#detail.type}
            """, nativeQuery = true)
    void updateTradeRoleLock(UserDayRollingConfigDetail detail);
}
