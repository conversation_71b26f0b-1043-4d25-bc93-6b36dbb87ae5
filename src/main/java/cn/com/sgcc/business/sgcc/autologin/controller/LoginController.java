package cn.com.sgcc.business.sgcc.autologin.controller;

import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.sgcc.autologin.model.dto.Cookie;
import cn.com.sgcc.business.sgcc.autologin.service.LoginService;
import cn.com.sgcc.config.ErrMsg;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录
 */
@RestController
@RequestMapping("/api/v1/login")
@CrossOrigin
@Slf4j
public class LoginController {
    @Resource
    private LoginService loginService;

    /**
     * 登录
     *
     * @param plantId 场站 ID
     * @return Result
     */
    @GetMapping("/login")
    @ErrMsg("登录失败")
    public Result<Void> login(String plantId) {
        var loginInfo = loginService.loginByPlantId(plantId, false);
        if (loginInfo == null) {
            return Result.fail("登录失败");
        }
        return Result.ok();
    }

    /**
     * getCookies
     *
     * @param userName userName
     * @return Result<List < Cookie>>
     */
    @GetMapping("/getCookies")
    @ErrMsg("获取登录信息")
    public Result<List<Cookie>> getCookies(String userName) {
        return Result.ok(loginService.getCookies(userName));
    }

    /**
     * addLogin
     *
     * @param loginInfo loginInfo
     * @return Result<Void>
     */
    @PostMapping("/addLogin")
    @ErrMsg("新增失败")
    public Result<Void> addLogin(@RequestBody JSONObject loginInfo) {
        loginService.addLogin(loginInfo);
        return Result.ok();
    }
}
