package cn.com.sgcc.business.sgcc.opid.service.impl;

import static cn.com.sgcc.constants.DateConsts.DATE_FORMAT;
import static cn.com.sgcc.constants.DateConsts.DATE_TIME_FORMAT;
import static cn.com.sgcc.constants.DateConsts.TIME_HH_MM_FORMAT;
import static cn.com.sgcc.util.TimeUtil.HOUR_TIME_PART_MAP_REV;

import cn.com.sgcc.business.common.model.dto.Page;
import cn.com.sgcc.business.sgcc.opid.model.SystemOpidTimeConfig;
import cn.com.sgcc.business.sgcc.opid.model.UserOpidConfig;
import cn.com.sgcc.business.sgcc.opid.model.UserOpidConfigDetail;
import cn.com.sgcc.business.sgcc.opid.model.dto.UserOpidConfigDto;
import cn.com.sgcc.business.sgcc.opid.model.excel.StrategyConfigExport;
import cn.com.sgcc.business.sgcc.opid.model.excel.StrategyConfigImport;
import cn.com.sgcc.business.sgcc.opid.repository.SystemOpidTimeConfigRepo;
import cn.com.sgcc.business.sgcc.opid.repository.UserOpidConfigDetailRepo;
import cn.com.sgcc.business.sgcc.opid.repository.UserOpidConfigRepo;
import cn.com.sgcc.business.sgcc.opid.service.OpidService;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.business.system.service.AccountService;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.constants.ProvConsts;
import cn.com.sgcc.constants.RoleConstants;
import cn.com.sgcc.job.service.opid.OpidReportJobContext;
import cn.com.sgcc.util.TimeUtil;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * OpidServiceImpl 实现了 OpidService 接口，提供了具体的业务逻辑实现。
 * 该类主要用于处理与 OPID 相关的配置查询、保存、导出和导入等功能。
 */
@Service
@Slf4j
public class OpidServiceImpl implements OpidService {

    @Resource
    private UnitService unitService;
    @Resource
    private AccountService accountService;
    @Resource
    private UserOpidConfigRepo configRepo;
    @Resource
    private UserOpidConfigDetailRepo configDetailRepo;
    @Resource
    private SystemOpidTimeConfigRepo timeConfigRepo;
    @Resource
    private OpidReportJobContext reportJobContext;

    @Value("${custom.pmos.provinceCode}")
    String provinceCode;

    @Override
    public List<UserOpidConfigDetail> queryConfigList(String unitId, String startDate) {
        return configDetailRepo.findAllByParams(Collections.singletonList(unitId), startDate, startDate);
    }

    @Override
    public Page<List<UserOpidConfigDto>> queryRecordList(String unitId, Page<List<UserOpidConfigDto>> page) {
        var count = configRepo.countAllByUnitId(unitId);
        if (count == 0) {
            page.setData(new ArrayList<>());
            return page;
        }
        var configDayList = configRepo
                .findAllByUnitIdPage(unitId, page.getPageSize(), (page.getPage() - 1) * page.getPageSize());
        if (configDayList.isEmpty()) {
            page.setData(new ArrayList<>());
            return page;
        }
        List<SystemOpidTimeConfig> tradeTimeConfigs = timeConfigRepo.findAll();
        var now = LocalDateTime.now();
        LocalDateTime nextSpotTime = getNextSpotTime(now, tradeTimeConfigs);
        List<String> days = configDayList.stream().map(UserOpidConfig::getStrategyDate).collect(Collectors.toList());
        List<UserOpidConfigDetail> configDetails = configDetailRepo.findAllByUnitIdAndStrategyDateIn(unitId, days);
        configDetails.forEach(detail -> {
            LocalDateTime dateTime = LocalDate.parse(detail.getStrategyDate(), DATE_FORMAT)
                                              .atTime(detail.getTimePart() * 2, 15);
            if (!dateTime.isBefore(nextSpotTime)) {
                detail.setStatus(999);
            }
        });
        Map<String, List<UserOpidConfigDetail>> configDetailMapByTime = configDetails
                .stream()
                .collect(Collectors.groupingBy(UserOpidConfigDetail::getStrategyDate));
        List<UserOpidConfigDto> list = configDayList.stream().map(UserOpidConfigDto::new).toList();
        list.forEach(it -> it.setDetails(configDetailMapByTime.get(it.getStrategyDate())));
        page.setData(list);
        page.setTotal(count);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void deleteRecordList(String unitId, String strategyDate) {
        configRepo.removeByUnitIdAndStrategyDate(unitId, strategyDate);
        configDetailRepo.removeByUnitIdAndStrategyDate(unitId, strategyDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean saveConfigList(List<String> unitIdList, Integer strategyType, Integer reportType,
                                  String startDate, String endDate,
                                  List<Integer> timePartList, List<UserOpidConfigDetail> configList) {
        var ignoredConfig = false;
        var now = LocalDateTime.now();
        var nowTime = now.format(DATE_TIME_FORMAT);
        var tradeTimeConfigs = timeConfigRepo.findAll();
        var unitConfigMap = unitService.getUnitMap(RoleConstants.Type.OPID);
        var saveConfigDetails = new ArrayList<UserOpidConfigDetail>();
        var saveConfigs = new HashSet<UserOpidConfig>();
        var startDay = LocalDate.parse(startDate, DATE_FORMAT);
        var endDay = LocalDate.parse(endDate, DATE_FORMAT);
        var nextSpotTime = getNextSpotTime(now, tradeTimeConfigs);
        while (!startDay.isAfter(endDay)) {
            var strategyDate = startDay.format(DATE_FORMAT);
            var timeParts = new ArrayList<Integer>();
            for (var configDetail : configList) {
                var timePart = configDetail.getTimePart();
                // 跳过下个可申报时段之前的配置
                if (startDay.atTime(timePart * 2, 15).isBefore(nextSpotTime)) {
                    ignoredConfig = true;
                    continue;
                }
                timeParts.add(timePart);
                for (var unitId : unitIdList) {
                    var unitConfig = unitConfigMap.get(unitId);
                    if (unitConfig.getType() == null) {
                        continue;
                    }
                    // 跳过光伏不在 08:15 - 18:00 的配置，黑龙江为06:15-18:00
                    if (ProvConsts.Prov.hlj.name().equals(provinceCode)) {
                        if (unitConfig.getType() == 1 && (timePart < 3 || timePart > 8)) {
                            ignoredConfig = true;
                            continue;
                        }
                    } else if ((timePart < 4 || timePart > 8) && unitConfig.getType() == 1) {
                        ignoredConfig = true;
                        continue;
                    }
                    var saveConfig = new UserOpidConfig();
                    saveConfig.setUnitId(unitId);
                    saveConfig.setUnitName(unitConfig.getUnitName());
                    saveConfig.setStrategyDate(strategyDate);
                    saveConfig.setStrategyType(strategyType);
                    saveConfig.setReportType(reportType);
                    saveConfig.setCreateTime(nowTime);
                    saveConfigs.add(saveConfig);
                    var saveConfigDetail = new UserOpidConfigDetail();
                    BeanUtils.copyProperties(configDetail, saveConfigDetail);
                    saveConfigDetail.setUnitId(unitId);
                    saveConfigDetail.setDispatchId(unitConfig.getDispatchId());
                    saveConfigDetail.setStrategyDate(strategyDate);
                    saveConfigDetail.setStrategyType(strategyType);
                    saveConfigDetail.setReportType(reportType);
                    saveConfigDetail.setStatus(0);
                    saveConfigDetail.setCreateTime(nowTime);
                    if (reportType == 2) {
                        saveConfigDetail.setPercent(100D);
                        saveConfigDetail.setPrice(null);
                    }
                    saveConfigDetails.add(saveConfigDetail);
                }
            }
            if (!timeParts.isEmpty()) {
                configRepo.removeByParams(unitIdList, strategyDate, strategyDate);
                configDetailRepo.removeByParams(unitIdList, strategyDate, strategyDate, timeParts);
            }
            startDay = startDay.plusDays(1);
        }
        configRepo.saveAllAndFlush(saveConfigs);
        var savedAllAndFlush = configDetailRepo.saveAllAndFlush(saveConfigDetails);
        var groupMap = savedAllAndFlush.stream().collect(Collectors.groupingBy(UserOpidConfigDetail::getUnitId));
        groupMap.forEach((unitId, detailList) -> {
            var groupMap1 = detailList.stream().collect(Collectors.groupingBy(UserOpidConfigDetail::getStrategyDate));
            groupMap1.forEach((date, list) -> {
                reportJobContext.getService().updateReportPrice(list);
            });
        });
        return ignoredConfig;
    }

    @Override
    public Map<String, Collection<StrategyConfigExport>> exportRecordData(List<String> unitIdList,
                                                                          String startDate, String endDate) {
        HashMap<String, Collection<StrategyConfigExport>> hashMap = new HashMap<>();
        Map<String, SystemUnitConfig> unitMap = unitService.getUnitMap(RoleConstants.Type.OPID);
        LocalDateTime startDay = LocalDate.parse(startDate, DATE_FORMAT).atTime(0, 0);
        LocalDateTime endDay = LocalDate.parse(endDate, DATE_FORMAT).atTime(0, 0).plusDays(1);
        List<String> allTime = TimeUtil.customTimeAxis(startDay, endDay, 60 * 15, "yyyy-MM-dd HH:mm", true, true);
        Map<String, List<UserOpidConfigDetail>> detailMapByUnit = configDetailRepo
                .findAllByParams(unitIdList, startDate, endDate)
                .stream()
                .collect(Collectors.groupingBy(UserOpidConfigDetail::getUnitId));
        for (String unitId : unitIdList) {
            SystemUnitConfig systemUnitConfig = unitMap.get(unitId);
            if (systemUnitConfig != null) {
                String unitName = systemUnitConfig.getUName();
                Map<String, UserOpidConfigDetail> map = new HashMap<>();
                for (UserOpidConfigDetail detail : detailMapByUnit.getOrDefault(unitId, new ArrayList<>())) {
                    String startTime = detail.getStartTime();
                    map.put(detail.getStrategyDate() + " " + startTime, detail);
                }
                List<StrategyConfigExport> collect = new ArrayList<>();
                for (String dateTime : allTime) {
                    UserOpidConfigDetail detail = map.get(dateTime);
                    if (detail == null) {
                        detail = new UserOpidConfigDetail();
                        detail.setStrategyDate(dateTime.substring(0, 10));
                        detail.setStartTime(dateTime.substring(11));
                    }
                    StrategyConfigExport export = new StrategyConfigExport();
                    export.setStrategyDate(detail.getStrategyDate());
                    export.setStartTime(detail.getStartTime());
                    export.setPowerLimit(detail.getPowerLimit());
                    if (detail.getPercent() != null) {
                        export.setPercent(
                                BigDecimal.valueOf(detail.getPercent())
                                          .divide(new BigDecimal(100), 3, RoundingMode.HALF_UP)
                                          .doubleValue()
                        );
                    }
                    export.setReportPower(detail.getReportPower());
                    export.setReportPrice(detail.getReportPrice());
                    collect.add(export);
                }
                hashMap.put(unitName, collect);
            }
        }
        return hashMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean importRecordData(List<String> unitIdList, Integer strategyType, Integer reportType,
                                    List<StrategyConfigImport> importList) {
        ArrayList<UserOpidConfigDetail> configList = new ArrayList<>();
        importList.sort((o1, o2) -> {
            if (o1.getStrategyDate().equals(o2.getStrategyDate())) {
                return o1.getStartTime().compareTo(o2.getStartTime());
            }
            return o1.getStrategyDate().compareTo(o2.getStrategyDate());
        });
        var map = new HashMap<Integer, Double>();
        for (StrategyConfigImport configImport : importList) {
            Double percent = configImport.getPercent();
            Double price = configImport.getPrice();
            if (percent != null && price != null) {
                percent = new BigDecimal(percent)
                        .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).doubleValue();
                price = new BigDecimal(price).setScale(0, RoundingMode.HALF_UP).doubleValue();
                if (price > 3000) {
                    price = 3000D;
                }
                UserOpidConfigDetail configDetail = new UserOpidConfigDetail();
                configDetail.setStrategyDate(configImport.getStrategyDate());
                configDetail.setStartTime(configImport.getStartTime());
                configDetail.setEndTime(configImport.getStartTime());
                var timePart = HOUR_TIME_PART_MAP_REV.get(configImport.getStartTime());
                configDetail.setTimePart(timePart);
                if (ProvConsts.Prov.hlj.name().equals(provinceCode) && map.containsKey(timePart)) {
                    price = map.get(timePart);
                } else {
                    map.put(timePart, price);
                }
                configDetail.setPercent(percent);
                configDetail.setPrice(price);
                configList.add(configDetail);
            }
        }
        boolean ignoredConfig = false;
        LocalDateTime now = LocalDateTime.now();
        List<SystemOpidTimeConfig> tradeTimeConfigs = timeConfigRepo.findAll();
        Map<String, SystemUnitConfig> unitConfigMap = unitService.getUnitMap(RoleConstants.Type.OPID);
        List<UserOpidConfigDetail> saveConfigDetails = new ArrayList<>();
        Set<UserOpidConfig> saveConfigs = new HashSet<>();
        LocalDateTime nextSpotTime = getNextSpotTime(now, tradeTimeConfigs);
        String lastDay = configList.getFirst().getStrategyDate();
        Integer lastTimePart = configList.getFirst().getTimePart();
        for (UserOpidConfigDetail configDetail : configList) {
            String day = configDetail.getStrategyDate();
            Integer timePart = configDetail.getTimePart();
            LocalDate startDay = LocalDate.parse(day, DATE_FORMAT);
            for (String unitId : unitIdList) {
                var unitType = unitConfigMap.get(unitId).getType();
                // 跳过下个可申报时段之前的配置
                if (startDay.atTime(timePart * 2, 15).isBefore(nextSpotTime)) {
                    ignoredConfig = true;
                    continue;
                }
                // 跳过光伏不在 6:02 - 14:02 的配置
                if ((timePart < 4 || timePart > 8) && unitType != null && unitType == 1) {
                    ignoredConfig = true;
                    continue;
                }
                String strategyDate = startDay.format(DATE_FORMAT);
                String nowTime = now.format(DATE_TIME_FORMAT);
                UserOpidConfig saveConfig = new UserOpidConfig();
                saveConfig.setUnitId(unitId);
                saveConfig.setUnitName(unitConfigMap.get(unitId).getUnitName());
                saveConfig.setStrategyDate(strategyDate);
                saveConfig.setStrategyType(strategyType);
                saveConfig.setReportType(reportType);
                saveConfig.setCreateTime(nowTime);
                saveConfigs.add(saveConfig);
                UserOpidConfigDetail saveConfigDetail = new UserOpidConfigDetail();
                BeanUtils.copyProperties(configDetail, saveConfigDetail);
                saveConfigDetail.setUnitId(unitId);
                saveConfigDetail.setDispatchId(unitConfigMap.get(unitId).getDispatchId());
                saveConfigDetail.setStrategyDate(strategyDate);
                saveConfigDetail.setStrategyType(strategyType);
                saveConfigDetail.setReportType(reportType);
                saveConfigDetail.setStatus(0);
                saveConfigDetail.setCreateTime(nowTime);
                if (reportType == 2) {
                    saveConfigDetail.setPercent(100D);
                    saveConfigDetail.setPrice(null);
                }
                saveConfigDetails.add(saveConfigDetail);
            }
            boolean dayChanged = !day.equals(lastDay);
            boolean timePartChanged = !Objects.equals(timePart, lastTimePart);
            if (dayChanged) {
                lastDay = day;
            }
            if (timePartChanged) {
                lastTimePart = timePart;
            }
            if (dayChanged || timePartChanged) {
                configDetailRepo.removeByParams(unitIdList, day, day, Collections.singletonList(timePart));
            }
        }
        configRepo.saveAllAndFlush(saveConfigs);
        configDetailRepo.saveAllAndFlush(saveConfigDetails);
        return ignoredConfig;
    }

    /**
     * 获取下个申报时段申报的数据的开始时间
     * <p>
     * 例如：当前 3 点，下次申报时间 04:02-04:08，可申报 06:15-08:00 时段，则此函数返回下个可申报的开始时间 06:15
     * </p>
     *
     * @param time             当前时间
     * @param tradeTimeConfigs 时间范围
     * @return 时间
     */
    private static LocalDateTime getNextSpotTime(LocalDateTime time, List<SystemOpidTimeConfig> tradeTimeConfigs) {
        String timeStr = time.format(TIME_HH_MM_FORMAT);
        tradeTimeConfigs.sort(Comparator.comparing(SystemOpidTimeConfig::getBidStartTime));
        String nextSpotTime = null;
        int dayOffset = 0;
        for (SystemOpidTimeConfig tradeTimeConfig : tradeTimeConfigs) {
            // 找到第一个超过当前时间的
            if (tradeTimeConfig.getBidStartTime().compareTo(timeStr) > 0) {
                if (tradeTimeConfig.getTradeTimePart() < 3
                        && tradeTimeConfig.getBidStartTime().compareTo("12:00") > 0) {
                    dayOffset = 1;
                }
                nextSpotTime = tradeTimeConfig.getStartPart();
                break;
            }
        }
        if (nextSpotTime == null) {
            nextSpotTime = tradeTimeConfigs.getFirst().getStartPart();
            dayOffset = 1;
        }
        return LocalDateTime.parse(time.format(DATE_FORMAT) + " " + nextSpotTime + ":00", DATE_TIME_FORMAT)
                            .plusDays(dayOffset);
    }
}
