package cn.com.sgcc.business.sgcc.profitanalysis.controller;

import cn.com.sgcc.business.sgcc.opid.model.UserOpidConfigDetail;
import cn.com.sgcc.business.sgcc.opid.repository.UserOpidConfigDetailRepo;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.config.role.RoleCondition;
import cn.com.sgcc.constants.DateConsts;
import cn.com.sgcc.constants.RoleConstants;
import cn.com.sgcc.job.service.ProfitAnalysisJobService;
import cn.com.sgcc.job.service.opid.OpidReportJobContext;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ProfitAnalysisJob {

    @Resource
    private ProfitAnalysisJobService jobService;
    @Resource
    private UnitService unitService;
    @Resource
    private OpidReportJobContext opidReportJobContext;
    @Resource
    private UserOpidConfigDetailRepo opidConfigDetailRepo;

    /**
     * job
     */
    @Scheduled(cron = "${custom.profitAnalysis.crawlerCron}")
    public void job() {
        if (!RoleCondition.hasRole(RoleConstants.Type.PROFIT_ANALYSIS)) {
            return;
        }
        log.info("爬取出清数据任务开始");
        var unitList = unitService.getUnitList(RoleConstants.Type.PROFIT_ANALYSIS);
        var yesterday = LocalDate.now().plusDays(-1).format(DateConsts.DATE_FORMAT);
        for (SystemUnitConfig unitConfig : unitList) {
            try {
                jobService.crawler(unitConfig.getUnitId(), yesterday, false);
            } catch (Exception e) {
                log.warn("爬取失败: {}", unitConfig.getUnitId());
            }
        }
        log.info("爬取出清数据任务结束");
    }

    /**
     * 爬取次日数据，只需要日前省内电价，仅限山西，自动策略，填充电价，限额100%
     */
    @Scheduled(cron = "${custom.profitAnalysis.crawlerCron2}")
    public void job2() {
        if (!RoleCondition.hasRole(RoleConstants.Type.OPID)) {
            return;
        }
        log.info("爬取出清数据2任务开始");
        var unitList = unitService.getUnitList(RoleConstants.Type.OPID);
        var tomorrow = LocalDate.now().plusDays(1).format(DateConsts.DATE_FORMAT);
        for (SystemUnitConfig unitConfig : unitList) {
            try {
                jobService.crawler(unitConfig.getUnitId(), tomorrow, true);
                var allNotSuccess = opidConfigDetailRepo.findAllNotSuccess(tomorrow, "00:15", "24:00");
                var groupMap = allNotSuccess.stream().collect(Collectors.groupingBy(UserOpidConfigDetail::getUnitId));
                groupMap.forEach((unitId, detailList) -> {
                    opidReportJobContext.getService().updateReportPrice(detailList);
                });
            } catch (Exception e) {
                log.warn("爬取失败: {}", unitConfig.getUnitId());
            }
        }
        log.info("爬取出清数据2任务结束");
    }
}
