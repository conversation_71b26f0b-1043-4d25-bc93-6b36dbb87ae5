package cn.com.sgcc.business.sgcc.imqpnewenergytrade.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Table(name = "user_imqp_new_energy_trade_config")
@IdClass(ImqpNewEnergyTradeConfig.PrimaryKey.class)
public class ImqpNewEnergyTradeConfig implements Serializable {
    @Id
    @Column
    String unitId;
    @Id
    @Column
    String strategyDate;
    @Id
    @Column
    String declareDate;
    @Column
    String trid;
    @Column
    String jydyid;
    @Column
    String startTime;
    @Column
    String endTime;
    @Column
    String startTimeAm;
    @Column
    String endTimeAm;
    @Column
    String startTimePm;
    @Column
    String endTimePm;
    @Column
    Double zcPower;
    @Column
    Double jcPower;
    @Column
    Double yfPower;
    @Column
    String tradeRole;
    @Column
    Double power;
    @Column
    Integer status;
    @Column
    String logId;
    @Column
    String createTime;
    @Column
    String updateTime;
    @Transient
    private List<ImqpNewEnergyTradeConfigLog> details;

    /**
     * getLockKey
     *
     * @return String
     */
    public String getLockKey() {
        return ImqpNewEnergyTradeConfig.class + "_" + unitId + strategyDate + declareDate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrimaryKey implements Serializable {
        String unitId;
        String strategyDate;
        String declareDate;
    }
}
