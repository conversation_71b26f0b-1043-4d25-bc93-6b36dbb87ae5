package cn.com.sgcc.business.sgcc.imqpnewenergytrade.service;

import static cn.com.sgcc.constants.RoleConstants.Type.IMQP_NEW_ENERGY_TRADE;

import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.sgcc.autologin.service.LoginService;
import cn.com.sgcc.business.sgcc.imqpnewenergytrade.model.ImqpNewEnergyTradeConfig;
import cn.com.sgcc.business.sgcc.imqpnewenergytrade.model.ImqpNewEnergyTradeConfigLog;
import cn.com.sgcc.business.sgcc.imqpnewenergytrade.repository.ImqpNewEnergyTradeConfigLogRepo;
import cn.com.sgcc.business.sgcc.imqpnewenergytrade.repository.ImqpNewEnergyTradeConfigRepo;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.constants.DateConsts;
import cn.com.sgcc.constants.IdConsts;
import cn.com.sgcc.job.service.PmosTimeService;
import cn.com.sgcc.util.LockUtil;
import cn.com.sgcc.util.SgccJxResp;
import cn.com.sgcc.util.SgccRequest;
import cn.com.sgcc.util.SgccResp;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;

/**
 * ImqpNewEnergyTradeJobService
 */
@Slf4j
@Service
public class ImqpNewEnergyTradeJobService {

    @Resource
    private SgccRequest sgccRequest;
    @Resource
    private UnitService unitService;
    @Resource
    private LoginService loginService;
    @Resource
    private ImqpNewEnergyTradeConfigRepo repo;
    @Resource
    private ImqpNewEnergyTradeConfigLogRepo logRepo;
    @Resource
    private TaskScheduler taskScheduler;
    @Resource
    private PmosTimeService pmosTimeService;
    @Value("${custom.imqpNewEnergyTrade.preparationTimeInSecond}")
    int preparationTimeInSecond;
    @Value("${custom.imqpNewEnergyTrade.httpWaitMilliSecond}")
    long httpWaitMilliSecond;
    @Value("${custom.imqpNewEnergyTrade.textMatch}")
    String textMatch;

    /**
     * updateTridList
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @param declareDate  declareDate
     */
    public void updateTridList(String unitId, String strategyDate, String declareDate) {
        var isUpdate = unitId != null;
        List<SystemUnitConfig> unitList;
        if (isUpdate) {
            unitList = new ArrayList<>(2);
            var unitConfigOptional = unitService.findFirstByUnitId(IMQP_NEW_ENERGY_TRADE, unitId);
            unitConfigOptional.ifPresent(unitList::add);
        } else {
            unitList = unitService.getUnitList(IMQP_NEW_ENERGY_TRADE);
        }
        unitList.forEach(unitConfig -> {
            var configList = repo.findAllByUnitIdAndStrategyDate(unitConfig.getUnitId(), strategyDate);
            var configMap = configList.stream()
                                      .collect(Collectors.toMap(
                                          ImqpNewEnergyTradeConfig::getDeclareDate, Function.identity()));
            var loginInfo = loginService.login(unitConfig.getUnitId(), false);
            if (loginInfo == null) {
                return;
            }
            var memberInfo = queryCurrentMemberInfo(loginInfo);
            if (memberInfo == null) {
                return;
            }
            loginInfo.setMemberId(memberInfo.getString("membersId"));
            var detectDateList = new ArrayList<String>();
            if (isUpdate) {
                detectDateList.add(declareDate);
            } else {
                var now = LocalDate.now();
                for (int dayInMonth = now.getDayOfMonth(); dayInMonth <= now.lengthOfMonth(); dayInMonth++) {
                    var detectDate = now.withDayOfMonth(dayInMonth);
                    var detectDateStr = detectDate.format(DateConsts.DATE_FORMAT);
                    detectDateList.add(detectDateStr);
                }
            }
            for (String detectDateStr : detectDateList) {
                var config = configMap.remove(detectDateStr);
                if (config != null && !isUpdate) {
                    continue;
                }
                var tridList = getTridList(detectDateStr, loginInfo);
                if (tridList == null) {
                    continue;
                }
                var tridOptional = tridList.stream()
                                           .map(o -> (JSONObject) o)
                                           .filter(trid -> trid.getString("TRNAME").matches(textMatch))
                                           .findFirst();
                if (tridOptional.isEmpty()) {
                    log.info("没有保量保价新能源合同转让交易, unitId: {}, date: {}", unitConfig.getUnitId(), detectDateStr);
                    continue;
                }
                var trid = tridOptional.get();
                var startDate = LocalDateTime.parse(trid.getString("STAR_DATE"), DateConsts.JX_DATE_TIME_FORMAT)
                                             .format(DateConsts.DATE_TIME_FORMAT);
                var endDate = LocalDateTime.parse(trid.getString("END_DATE"), DateConsts.JX_DATE_TIME_FORMAT)
                                           .format(DateConsts.DATE_TIME_FORMAT);
                var nowDateTimeStr = LocalDateTime.now().format(DateConsts.DATE_TIME_FORMAT);
                if (config == null) {
                    config = new ImqpNewEnergyTradeConfig();
                    config.setLogId(IdConsts.SNOWFLAKE.nextIdStr());
                    config.setStatus(0);
                    config.setCreateTime(nowDateTimeStr);
                }
                config.setUnitId(unitConfig.getUnitId());
                config.setStrategyDate(strategyDate);
                config.setDeclareDate(detectDateStr);
                config.setStartTime(startDate);
                config.setEndTime(endDate);
                config.setUpdateTime(nowDateTimeStr);
                if (isUpdate) {
                    var configLogOptional = logRepo.findById(config.getLogId());
                    var configLog = configLogOptional.orElseGet(() -> {
                        var tradeConfigLog = new ImqpNewEnergyTradeConfigLog();
                        tradeConfigLog.setCreateTime(nowDateTimeStr);
                        return tradeConfigLog;
                    });
                    BeanUtils.copyProperties(config, configLog, "createTime");
                    logRepo.saveAndFlush(configLog);
                } else {
                    config.setCreateTime(nowDateTimeStr);
                    var configLog = new ImqpNewEnergyTradeConfigLog();
                    BeanUtils.copyProperties(config, configLog);
                    logRepo.saveAndFlush(configLog);
                }
                repo.saveAndFlush(config);
            }
        });
    }

    /**
     * detectJob
     *
     * @param energyTradeConfig energyTradeConfig
     */
    public void detectJob(ImqpNewEnergyTradeConfig energyTradeConfig) {
        List<ImqpNewEnergyTradeConfig> configList;
        if (energyTradeConfig == null) {
            var now = LocalDate.now();
            var strategyDate = now.format(DateConsts.MONTH_FORMAT);
            var declareDate = now.format(DateConsts.DATE_FORMAT);
            configList = repo.findAllByStrategyDateAndDeclareDate(strategyDate, declareDate);
        } else {
            configList = List.of(energyTradeConfig);
        }
        if (configList.isEmpty()) {
            return;
        }
        configList.forEach(tradeConfig -> {
            var configOptional = repo.findByUnitIdAndStrategyDateAndDeclareDate(tradeConfig.getUnitId(),
                tradeConfig.getStrategyDate(),
                tradeConfig.getDeclareDate());
            if (configOptional.isEmpty()) {
                return;
            }
            var config = configOptional.get();
            log.info("开始检测申报开始时间: {}", JSONObject.toJSONString(config));
            var now = LocalDateTime.now();
            if (config.getStartTimeAm() != null) {
                var startTimeAm = LocalDateTime.parse(config.getStartTimeAm(), DateConsts.DATE_TIME_FORMAT)
                                               .minusSeconds(pmosTimeService.getPmosTimeDiff());
                var endTimeAm = LocalDateTime.parse(config.getEndTimeAm(), DateConsts.DATE_TIME_FORMAT)
                                             .minusSeconds(pmosTimeService.getPmosTimeDiff());
                var startTimePm = LocalDateTime.parse(config.getStartTimePm(), DateConsts.DATE_TIME_FORMAT)
                                               .minusSeconds(pmosTimeService.getPmosTimeDiff());
                var endTimePm = LocalDateTime.parse(config.getEndTimePm(), DateConsts.DATE_TIME_FORMAT)
                                             .minusSeconds(pmosTimeService.getPmosTimeDiff());
                if (now.isBefore(endTimeAm)) {
                    log.info("创建定时任务: {}", startTimeAm.minusSeconds(preparationTimeInSecond));
                    taskScheduler.schedule(
                        () -> prepareToDeclare(tradeConfig, startTimeAm),
                        startTimeAm.minusSeconds(preparationTimeInSecond)
                                   .toInstant(ZoneOffset.ofHours(8)));
                    log.info("创建定时任务: {}", startTimePm.minusSeconds(preparationTimeInSecond));
                    taskScheduler.schedule(
                        () -> prepareToDeclare(tradeConfig, startTimePm),
                        startTimePm.minusSeconds(preparationTimeInSecond)
                                   .toInstant(ZoneOffset.ofHours(8)));
                } else if (now.isBefore(endTimePm)) {
                    log.info("创建定时任务: {}", startTimePm.minusSeconds(preparationTimeInSecond));
                    taskScheduler.schedule(
                        () -> prepareToDeclare(tradeConfig, startTimePm),
                        startTimePm.minusSeconds(preparationTimeInSecond)
                                   .toInstant(ZoneOffset.ofHours(8)));
                } else {
                    log.info("申报已结束");
                }
            } else if (config.getStartTime() != null) {
                var startTime = LocalDateTime.parse(config.getStartTime(), DateConsts.DATE_TIME_FORMAT)
                                             .minusSeconds(pmosTimeService.getPmosTimeDiff());
                var endTime = LocalDateTime.parse(config.getEndTime(), DateConsts.DATE_TIME_FORMAT)
                                           .minusSeconds(pmosTimeService.getPmosTimeDiff());
                if (now.isBefore(endTime)) {
                    log.info("创建定时任务: {}", startTime.minusSeconds(preparationTimeInSecond));
                    taskScheduler.schedule(() -> prepareToDeclare(tradeConfig, startTime), startTime
                        .minusSeconds(preparationTimeInSecond)
                        .toInstant(ZoneOffset.ofHours(8)));
                } else {
                    log.info("申报已结束");
                }
            } else {
                log.info("没有开始时间");
            }
        });
    }

    /**
     * prepareToDeclare
     *
     * @param tradeConfig tradeConfig
     * @param startTime   startTime
     */
    public void prepareToDeclare(ImqpNewEnergyTradeConfig tradeConfig, LocalDateTime startTime) {
        var loginInfo = loginService.login(tradeConfig.getUnitId(), false);
        if (loginInfo == null) {
            return;
        }
        taskScheduler.schedule(() -> declare(tradeConfig), startTime.toInstant(ZoneOffset.ofHours(8)));
    }

    /**
     * declare
     *
     * @param tradeConfig tradeConfig
     */
    public void declare(ImqpNewEnergyTradeConfig tradeConfig) {
        LockUtil.lock(tradeConfig.getLockKey(), k -> {
            var now = LocalDateTime.now();
            var sgccNow = pmosTimeService.getCurrentPmosTime();
            var configOptional = repo.findByUnitIdAndStrategyDateAndDeclareDate(tradeConfig.getUnitId(),
                tradeConfig.getStrategyDate(),
                tradeConfig.getDeclareDate());
            if (configOptional.isEmpty()) {
                return;
            }
            var config = configOptional.get();
            var configLogOptional = logRepo.findById(config.getLogId());
            var configLog = configLogOptional.orElseGet(() -> {
                var newLog = new ImqpNewEnergyTradeConfigLog();
                BeanUtils.copyProperties(config, newLog);
                newLog.setCreateTime(now.format(DateConsts.DATE_TIME_FORMAT));
                newLog.setUpdateTime(now.format(DateConsts.DATE_TIME_FORMAT));
                return newLog;
            });
            var unitId = config.getUnitId();
            if (config.getStatus() != 0) {
                log.info("已经申报过了: {}", unitId);
                return;
            }
            var startTime = LocalDateTime.parse(config.getStartTime(), DateConsts.DATE_TIME_FORMAT);
            var endTime = LocalDateTime.parse(config.getEndTime(), DateConsts.DATE_TIME_FORMAT);
            if (!(sgccNow.isAfter(startTime) && sgccNow.isBefore(endTime))) {
                log.info("不在申报时间范围内");
                return;
            }
            if (config.getStartTimeAm() != null) {
                var startTimeAm = LocalDateTime.parse(config.getStartTimeAm(), DateConsts.DATE_TIME_FORMAT);
                var endTimeAm = LocalDateTime.parse(config.getEndTimeAm(), DateConsts.DATE_TIME_FORMAT);
                var startTimePm = LocalDateTime.parse(config.getStartTimePm(), DateConsts.DATE_TIME_FORMAT);
                var endTimePm = LocalDateTime.parse(config.getEndTimePm(), DateConsts.DATE_TIME_FORMAT);
                if (!((sgccNow.isAfter(startTimeAm) && sgccNow.isBefore(endTimeAm))
                      || (sgccNow.isAfter(startTimePm) && sgccNow.isBefore(endTimePm)))) {
                    log.info("不在申报时间范围内");
                    return;
                }
            }
            var loginInfo = loginService.login(unitId, false);
            if (loginInfo == null) {
                return;
            }
            var memberInfo = queryCurrentMemberInfo(loginInfo);
            if (memberInfo == null) {
                return;
            }
            loginInfo.setMemberId(memberInfo.getString("membersId"));
            var initTrid = initTrid(loginInfo);
            if (initTrid == null) {
                return;
            }
            config.setTrid(initTrid.getString("TRID"));
            var swSTime = LocalDateTime
                .parse(initTrid.getString("SW_S_TIME"), DateConsts.SGCC_DATE_TIME_FORMAT_UTC).plusHours(8);
            var swETime = LocalDateTime
                .parse(initTrid.getString("SW_E_TIME"), DateConsts.SGCC_DATE_TIME_FORMAT_UTC).plusHours(8);
            var xwSTime = LocalDateTime
                .parse(initTrid.getString("XW_S_TIME"), DateConsts.SGCC_DATE_TIME_FORMAT_UTC).plusHours(8);
            var xwETime = LocalDateTime
                .parse(initTrid.getString("XW_E_TIME"), DateConsts.SGCC_DATE_TIME_FORMAT_UTC).plusHours(8);
            config.setStartTimeAm(swSTime.format(DateConsts.DATE_TIME_FORMAT));
            config.setEndTimeAm(swETime.format(DateConsts.DATE_TIME_FORMAT));
            config.setStartTimePm(xwSTime.format(DateConsts.DATE_TIME_FORMAT));
            config.setEndTimePm(xwETime.format(DateConsts.DATE_TIME_FORMAT));
            repo.saveAndFlush(config);
            var startTimeAm = LocalDateTime.parse(config.getStartTimeAm(), DateConsts.DATE_TIME_FORMAT);
            var endTimeAm = LocalDateTime.parse(config.getEndTimeAm(), DateConsts.DATE_TIME_FORMAT);
            var startTimePm = LocalDateTime.parse(config.getStartTimePm(), DateConsts.DATE_TIME_FORMAT);
            var endTimePm = LocalDateTime.parse(config.getEndTimePm(), DateConsts.DATE_TIME_FORMAT);
            if (!((sgccNow.isAfter(startTimeAm) && sgccNow.isBefore(endTimeAm))
                  || (sgccNow.isAfter(startTimePm) && sgccNow.isBefore(endTimePm)))) {
                log.info("不在申报时间范围内");
                return;
            }
            var initjydy = initjydy(config, loginInfo);
            if (initjydy == null) {
                return;
            }
            config.setJydyid(initjydy.getString("JYDYID"));
            repo.saveAndFlush(config);
            var checkInterval = checkInterval(config, loginInfo);
            if (checkInterval == null) {
                return;
            }
            var sydl = sydl(config, loginInfo);
            if (sydl == null) {
                return;
            }
            config.setJcPower(sydl.getDouble("jcdl"));
            config.setZcPower(sydl.getDouble("zcdl"));
            config.setYfPower(sydl.getDouble("yfdl"));
            repo.saveAndFlush(config);
            configLog.setJcPower(config.getJcPower());
            configLog.setZcPower(config.getZcPower());
            configLog.setYfPower(config.getYfPower());
            logRepo.saveAndFlush(configLog);
            if (config.getPower() == null || config.getPower().intValue() == 0) {
                log.info("没有电量数据: {}", unitId);
                return;
            }
            if ("1".equals(config.getTradeRole()) && config.getZcPower() < config.getPower()) {
                log.warn("增持可挂牌剩余电量不足: {} < {}", config.getPower(), config.getZcPower());
                return;
            } else if ("2".equals(config.getTradeRole()) && config.getJcPower() < config.getPower()) {
                log.warn("减持可挂牌剩余电量不足: {} < {}", config.getPower(), config.getJcPower());
                return;
            }
            var gpcz = gpcz(config, loginInfo);
            config.setStatus(gpcz == null ? 2 : 1);
            config.setUpdateTime(now.format(DateConsts.DATE_TIME_FORMAT));
            configLog.setStatus(config.getStatus());
            repo.saveAndFlush(config);
            configLog.setUpdateTime(now.format(DateConsts.DATE_TIME_FORMAT));
            logRepo.saveAndFlush(configLog);
        });
    }

    /**
     * queryCurrentMemberInfo
     *
     * @param loginInfo loginInfo
     * @return JSONObject
     */
    public JSONObject queryCurrentMemberInfo(LoginInfo loginInfo) {
        String businessName = "查询用户信息";
        log.info("开始 {}, userName: {}", businessName, loginInfo.getUserName());
        String respStr;
        try (var response = sgccRequest
            .getPostRequest("/px-service-omemberregister/mmGeneratorInfo/queryCurrentMemberInfo",
                "{}", loginInfo, httpWaitMilliSecond)
            .header("Currentroute", "/pxf-service-omarketservice/currentInfoRouter/generatorInfo")
            .execute()) {
            respStr = response.body();
        }
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var data = SgccResp.jsonData(sgccResp);
        log.info("{} 结果: {}", businessName, data);
        return data;
    }

    /**
     * getTridList
     *
     * @param date      date
     * @param loginInfo loginInfo
     * @return JSONArray
     */
    public JSONArray getTridList(String date, LoginInfo loginInfo) {
        String businessName = "查询" + date + "保量保价新能源合同转让信息";
        log.info("开始 {}, userName: {}", businessName, loginInfo.getUserName());
        String respStr;
        try (var response = sgccRequest
            .getPostFormRequest("/px-jx-trade-out/jyrl/getTridList?memberId=%s&userName=%s"
                    .formatted(loginInfo.getMemberId(), loginInfo.getUserName()),
                "jy_time=" + date, loginInfo, httpWaitMilliSecond)
            .header("Referer", "https://pmos.jx.sgcc.com.cn/pxf-jx-trade-out/")
            .execute()) {
            respStr = response.body();
        }
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccJxResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var array = SgccJxResp.arrayData(sgccResp);
        if (array.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, array);
        return array;
    }

    /**
     * initTrid
     *
     * @param loginInfo loginInfo
     * @return JSONObject
     */
    public JSONObject initTrid(LoginInfo loginInfo) {
        String businessName = "查询当前申报信息";
        log.info("开始 {}, userName: {}", businessName, loginInfo.getUserName());
        String respStr;
        try (var response = sgccRequest
            .getPostFormRequest("/px-jx-trade-out/d3/d3DayXNYController/initTrid?memberId=%s&userName=%s"
                    .formatted(loginInfo.getMemberId(), loginInfo.getUserName()),
                "", loginInfo, httpWaitMilliSecond)
            .header("Referer", "https://pmos.jx.sgcc.com.cn/pxf-jx-trade-out/")
            .execute()) {
            respStr = response.body();
        }
        var data = JSONObject.parseObject(respStr);
        if (data == null || data.getString("TRID") == null) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, data);
        return data;
    }

    /**
     * initjydy
     *
     * @param config    config
     * @param loginInfo loginInfo
     * @return JSONObject
     */
    public JSONObject initjydy(ImqpNewEnergyTradeConfig config, LoginInfo loginInfo) {
        String businessName = "查询交易单元ID";
        log.info("开始 {}, userName: {}", businessName, loginInfo.getUserName());
        String respStr;
        try (var response = sgccRequest
            .getPostFormRequest("/px-jx-trade-out/d3/d3DayXNYController/initjydy?memberId=%s&userName=%s"
                    .formatted(loginInfo.getMemberId(), loginInfo.getUserName()),
                "trid=%s&jy_time=%s".formatted(config.getTrid(), config.getDeclareDate()),
                loginInfo, httpWaitMilliSecond)
            .header("Referer", "https://pmos.jx.sgcc.com.cn/pxf-jx-trade-out/")
            .execute()) {
            respStr = response.body();
        }
        var array = JSONArray.parseArray(respStr);
        if (array == null || array.isEmpty()) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var data = array.getJSONObject(0);
        log.info("{} 结果: {}", businessName, data);
        return data;
    }

    /**
     * sydl
     *
     * @param config    config
     * @param loginInfo loginInfo
     * @return JSONObject
     */
    public JSONObject sydl(ImqpNewEnergyTradeConfig config, LoginInfo loginInfo) {
        String businessName = "查询电量信息";
        log.info("开始 {}, userName: {}", businessName, loginInfo.getUserName());
        String respStr;
        try (var response = sgccRequest
            .getPostFormRequest("/px-jx-trade-out/d3/d3DayXNYController/sydl?memberId=%s&userName=%s"
                    .formatted(loginInfo.getMemberId(), loginInfo.getUserName()),
                "trid=%s&jy_time=%s&jydyid=%s"
                    .formatted(config.getTrid(), config.getDeclareDate(), config.getJydyid()),
                loginInfo, httpWaitMilliSecond)
            .header("Referer", "https://pmos.jx.sgcc.com.cn/pxf-jx-trade-out/")
            .execute()) {
            respStr = response.body();
        }
        var data = JSONObject.parseObject(respStr);
        if (data == null || data.getDouble("jcdl") == null) {
            log.error("{} 无数据: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, data);
        return data;
    }

    /**
     * checkInterval
     *
     * @param config    config
     * @param loginInfo loginInfo
     * @return JSONObject
     */
    public JSONObject checkInterval(ImqpNewEnergyTradeConfig config, LoginInfo loginInfo) {
        String businessName = "查询当前申报状态";
        log.info("开始 {}, userName: {}", businessName, loginInfo.getUserName());
        String respStr;
        try (var response = sgccRequest
            .getPostFormRequest("/px-jx-trade-out/d3/d3DayXNYController/check_interval?memberId=%s&userName=%s"
                    .formatted(loginInfo.getMemberId(), loginInfo.getUserName()),
                "trid=%s&jy_time=%s".formatted(config.getTrid(), config.getDeclareDate()),
                loginInfo, httpWaitMilliSecond)
            .header("Referer", "https://pmos.jx.sgcc.com.cn/pxf-jx-trade-out/")
            .execute()) {
            respStr = response.body();
        }
        var data = JSONObject.parseObject(respStr);
        if (data == null || !"1".equals(data.getString("msg"))) {
            log.error("{} 未开始: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, data);
        return data;
    }

    /**
     * gpcz
     *
     * @param config    config
     * @param loginInfo loginInfo
     * @return JSONObject
     */
    public JSONObject gpcz(ImqpNewEnergyTradeConfig config, LoginInfo loginInfo) {
        String businessName = "提交挂牌请求";
        log.info("开始 {}, userName: {}", businessName, loginInfo.getUserName());
        String respStr;
        try (var response = sgccRequest
            .getPostFormRequest("/px-jx-trade-out/d3/d3DayXNYController/gp_cz?memberId=%s&userName=%s"
                    .formatted(loginInfo.getMemberId(), loginInfo.getUserName()),
                "energy=%s&role=%s&trid=%s&jy_time=%s&jydyid=%s&type=1&jy_type=1".formatted(
                    String.valueOf("1".equals(config.getTradeRole())
                                   ? config.getPower().intValue()
                                   : -config.getPower().intValue()),
                    config.getTradeRole(),
                    config.getTrid(),
                    config.getDeclareDate(),
                    config.getJydyid()
                ), loginInfo, httpWaitMilliSecond)
            .header("Referer", "https://pmos.jx.sgcc.com.cn/pxf-jx-trade-out/")
            .execute()) {
            respStr = response.body();
        }
        var data = JSONObject.parseObject(respStr);
        if (data == null || !"挂牌成功".equals(data.getString("msg"))) {
            log.error("{} 提交失败: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, data);
        return data;
    }
}
