package cn.com.sgcc.business.sgcc.dayrolling.repository;

import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingInfoMarket;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * UserDayRollingInfoMarketRepo
 */
@Repository
public interface UserDayRollingInfoMarketRepo extends JpaRepository<UserDayRollingInfoMarket, Void> {
    /**
     * findByStrategyDate
     *
     * @param strategyDate strategyDate
     * @return List<UserDayRollingInfoMarket>
     */
    List<UserDayRollingInfoMarket> findByStrategyDate(String strategyDate);

    /**
     * findByStrategyDateAndTradeseqId
     *
     * @param strategyDate strategyDate
     * @param tradeseqId   tradeseqId
     * @return List<UserDayRollingInfoMarket>
     */
    List<UserDayRollingInfoMarket> findByStrategyDateAndTradeseqId(String strategyDate, String tradeseqId);

    /**
     * findByStrategyDateAndTimeCode
     *
     * @param strategyDate strategyDate
     * @param timeCode     timeCode
     * @return List<UserDayRollingInfoMarket>
     */
    List<UserDayRollingInfoMarket> findByStrategyDateAndTimeCode(String strategyDate, Integer timeCode);

    /**
     * findByStrategyDateAndTradeseqIdAndTimeCodeNot
     *
     * @param strategyDate strategyDate
     * @param tradeseqId   tradeseqId
     * @param timeCode     timeCode
     * @return List<UserDayRollingInfoMarket>
     */
    List<UserDayRollingInfoMarket> findByStrategyDateAndTradeseqIdAndTimeCodeNot(String strategyDate,
                                                                                 String tradeseqId,
                                                                                 Integer timeCode);
}
