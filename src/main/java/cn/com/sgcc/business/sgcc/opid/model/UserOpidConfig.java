package cn.com.sgcc.business.sgcc.opid.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.Data;

@Data
@Entity
@Table(name = "user_opid_config")
@IdClass(UserOpidConfig.PrimaryKey.class)
public class UserOpidConfig implements Serializable {
    @Id
    @Column
    private String unitId;
    @Id
    @Column
    private String strategyDate;
    @Column
    private Integer strategyType;
    @Column
    private Integer reportType;
    @Column
    private String unitName;
    @Column
    private String createTime;

    @Data
    static class PrimaryKey implements Serializable {
        private String unitId;
        private String strategyDate;
    }
}
