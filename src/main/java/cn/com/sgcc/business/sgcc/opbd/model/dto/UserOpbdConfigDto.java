package cn.com.sgcc.business.sgcc.opbd.model.dto;

import cn.com.sgcc.business.sgcc.opbd.model.UserOpbdConfig;
import cn.com.sgcc.business.sgcc.opbd.model.UserOpbdConfigDetail;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * UserOpbdConfigDto
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class UserOpbdConfigDto extends UserOpbdConfig implements Serializable {
    private List<UserOpbdConfigDetail> details;
    private Boolean disabled;

    /**
     * UserOpbdConfigDto Constructor
     *
     * @param config config
     */
    public UserOpbdConfigDto(UserOpbdConfig config) {
        BeanUtils.copyProperties(config, this);
    }
}
