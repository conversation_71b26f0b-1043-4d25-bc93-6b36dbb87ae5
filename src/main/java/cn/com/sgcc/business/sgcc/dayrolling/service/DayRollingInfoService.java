package cn.com.sgcc.business.sgcc.dayrolling.service;

import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingInfoMarket;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingInfoUnit;
import java.util.List;
import java.util.Map;

/**
 * DayRollingInfoService
 */
public interface DayRollingInfoService {
    /**
     * tradeList
     *
     * @param date date
     * @return Map<String, String>
     */
    Map<String, String> tradeList(String date);

    /**
     * unitList
     *
     * @param unitId       unitId
     * @param strategyDate strategyDate
     * @param tradeseqId   tradeseqId
     * @return List<UserDayRollingInfoUnit>
     */
    List<UserDayRollingInfoUnit> unitList(String unitId, String strategyDate, String tradeseqId);

    /**
     * marketList
     *
     * @param strategyDate strategyDate
     * @param tradeseqId   tradeseqId
     * @return List<UserDayRollingInfoMarket>
     */
    List<UserDayRollingInfoMarket> marketList(String strategyDate, String tradeseqId);

    /**
     * importMarketList
     *
     * @param list list
     * @return String
     */
    String importMarketList(List<UserDayRollingInfoMarket> list);

    /**
     * importUnitList
     *
     * @param list list
     * @return String
     */
    String importUnitList(List<UserDayRollingInfoUnit> list);

    /**
     * crawler
     *
     * @param unitId unitId
     * @param date   date
     */
    void crawler(String unitId, String date);

    /**
     * syncData
     *
     * @param unitId unitId
     * @param date   date
     * @param price  price
     */
    void syncData(String unitId, String date, boolean price);

}
