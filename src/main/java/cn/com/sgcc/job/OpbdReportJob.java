package cn.com.sgcc.job;

import cn.com.sgcc.config.role.HasRole;
import cn.com.sgcc.config.role.RoleCondition;
import cn.com.sgcc.constants.RoleConstants;
import cn.com.sgcc.job.service.opbd.OpbdReportJobContext;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Conditional;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * OpbdReportJob
 */
@Conditional({RoleCondition.class})
@HasRole(RoleConstants.Type.OPBD)
@Slf4j
@Component
@RestController
@RequestMapping("/job/opbd")
public class OpbdReportJob {

    @Resource
    private OpbdReportJobContext context;

    @Value("${custom.pmos.provinceCode}")
    String provinceCode;

    /**
     * job
     */
    @RequestMapping("/job")
    @Scheduled(cron = "${custom.opbd.startCron}")
    public void job() {
        if (!RoleCondition.hasRole(RoleConstants.Type.OPBD)) {
            return;
        }
        log.info("获取省间日前申报开始时间任务开始");
        context.getService(provinceCode).detect();
        log.info("获取省间日前申报开始时间任务结束");
    }

}
