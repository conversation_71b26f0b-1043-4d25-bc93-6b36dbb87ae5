package cn.com.sgcc.job.service.opbd;

import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.constants.ProvConsts;
import com.alibaba.fastjson2.JSONObject;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OpbdReportJobServiceHlj extends OpbdReportJobServiceDefault {

    @Override
    public List<String> getSupportedProv() {
        return List.of(ProvConsts.Prov.hlj.name());
    }

    @Override
    public boolean checkBeforeSchedule(String strategyDate, LocalDateTime declareDateTime) {
        return super.checkBeforeSchedule(strategyDate, declareDateTime);
    }

    @Override
    public List<String> getReportDays(LoginInfo loginInfo) {
        return queryReportableTime(loginInfo);
    }

    @Override
    public void agreeConfirmation(String strategyDate, LoginInfo loginInfo) {
    }

    @Override
    public void paramProcess(JSONObject body, SystemUnitConfig unitConfig) {
        body.put("tradeUnitType", SystemUnitConfig.getTypeName(unitConfig.getType()));
    }
}
