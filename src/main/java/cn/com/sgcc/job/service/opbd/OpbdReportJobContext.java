package cn.com.sgcc.job.service.opbd;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OpbdReportJobContext {
    private final Map<String, OpbdReportJobService> map = new HashMap<>();
    public static final String DEFAULT_TYPE = "default";

    /**
     * OpbdReportJobContext Constructor
     *
     * @param services services
     */
    @Autowired
    public OpbdReportJobContext(List<OpbdReportJobService> services) {
        services.forEach(service -> service.getSupportedProv().forEach(prov -> map.put(prov, service)));
    }

    /**
     * getService
     *
     * @param prov prov
     * @return OpbdReportJobService
     */
    public OpbdReportJobService getService(String prov) {
        var service = map.get(prov);
        if (service == null) {
            service = map.get(DEFAULT_TYPE);
        }
        return service;
    }

    /**
     * getService
     *
     * @return OpbdReportJobService
     */
    public OpbdReportJobService getService() {
        return getService(DEFAULT_TYPE);
    }
}
