package cn.com.sgcc.job.service.opid;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OpidReportJobContext {
    private final Map<String, OpidReportJobService> map = new HashMap<>();
    public static final String DEFAULT_TYPE = "default";

    /**
     * OpidReportJobContext Constructor
     *
     * @param services services
     */
    @Autowired
    public OpidReportJobContext(List<OpidReportJobService> services) {
        services.forEach(service -> service.getSupportedProv().forEach(prov -> map.put(prov, service)));
    }

    /**
     * getService
     *
     * @param prov prov
     * @return OpidReportJobService
     */
    public OpidReportJobService getService(String prov) {
        var service = map.get(prov);
        if (service == null) {
            service = map.get(DEFAULT_TYPE);
        }
        return service;
    }

    /**
     * getService
     *
     * @return OpidReportJobService
     */
    public OpidReportJobService getService() {
        return getService(DEFAULT_TYPE);
    }
}
