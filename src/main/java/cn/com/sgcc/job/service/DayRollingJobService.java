package cn.com.sgcc.job.service;

import static cn.com.sgcc.constants.DateConsts.DATE_FORMAT;
import static cn.com.sgcc.constants.DateConsts.DATE_TIME_FORMAT;
import static cn.com.sgcc.constants.DateConsts.SGCC_DATE_TIME_FORMAT;
import static cn.com.sgcc.constants.DateConsts.SGCC_DATE_TIME_FORMAT_2;

import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.sgcc.autologin.service.LoginService;
import cn.com.sgcc.business.sgcc.dayrolling.controller.DayRollingController;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingConfigDetail;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingConfigDetailLog;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingTimeInfo;
import cn.com.sgcc.business.sgcc.dayrolling.repository.UserDayRollingConfigDetailLogRepo;
import cn.com.sgcc.business.sgcc.dayrolling.repository.UserDayRollingConfigDetailRepo;
import cn.com.sgcc.business.sgcc.dayrolling.repository.UserDayRollingTimeInfoRepo;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.constants.RoleConstants;
import cn.com.sgcc.job.model.DayRollingDto;
import cn.com.sgcc.util.DecimalUtil;
import cn.com.sgcc.util.SgccRequest;
import cn.com.sgcc.util.SgccResp;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ScheduledFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;

/**
 * DayRollingJobService
 */
@Slf4j
@Service
public class DayRollingJobService {
    @Value("${custom.dayRolling.preparationTimeInSecond}")
    int preparationTimeInSecond;
    @Value("${custom.dayRolling.waitTimeInMillSecond}")
    int waitTimeInMillSecond;
    @Value("${custom.dayRolling.encryptParticipantId.encryptionKey}")
    String encryptionKey;
    @Value("${custom.dayRolling.encryptParticipantId.encryptLevel}")
    int encryptLevel;
    String currentRoute = "/pxf-trade-auction-extranet/myTransaction/Announcement";
    String currentRoute2 = "/pxf-trade-auction-extranet/tradeDemoSx/rollMatchTrade?tradeseqId=";

    @Resource
    private TaskScheduler taskScheduler;
    @Resource
    private SgccRequest sgccRequest;
    @Resource
    private UnitService unitService;
    @Resource
    private UserDayRollingConfigDetailRepo configDetailRepo;
    @Resource
    private UserDayRollingConfigDetailLogRepo configDetailLogRepo;
    @Resource
    private UserDayRollingTimeInfoRepo timeInfoRepo;
    @Resource
    private LoginService loginService;
    @Resource
    private PmosTimeService pmosTimeService;

    private final List<ScheduledFuture<?>> detectScheduleList = new ArrayList<>();
    private final List<UserDayRollingTimeInfo> timeInfoList = new ArrayList<>();
    private final List<SystemUnitConfig> unitList = new ArrayList<>();

    /**
     * clearJob
     */
    public void clearJob() {
        timeInfoList.clear();
        unitList.clear();
        detectScheduleList.forEach(scheduledFuture -> scheduledFuture.cancel(true));
        detectScheduleList.clear();
    }

    /**
     * detectJob
     *
     * @param isCron  isCron
     * @param isToday isToday
     * @param params  params
     */
    public void detectJob(boolean isCron, boolean isToday, JSONObject params) {
        log.info("isCron: {}, isToday: {}, params: {}", isCron, isToday, params);
        var now = now();
        if (params != null) {
            var dateStr = params.getString("date");
            var unitId = params.getString("unitId");
            var date = LocalDate.parse(dateStr, DATE_FORMAT);
            var epochDay = now().toLocalDate().toEpochDay();
            if (date.toEpochDay() == epochDay || date.minusDays(1).toEpochDay() == epochDay) {
                detectByDateAndUnitId(dateStr, unitId);
            }
            return;
        }
        clearJob();
        if (isToday) {
            detectByDate(now, isCron);
        } else {
            detectByDate(now.plusDays(1), isCron);
        }
    }

    /**
     * detectByDate
     *
     * @param now    now
     * @param isCron isCron
     */
    public void detectByDate(LocalDateTime now, boolean isCron) {
        var strategyDate = now.format(DATE_FORMAT);
        log.info("开始获取日滚动交易信息，策略时间: {}", strategyDate);
        unitList.addAll(unitService.getUnitList(RoleConstants.Type.DAY_ROLLING));
        if (unitList.isEmpty()) {
            log.info("没有参与日滚动的交易单元");
            return;
        }
        var firstUnit = unitList.getFirst();
        var loginUnitId = firstUnit.getUnitId();
        var timeInfos = timeInfoRepo.findAllByStrategyDate(strategyDate);
        if (timeInfos.isEmpty() && isCron) {
            log.info("数据库中不存在 {} 日滚动信息，现在开始获取", strategyDate);
            var loginInfo = loginService.login(loginUnitId, false);
            if (loginInfo == null) {
                return;
            }
            var declareArray = querySeqOut(strategyDate, loginInfo);
            if (declareArray == null) {
                return;
            }
            timeInfos = declareArray.stream().map(o -> (JSONObject) o).map(jsonObject -> {
                var timeInfo = jsonObject.to(UserDayRollingTimeInfo.class);
                timeInfo.setStrategyDate(strategyDate);
                var beginDate = LocalDateTime.parse(timeInfo.getBeginDate(), SGCC_DATE_TIME_FORMAT)
                                             .format(DATE_FORMAT);
                if ("4".equals(timeInfo.getTradeCycle())) {
                    timeInfo.setDeclareDate(beginDate);
                } else {
                    timeInfo.setDeclareDate(beginDate + "旬交易");
                }
                timeInfo.setCreateTime(LocalDateTime.now().format(DATE_TIME_FORMAT));
                return timeInfo;
            }).toList();
            log.info("申报时间: {}", timeInfos.stream()
                                              .map(UserDayRollingTimeInfo::getDeclareDate)
                                              .collect(Collectors.joining(", ")));
            timeInfos.forEach(timeInfo -> {
                var schArray = querySchById(timeInfo, loginInfo);
                if (schArray == null) {
                    return;
                }
                schArray.stream()
                        .map(o -> (JSONObject) o)
                        .sorted(Comparator.comparing(o -> o.getString("startTime")))
                        .forEach(jsonObject -> {
                            if (StringUtils.isBlank(timeInfo.getStartTime1())) {
                                timeInfo.setStartTime1(jsonObject.getString("startTime"));
                                timeInfo.setEndTime1(jsonObject.getString("endTime"));
                            } else {
                                timeInfo.setStartTime2(jsonObject.getString("startTime"));
                                timeInfo.setEndTime2(jsonObject.getString("endTime"));
                            }
                        });
                var tradeseqId = timeInfo.getTradeseqId();
                var declareDate = timeInfo.getDeclareDate();
                saveInfo(unitList, strategyDate, declareDate, tradeseqId);
            });
            timeInfoRepo.saveAllAndFlush(timeInfos);
        }
        for (UserDayRollingTimeInfo timeInfo : timeInfos) {
            var declareStartTime = LocalDateTime.parse(timeInfo.getStartTime1(), SGCC_DATE_TIME_FORMAT_2);
            var taskStartTime = declareStartTime.plusSeconds(-preparationTimeInSecond);
            now = now();
            if (taskStartTime.toLocalDate().toEpochDay() == now.toLocalDate().toEpochDay()
                && taskStartTime.isBefore(now)) {
                log.info("{} 已经超过开始申报时间: {}，不再创建抢报任务",
                    timeInfo.getTradeseqId(), taskStartTime.format(DATE_TIME_FORMAT));
            } else {
                log.info("新建抢报任务: {}，任务执行时间: {}",
                    timeInfo.getTradeseqId(), taskStartTime.format(DATE_TIME_FORMAT));
                var dto = new DayRollingDto();
                dto.setDeclareStartTime(declareStartTime);
                dto.setTimeInfo(timeInfo);
                detectScheduleList.add(taskScheduler.schedule(
                    () -> report(unitList, dto, 1), taskStartTime.toInstant(ZoneOffset.ofHours(8)))
                );
            }
        }
        timeInfoList.addAll(timeInfos);
        timeInfoList.forEach(timeInfo -> {
            timeInfo.setLocalStartTime1(LocalDateTime.parse(timeInfo.getStartTime1(), SGCC_DATE_TIME_FORMAT_2));
            timeInfo.setLocalEndTime1(LocalDateTime.parse(timeInfo.getEndTime1(), SGCC_DATE_TIME_FORMAT_2));
            timeInfo.setLocalStartTime2(LocalDateTime.parse(timeInfo.getStartTime2(), SGCC_DATE_TIME_FORMAT_2));
            timeInfo.setLocalEndTime2(LocalDateTime.parse(timeInfo.getEndTime2(), SGCC_DATE_TIME_FORMAT_2));
        });
    }

    /**
     * detectByDateAndUnitId
     *
     * @param strategyDate strategyDate
     * @param unitId       unitId
     */
    public void detectByDateAndUnitId(String strategyDate, String unitId) {
        log.info("{} 重新获取 {} 日滚动信息，现在开始获取", unitId, strategyDate);
        var unitConfig = unitList.stream().filter(unit -> unit.getUnitId().equals(unitId)).findFirst();
        if (unitConfig.isEmpty()) {
            log.info("没有这个交易单元: {}", unitId);
            return;
        }
        var loginInfo = loginService.login(unitId, false);
        if (loginInfo == null) {
            return;
        }
        var timeInfos = timeInfoRepo.findAllByStrategyDate(strategyDate);
        if (timeInfos.isEmpty()) {
            var declareArray = querySeqOut(strategyDate, loginInfo);
            if (declareArray == null) {
                return;
            }
            timeInfos = declareArray.stream().map(o -> (JSONObject) o).map(jsonObject -> {
                var timeInfo = jsonObject.to(UserDayRollingTimeInfo.class);
                timeInfo.setStrategyDate(strategyDate);
                if ("4".equals(timeInfo.getTradeCycle())) {
                    timeInfo.setDeclareDate(LocalDateTime.parse(timeInfo.getBeginDate(), SGCC_DATE_TIME_FORMAT)
                                                         .format(DATE_FORMAT));
                } else {
                    timeInfo.setDeclareDate(LocalDateTime.parse(timeInfo.getBeginDate(), SGCC_DATE_TIME_FORMAT)
                                                         .format(DATE_FORMAT) + "旬交易");
                }
                timeInfo.setCreateTime(LocalDateTime.now().format(DATE_TIME_FORMAT));
                return timeInfo;
            }).toList();
            log.info("申报时间: {}", timeInfos.stream()
                                              .map(UserDayRollingTimeInfo::getDeclareDate)
                                              .collect(Collectors.joining(", ")));
            timeInfos.forEach(timeInfo -> {
                var schArray = querySchById(timeInfo, loginInfo);
                if (schArray == null) {
                    return;
                }
                schArray.stream()
                        .map(o -> (JSONObject) o)
                        .sorted(Comparator.comparing(o -> o.getString("startTime")))
                        .forEach(jsonObject -> {
                            if (StringUtils.isBlank(timeInfo.getStartTime1())) {
                                timeInfo.setStartTime1(jsonObject.getString("startTime"));
                                timeInfo.setEndTime1(jsonObject.getString("endTime"));
                            } else {
                                timeInfo.setStartTime2(jsonObject.getString("startTime"));
                                timeInfo.setEndTime2(jsonObject.getString("endTime"));
                            }
                        });
                var tradeseqId = timeInfo.getTradeseqId();
                var declareDate = timeInfo.getDeclareDate();
                saveInfo(unitList, strategyDate, declareDate, tradeseqId);
            });
            timeInfoRepo.saveAllAndFlush(timeInfos);
        }
        for (var timeInfo : timeInfos) {
            saveInfo(List.of(unitConfig.get()), strategyDate, timeInfo.getDeclareDate(), timeInfo.getTradeseqId());
        }
    }

    /**
     * queryDishDataJob
     */
    public void queryDishDataJob() {
        if (timeInfoList.isEmpty()) {
            return;
        }
        var now = now();
        timeInfoList.stream()
                    .filter(timeInfo -> now.isAfter(timeInfo.getLocalStartTime1())
                                        && now.isBefore(timeInfo.getLocalEndTime1())
                                        || now.isAfter(timeInfo.getLocalStartTime2())
                                           && now.isBefore(timeInfo.getLocalEndTime2()))
                    .forEach(timeInfo -> {
                        log.info("盯盘任务: {}, 策略时间: {}, 申报时间: {}",
                            timeInfo.getTradeseqId(), timeInfo.getStrategyDate(), timeInfo.getDeclareDate());
                        var dto = new DayRollingDto();
                        dto.setTimeInfo(timeInfo);
                        report(unitList, dto, 2);
                    });
    }

    /**
     * saveInfo
     *
     * @param unitList     unitList
     * @param strategyDate strategyDate
     * @param declareDate  declareDate
     * @param tradeseqId   tradeseqId
     */
    private void saveInfo(List<SystemUnitConfig> unitList, String strategyDate, String declareDate, String tradeseqId) {
        for (SystemUnitConfig unitConfig : unitList) {
            var unitId = unitConfig.getUnitId();
            var details = configDetailRepo
                .findAllByUnitIdAndStrategyDateAndDeclareDate(unitId, strategyDate, declareDate);
            if (!details.isEmpty()) {
                continue;
            }
            var loginInfo = loginService.login(unitId, false);
            log.info("保存限额等配置信息: unitId: {}, tradeseqId: {}", unitId, tradeseqId);
            var arrayData = queryPositionStatistics(unitConfig.getUnitIdIlt(), tradeseqId, loginInfo);
            if (arrayData == null) {
                return;
            }
            // 抢报和盯盘都创建数据
            for (Integer type : List.of(1, 2)) {
                for (Object o : arrayData) {
                    var jsonObject = (JSONObject) o;
                    var timeCode = jsonObject.getInteger("timeCode");
                    var configDetail = new UserDayRollingConfigDetail();
                    configDetail.setUnitId(unitId);
                    configDetail.setStrategyDate(strategyDate);
                    configDetail.setDeclareDate(declareDate);
                    configDetail.setTimeCode(timeCode);
                    configDetail.setType(type);
                    configDetail.setTradeSeqId(tradeseqId);
                    configDetail.setBuyEnergyLimit(jsonObject.getDouble("buyEnergyLimit"));
                    configDetail.setSellEnergyLimit(jsonObject.getDouble("sellEnergyLimit"));
                    configDetail.setPriceLowerLimit(jsonObject.getDouble("priceLowerLimit"));
                    configDetail.setPriceUpperLimit(jsonObject.getDouble("priceUpperLimit"));
                    configDetail.setStatus(0);
                    configDetail.setCreateTime(now().format(DATE_TIME_FORMAT));
                    details.add(configDetail);
                }
            }
            configDetailRepo.saveAllAndFlush(details);
            configDetailLogRepo.saveAllAndFlush(details.stream().map(detail -> {
                var detailLog = new UserDayRollingConfigDetailLog();
                BeanUtils.copyProperties(detail, detailLog);
                detailLog.setCreateTime(now().format(DATE_TIME_FORMAT));
                return detailLog;
            }).toList());
            log.info("保存申报限额配置信息成功: {}", details.size());
        }
    }

    /**
     * report
     *
     * @param unitList      unitList
     * @param dayRollingDto dayRollingDto
     * @param type          type
     */
    private void report(List<SystemUnitConfig> unitList, DayRollingDto dayRollingDto, int type) {
        var timeInfo = dayRollingDto.getTimeInfo();
        if (type == 1) {
            var duration = Duration.between(now(), dayRollingDto.getDeclareStartTime());
            if (duration.isNegative()) {
                log.warn("滚动交易已经开始了，来不及了，抢报结束");
                return;
            }
            log.info("滚动交易抢报任务开始, 距离抢报开始剩余时间: {} s", duration.toSeconds());
        }
        var all = new HashMap<String, DayRollingDto>();
        unitList.forEach(unitConfig -> {
            var details = configDetailRepo.findAllByUnitIdAndStrategyDateAndDeclareDate(
                                              unitConfig.getUnitId(),
                                              timeInfo.getStrategyDate(),
                                              timeInfo.getDeclareDate()
                                          )
                                          .stream()
                                          .filter(detail -> detail.getType() == type
                                                            && detail.getTradeSeqId() != null
                                                            && detail.getTradeRole() != null
                                                            && detail.getPower() != null
                                                            && detail.getPower() != 0
                                                            && detail.getPrice() != null
                                                            && detail.getBuyEnergyLimit() != null
                                                            && detail.getSellEnergyLimit() != null
                                                            && detail.getPriceLowerLimit() != null
                                                            && detail.getPriceUpperLimit() != null
                                                            && detail.getStatus() == 0
                                          ).toList();
            if (details.isEmpty()) {
                log.info("没有要申报的交易, 交易单元: {}", unitConfig.getUnitId());
                return;
            }
            log.info("准备开始申报，需要申报的交易单元提前登录: {}", unitConfig.getUnitId());
            var dto = new DayRollingDto();
            BeanUtils.copyProperties(dayRollingDto, dto);
            dto.setTimeInfo(dayRollingDto.getTimeInfo());
            dto.setUnitConfig(unitConfig);
            dto.setDetails(details);
            var loginInfo = loginService.login(unitConfig.getUnitId(), false);
            var tradeUnitInfo = queryTradeUnit(timeInfo.getTradeseqId(), dto.getUnitConfig().getUnitIdIlt(), loginInfo);
            if (tradeUnitInfo == null) {
                return;
            }
            dto.setLoginInfo(loginInfo);
            dto.setMarketId(tradeUnitInfo.getString("marketId"));
            dto.setMembersName(tradeUnitInfo.getString("membersName"));
            dto.setMembersType(tradeUnitInfo.getString("membersType"));
            all.put(unitConfig.getUnitId(), dto);
            log.info("交易单元: {}, 要申报的交易: {}", unitConfig.getUnitId(), timeInfo.getTradeseqId());
        });
        if (all.isEmpty()) {
            log.info("没有要申报的交易单元");
            return;
        }
        if (type == 1) {
            var duration = Duration.between(now(), dayRollingDto.getDeclareStartTime());
            if (duration.isNegative()) {
                log.warn("滚动交易已经开始了，来不及了，申报结束");
                return;
            }
            log.info("距离申报开始剩余时间: {} s", duration.toSeconds());
            try {
                Thread.sleep(duration.toMillis() + waitTimeInMillSecond);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("异常中断", e);
                return;
            }
        }
        all.entrySet().parallelStream().forEach(entry -> {
            var unitId = entry.getKey();
            var dto = entry.getValue();
            List<UserDayRollingConfigDetail> okList;
            if (type != 1) {
                var dishDataMapByTime = queryDishData(timeInfo.getTradeseqId(), dto.getLoginInfo());
                log.info("开始对比限额");
                okList = dto.getDetails()
                            .stream()
                            .filter(detail -> {
                                var dishData = dishDataMapByTime.get(detail.getTimeCode());
                                var buyAmount = dishData.getDouble("buyAmount1");
                                var saleAmount = dishData.getDouble("saleAmount1");
                                var buyPrice = dishData.getDouble("buyPrice1");
                                var salePrice = dishData.getDouble("salePrice1");
                                return switch (detail.getTradeRole()) {
                                    case "1" -> {
                                        if (detail.getPrice() >= salePrice) {
                                            detail.setReportPower(Math.min(saleAmount, detail.getPower()));
                                            detail.setReportPrice(salePrice);
                                            yield true;
                                        }
                                        yield false;
                                    }
                                    case "2" -> {
                                        if (detail.getPrice() <= buyPrice) {
                                            detail.setReportPower(Math.min(buyAmount, detail.getPower()));
                                            detail.setReportPrice(buyPrice);
                                            yield true;
                                        }
                                        yield false;
                                    }
                                    case null, default -> false;
                                };
                            })
                            .toList();
                if (okList.isEmpty()) {
                    log.warn("没有符合条件的交易，申报结束");
                    return;
                }
                // 有符合条件的数据再查是否还有次数
                var positionInfo = queryPositionInfo(dto);
                if (positionInfo == null) {
                    return;
                }
                okList = okList.stream()
                               .filter(detail -> positionInfo.get(detail.getTimeCode()) != null
                                                 && positionInfo.get(detail.getTimeCode()) > 0).toList();
                if (okList.isEmpty()) {
                    log.warn("没有符合条件的交易，申报结束");
                    return;
                }
                var detailList = queryDealDetailList(timeInfo.getTradeseqId(), dto.getLoginInfo());
                if (detailList != null) {
                    for (Object o : detailList) {
                        var detail = (JSONObject) o;
                        var detailUnitId = detail.getString("unitId");
                        var timeCode = detail.getString("timeCode");
                        var tradeDirection = detail.getInteger("tradeDirection");
                        okList.stream()
                              .filter(ok -> dto.getUnitConfig().getUnitIdIlt().equals(detailUnitId)
                                            && (ok.getTimeCode() + "").equals(timeCode))
                              .forEach(ok -> {
                                  // 已经申报成功过，同一时段交易不能同时买卖
                                  ok.setTradeRoleLock(String.valueOf(tradeDirection));
                                  configDetailRepo.updateTradeRoleLock(ok);
                              });
                    }
                    var oldSize = okList.size();
                    okList = okList.stream()
                                   .filter(detail -> detail.getTradeRoleLock() == null
                                                     || detail.getTradeRoleLock().equals(detail.getTradeRole()))
                                   .toList();
                    if (oldSize != okList.size()) {
                        log.warn("过滤掉了 {} 条交易方向错误的数据", oldSize - okList.size());
                    }
                    if (okList.isEmpty()) {
                        log.warn("没有符合条件的交易，申报结束");
                        return;
                    }
                }
            } else {
                okList = dto.getDetails();
            }
            var declareSuccess = declare(dto, okList);
            if (declareSuccess) {
                okList.forEach(detail -> detail.setStatus(1));
                configDetailRepo.saveAllAndFlush(okList);
                configDetailLogRepo.saveAllAndFlush(okList.stream().map(detail -> {
                    var detailLog = new UserDayRollingConfigDetailLog();
                    BeanUtils.copyProperties(detail, detailLog);
                    detailLog.setCreateTime(LocalDateTime.now().format(DATE_TIME_FORMAT));
                    return detailLog;
                }).toList());
                DayRollingController.EMITTERS.send(dto.getUnitConfig().getUName() + " 日滚动交易申报完成");
            }
        });
    }

    /**
     * declare
     *
     * @param dto    dto
     * @param okList okList
     * @return boolean
     */
    private boolean declare(DayRollingDto dto, List<UserDayRollingConfigDetail> okList) {
        String businessName = "申报数据提交";
        log.info("开始 {}, okListSize: {}", businessName, okList.size());
        String respStr;
        try (var response = sgccRequest
            .getPostRequest("/px-trade-auction/listing/declare", JSONArray.toJSONString(
                okList.stream()
                      .map(detail -> JSONObject
                          .of()
                          .fluentPut("unitId", dto.getUnitConfig().getUnitIdIlt())
                          .fluentPut("unitName", dto.getUnitConfig().getUnitNameIlt())
                          // 时段类型 [1, 24]
                          .fluentPut("reportType", String.valueOf(detail.getTimeCode()))
                          // 时段类型 [1, 24]
                          .fluentPut("beginTime", String.format("%02d:00", detail.getTimeCode() - 1))
                          // 时段类型 [1, 24]
                          .fluentPut("endTime", String.format("%02d:00", detail.getTimeCode()))
                          .fluentPut("tag", detail.getTimeCode() - 1) // 时段类型 [1, 24]
                          .fluentPut("tradeseqId", dto.getTimeInfo().getTradeseqId())
                          .fluentPut("participantId", encryptParticipantId(dto.getMarketId()))
                          .fluentPut("participantName", dto.getMembersName())
                          .fluentPut("participantType", dto.getMembersType())
                          .fluentPut("tradeRole", detail.getTradeRole())
                          .fluentPut("amount", detail.getReportPower())
                          .fluentPut("price", detail.getReportPrice())
                          .fluentPut("sumEnergy", DecimalUtil
                              .multiply(detail.getReportPower(), Period.between(
                                  LocalDateTime.parse(dto.getTimeInfo().getStartTime(),
                                      SGCC_DATE_TIME_FORMAT).toLocalDate(),
                                  LocalDateTime.parse(dto.getTimeInfo().getEndDate(),
                                      SGCC_DATE_TIME_FORMAT).toLocalDate()
                              ).getDays() + 1)
                          )
                      ).toList()), dto.getLoginInfo())
            .header("Currentroute",
                "/pxf-trade-auction-extranet/tradeDemoSx/rollMatchTrade?tradeseqId="
                + dto.getTimeInfo().getTradeseqId() + "&date=" + System.currentTimeMillis())
            .execute()) {
            respStr = response.body();
        }
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return false;
        }
        log.info("{} 结果: {}", businessName, true);
        return true;
    }

    /**
     * querySeqOut
     *
     * @param strategyDate strategyDate
     * @param loginInfo    loginInfo
     * @return JSONArray
     */
    private JSONArray querySeqOut(String strategyDate, LoginInfo loginInfo) {
        String businessName = "查询日滚动交易列表";
        log.info("开始 {}, strategyDate: {}", businessName, strategyDate);
        String respStr;
        try (var response = sgccRequest
            .getPostRequest("/px-trade-auction/auctionConfig/querySeqOut", JSONObject.of(
                "data", JSONObject.of(
                    "bidDate", strategyDate, // 标的日
                    "tradeBatchCaption", "",
                    "tradeCycle", "", // 交易周期: { 1: "多月", 2: "月度", 3: "旬", 4: "日滚动" }
                    "tradeStage", "2", // 交易类型 (1: 集中竞价, 2: 滚动撮合)
                    "tradeseqStatus", ""),
                "pageInfo", JSONObject.of()
            ).toString(), loginInfo)
            .header("Currentroute", currentRoute)
            .execute()) {
            respStr = response.body();
        }
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var list = SgccResp.jsonData(sgccResp).getJSONArray("list");
        if (list == null) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        if (list.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        list = new JSONArray(list.stream()
                                 .filter(o -> "2".equals(((JSONObject) o).getString("tradeStage")))
                                 .filter(o -> List.of("3", "4").contains(((JSONObject) o).getString("tradeCycle")))
                                 .toList());
        log.info("{} 结果: {}", businessName, list);
        return list;
    }

    /**
     * querySchById
     *
     * @param timeInfo  timeInfo
     * @param loginInfo loginInfo
     * @return JSONArray
     */
    public JSONArray querySchById(UserDayRollingTimeInfo timeInfo, LoginInfo loginInfo) {
        String businessName = "查询申报开始时间";
        log.info("开始 {}, tradeseqId: {}", businessName, timeInfo.getTradeseqId());
        String respStr;
        var tradeBatchId = timeInfo.getTradeBatchId();
        try (var response = sgccRequest
            .getPostRequest("/px-trade-auction/auctionConfig/querySchById", JSONObject.of(
                "tradeBatchId", tradeBatchId
            ).toString(), loginInfo)
            .header("Currentroute", currentRoute)
            .execute()) {
            respStr = response.body();
        }
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var arrayData = SgccResp.arrayData(sgccResp);
        if (arrayData.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        arrayData = new JSONArray(
            arrayData.stream()
                     .map(o -> (JSONObject) o)
                     .filter(o -> "gd_flow".equals(o.getString("flowCode")))
                     .toList());
        log.info("{} 结果: {}", businessName, arrayData);
        return arrayData;
    }

    /**
     * queryPositionStatistics
     *
     * @param unitIdIlt  unitIdIlt
     * @param tradeseqId tradeseqId
     * @param loginInfo  loginInfo
     * @return JSONArray
     */
    public JSONArray queryPositionStatistics(String unitIdIlt, String tradeseqId, LoginInfo loginInfo) {
        String businessName = "查询申报限额信息";
        log.info("开始 {}, unitIdIlt: {}, tradeseqId: {}", businessName, unitIdIlt, tradeseqId);
        String respStr;
        try (var response = sgccRequest
            .getPostRequest("/px-trade-auction/access/queryPositionStatistics", JSONObject.of(
                "tradeseqId", tradeseqId,
                "unitId", unitIdIlt
            ).toString(), loginInfo)
            .header("Currentroute", currentRoute)
            .execute()) {
            respStr = response.body();
        }
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var arrayData = SgccResp.arrayData(sgccResp);
        if (arrayData.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, arrayData);
        return arrayData;
    }

    /**
     * 5 秒钟一次
     *
     * @param tradeseqId tradeseqId
     * @param loginInfo  loginInfo
     * @return Map<Integer, JSONObject>
     */
    public Map<Integer, JSONObject> queryDishData(String tradeseqId, LoginInfo loginInfo) {
        String businessName = "查询交易列表";
        log.info("开始 {}, tradeseqId: {}", businessName, tradeseqId);
        String respStr;
        try (var response = sgccRequest
            .getPostRequest("/px-trade-auction/listing/queryDishData", JSONObject.of(
                "tradeseqId", tradeseqId
            ).toString(), loginInfo)
            .header("Currentroute", currentRoute2 + tradeseqId)
            .execute()) {
            respStr = response.body();
        }
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var array = SgccResp.arrayData(sgccResp);
        if (array.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        var result = array.stream().map(o -> (JSONObject) o)
                          .collect(Collectors.toMap(
                              jsonObject -> jsonObject.getInteger("timeCode"), Function.identity()));
        log.info("{} 结果: {}", businessName, result);
        return result;
    }

    /**
     * 1 分钟一次
     *
     * @param dayRollingDto dayRollingDto
     * @return Map<Integer, Integer>
     */
    public Map<Integer, Integer> queryPositionInfo(DayRollingDto dayRollingDto) {
        String businessName = "查询可申报笔数";
        log.info("开始 {}, unitIdIlt: {}, tradeseqId: {}, beginDate: {}",
            businessName,
            dayRollingDto.getUnitConfig().getUnitId(),
            dayRollingDto.getTimeInfo().getTradeseqId(),
            dayRollingDto.getTimeInfo().getBeginDate());
        String respStr;
        try (var response = sgccRequest
            .getPostRequest("/px-trade-auction/listing/queryPositionInfo", JSONObject.of(
                "beginDate", dayRollingDto.getTimeInfo().getBeginDate(),
                "participantType", "10",
                "tradeseqId", dayRollingDto.getTimeInfo().getTradeseqId(),
                "unitId", dayRollingDto.getUnitConfig().getUnitIdIlt()
            ).toString(), dayRollingDto.getLoginInfo())
            .header("Currentroute", currentRoute2 + dayRollingDto.getTimeInfo().getTradeseqId())
            .execute()) {
            respStr = response.body();
        }
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var array = SgccResp.arrayData(sgccResp);
        if (array.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        var result = array.stream().map(o -> (JSONObject) o)
                          .collect(Collectors.toMap(
                              jsonObject -> jsonObject.getInteger("timeCode"),
                              jsonObject -> jsonObject.getInteger("bidEffectiveNum")
                          ));
        log.info("{} 结果: {}", businessName, result);
        return result;
    }

    /**
     * information
     *
     * @param loginInfo loginInfo
     * @return JSONObject
     */
    public JSONObject information(LoginInfo loginInfo) {
        String businessName = "查询账号信息";
        log.info("开始 {}, userName: {}", businessName, loginInfo.getUserName());
        String respStr;
        try (var response = sgccRequest
            .getPostRequest("/px-common-authority/user/information", JSONObject.of(
                "token", loginInfo.getTicket()
            ).toString(), loginInfo)
            .header("Currentroute", "/outNet")
            .execute()) {
            respStr = response.body();
        }
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var data = SgccResp.jsonData(sgccResp);
        log.info("{} 结果: {}", businessName, data);
        return data;
    }

    /**
     * queryTradeUnit
     *
     * @param tradeseqId tradeseqId
     * @param unitIdIlt  unitIdIlt
     * @param loginInfo  loginInfo
     * @return JSONObject
     */
    public JSONObject queryTradeUnit(String tradeseqId, String unitIdIlt, LoginInfo loginInfo) {
        String businessName = "查询交易单元信息";
        log.info("开始 {}, unitIdIlt: {}, tradeseqId: {}", businessName, unitIdIlt, tradeseqId);
        String respStr;
        try (var response = sgccRequest
            .getPostRequest("/px-trade-auction/auctionConfig/queryTradeUnit", JSONObject.of(
                "tradeseqId", tradeseqId
            ).toString(), loginInfo)
            .header("Currentroute", currentRoute)
            .execute()) {
            respStr = response.body();
        }
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var array = SgccResp.arrayData(sgccResp);
        if (array.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        var unitList = array.stream().map(o -> (JSONObject) o)
                            .filter(jsonObject -> unitIdIlt.equals(jsonObject.getString("unitId"))).toList();
        if (unitList.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        var unitInfo = unitList.getFirst();
        log.info("{} 结果: {}", businessName, unitInfo);
        return unitInfo;
    }

    /**
     * queryDealDetailList
     *
     * @param tradeseqId tradeseqId
     * @param loginInfo  loginInfo
     * @return JSONArray
     */
    public JSONArray queryDealDetailList(String tradeseqId, LoginInfo loginInfo) {
        String businessName = "查询交易成功列表";
        log.info("开始 {}, tradeseqId: {}", businessName, tradeseqId);
        String respStr;
        try (var response = sgccRequest
            .getPostRequest("/px-trade-auction/listing/queryDealDetailList", JSONObject.of(
                "pageInfo", JSONObject.of("pageNum", 1,
                    "pageSize", 100,
                    "total", 0),
                "data", JSONObject.of("listingRole", "",
                    "timeCodeList", JSONArray.of(),
                    "tradeseqId", tradeseqId)
            ).toString(), loginInfo)
            .header("Currentroute", currentRoute2 + tradeseqId)
            .execute()) {
            respStr = response.body();
        }
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var jsonData = SgccResp.jsonData(sgccResp);
        if (jsonData.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        var array = jsonData.getJSONArray("list");
        if (array == null || array.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, array);
        return array;
    }

    /**
     * encryptParticipantId
     *
     * @param marketId marketId
     * @return String
     */
    public String encryptParticipantId(String marketId) {
        marketId = marketId + now().format(DATE_TIME_FORMAT).replace("-", "");
        var t = new StringBuilder();
        for (var n = 0; n < marketId.length(); n++) {
            var r = encryptionKey.indexOf(marketId.charAt(n));
            var o = r + encryptLevel >= encryptionKey.length()
                    ? r + encryptLevel - encryptionKey.length()
                    : r + encryptLevel;
            t.append(encryptionKey.charAt(o));
        }
        var n = t.toString();
        var r = UUID.randomUUID().toString().replace("-", "").toUpperCase();
        var o = 32 - n.length();
        var i = r.substring(1, o + 1);
        return n + i;
    }

    /**
     * now
     *
     * @return LocalDateTime
     */
    public LocalDateTime now() {
        return pmosTimeService.getCurrentPmosTime();
    }

}
