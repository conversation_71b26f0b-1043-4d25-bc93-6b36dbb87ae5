package cn.com.sgcc.job.service.opid;

import static cn.com.sgcc.constants.DateConsts.DATE_FORMAT;
import static cn.com.sgcc.constants.DateConsts.DATE_TIME_FORMAT;
import static cn.com.sgcc.constants.DateConsts.DATE_TIME_HH_MM_FORMAT;
import static cn.com.sgcc.constants.RoleConstants.Type.OPID;

import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.sgcc.autologin.service.LoginService;
import cn.com.sgcc.business.sgcc.opid.model.SystemOpidTimeConfig;
import cn.com.sgcc.business.sgcc.opid.model.UserOpidConfigDetail;
import cn.com.sgcc.business.sgcc.opid.repository.SystemOpidTimeConfigRepo;
import cn.com.sgcc.business.sgcc.opid.repository.UserOpidConfigDetailRepo;
import cn.com.sgcc.business.sgcc.profitanalysis.repository.DataProfitAnalysisRepo;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.util.EncryptUtil;
import cn.com.sgcc.util.SgccRequest;
import cn.com.sgcc.util.SgccResp;
import cn.com.sgcc.util.http.NewSgccRequest;
import cn.com.sgcc.util.http.ReqConfig;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OpidReportJobServiceDefault implements OpidReportJobService {
    @Value("${custom.opid.range.start}")
    int rangeStart;
    @Value("${custom.opid.range.end}")
    int rangeEnd;
    @Value("${custom.pmos.queryLimit}")
    boolean queryLimit;
    @Value("${custom.opid.sync.url}")
    String syncUrl;

    @Resource
    UnitService unitService;
    @Resource
    SystemOpidTimeConfigRepo timeConfigRepo;
    @Resource
    UserOpidConfigDetailRepo configDetailRepo;
    @Resource
    DataProfitAnalysisRepo dataProfitAnalysisRepo;
    @Resource
    LoginService loginService;
    @Resource
    SgccRequest sgccRequest;
    @Resource
    NewSgccRequest newSgccRequest;
    String currentRoute = "/pxf-psgcc-spotgoods-extranet/day-trade";

    @Override
    public List<String> getSupportedProv() {
        return List.of(OpidReportJobContext.DEFAULT_TYPE);
    }

    @Override
    public void doDeclare() {
        var tradeTimeConfigs = timeConfigRepo.findAll();
        var nowDate = LocalDateTime.now();
        var hour = nowDate.getHour();
        var minute = nowDate.getMinute();
        var list = tradeTimeConfigs.stream().filter(i -> {
            String[] hourMinuteA = i.getBidStartTime().split(":");
            String[] hourMinuteB = i.getBidEndTime().split(":");
            int startHour = Integer.parseInt(hourMinuteA[0]);
            int endHour = Integer.parseInt(hourMinuteB[0]);
            int startMin = Integer.parseInt(hourMinuteA[1]);
            int endMin = Integer.parseInt(hourMinuteB[1]);
            return startHour == endHour
                   ? (hour == startHour && (minute >= startMin && minute <= endMin))
                   : ((hour == startHour && minute >= startMin) || (hour == endHour && minute <= endMin));
        }).toList();
        if (list.size() == 1) {
            log.debug("当前时间段可以执行申报");
            var timeConfig = list.getFirst();
            var startBidHour = Integer.parseInt(timeConfig.getBidStartTime().split(":")[0]);
            var endBidHour = Integer.parseInt(timeConfig.getBidEndTime().split(":")[0]);
            var startPartHour = Integer.parseInt(timeConfig.getStartPart().split(":")[0]);
            var reportDay = nowDate.plusSeconds(1);
            // 00:15 和 02:15 可能会会跨天，20 点之后才有可能跨天
            if (startPartHour < 3 && hour > 20 && (startBidHour > 20 || endBidHour > 20)) {
                // 今天申报明天的，日期加一
                reportDay = reportDay.plusDays(1);
            }
            var strategyConfigDetailList = configDetailRepo.findAllNotSuccess(
                reportDay.format(DATE_FORMAT), timeConfig.getStartPart(), timeConfig.getEndPart());
            log.debug("strategyConfigDetailList size: {}", strategyConfigDetailList.size());
            report(strategyConfigDetailList, timeConfig, tradeTimeConfigs, reportDay);
        } else {
            log.debug("当前时间段不能执行申报");
        }
    }

    @Override
    public void doDeclareAll(boolean force) {
    }

    /**
     * report
     *
     * @param strategyConfigDetailList strategyConfigDetailList
     * @param timeConfig               timeConfig
     * @param tradeTimeConfigs         tradeTimeConfigs
     * @param reportDay                reportDay
     */
    public void report(List<UserOpidConfigDetail> strategyConfigDetailList, SystemOpidTimeConfig timeConfig,
                       List<SystemOpidTimeConfig> tradeTimeConfigs, LocalDateTime reportDay) {
        var needStrategyConfigDetailList = strategyConfigDetailList
            .stream()
            .filter(i -> i.getStatus() != 1 && i.getPercent() != null && i.getPercent() != 0)
            .toList();
        if (needStrategyConfigDetailList.isEmpty()) {
            log.debug("当前时间没有需要执行的申报");
            return;
        }
        log.info("当前时间有需要执行的申报");
        var today = LocalDate.now().format(DATE_FORMAT);
        var unitMap = unitService.getUnitMap(OPID);
        var loginMap = needStrategyConfigDetailList
            .stream()
            .map(UserOpidConfigDetail::getUnitId)
            .distinct()
            .parallel()
            .map(unitId -> loginService.login(unitId, false))
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(LoginInfo::getPlantId, Function.identity(), (o1, o2) -> o1));
        if (loginMap.isEmpty()) {
            log.info("没有登录成功的账号");
            return;
        }
        var updateNumber = new AtomicInteger();
        var firstLoginInfo = loginMap.values().iterator().next();
        // 获取申报时间范围
        var tradePartResp = sgccRequest.getPostRequest(
                                           "/px-psgcc-spotgoods-extranet/dayInTradeDeclare/getTradePart",
                                           "{}", firstLoginInfo)
                                       .header("Currentroute", currentRoute)
                                       .execute().body();
        log.info("申报时间范围：{}", tradePartResp);
        var tradePart = JSONObject.parseObject(tradePartResp);
        if (tradePart.getInteger("status") == 0) {
            var partArray = tradePart.getJSONArray("data");
            if (partArray.size() == 12) {
                partArray.forEach(part -> {
                    var jsonPart = (JSONObject) part;
                    var tradeTimePart = jsonPart.getString("tradeTimePart");
                    var bidEndTime = jsonPart.getString("bidEndTime");
                    var endTime = LocalTime.parse(bidEndTime, DateTimeFormatter.ofPattern("HH:mm:ss"));
                    var start = endTime.minusMinutes(rangeStart).format(DateTimeFormatter.ofPattern("HH:mm"));
                    var end = endTime.minusMinutes(rangeEnd).format(DateTimeFormatter.ofPattern("HH:mm"));
                    timeConfigRepo.updateTimeConfig(start, end, tradeTimePart);
                    // 判断时间范围改变，则退出
                    if (tradeTimeConfigs.stream().noneMatch(
                        config -> config.getTradeTimePart().toString().equals(tradeTimePart)
                            && config.getBidStartTime().equals(start)
                            && config.getBidEndTime().equals(end))) {
                        updateNumber.addAndGet(1);
                    }
                });
            }
        }

        if (updateNumber.get() > 0) {
            log.info("申报时间已更改");
            return;
        }
        log.info("申报时间未更改");
        var groupMap = needStrategyConfigDetailList
            .stream()
            .collect(Collectors.groupingBy(UserOpidConfigDetail::getUnitId));
        groupMap.forEach((unitId, detailList) -> {
            try {
                var unitConfig = unitMap.get(unitId);
                var loginInfo = loginMap.get(unitConfig.getPlantId());
                agreeConfirmation(unitId, today, loginInfo);
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("异常中断", e);
                }
                if (!setLimit(unitConfig, timeConfig, reportDay, detailList, loginInfo)) {
                    return;
                }
                updateReportPrice(detailList);
                // 根据每个交易单元的配置情况，发送申报请求
                if (execDeclare(unitId, detailList, loginInfo, timeConfig.getOrderNumber())) {
                    detailList.forEach(detail -> {
                        detail.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
                        detail.setStatus(1);
                    });
                    configDetailRepo.saveAllAndFlush(detailList);
                }
            } catch (Exception e) {
                log.error(unitId + "申报异常", e);
            }
        });
    }

    /**
     * declarationConfirmation
     *
     * @param unitId    unitId
     * @param date      date
     * @param loginInfo loginInfo
     */
    public void agreeConfirmation(String unitId, String date, LoginInfo loginInfo) {
        var businessName = "协议列表确认";
        log.info("开始 {}, unitId: {}, date: {}", businessName, unitId, date);
        String respStr;
        try (var response = sgccRequest
            .getPostRequest("/px-psgcc-spotgoods-extranet/dayAheadTradeDeclare/getDeclarationConfirmation",
                JSONObject.of("confirmationTime", date).toString(), loginInfo)
            .header("Currentroute", currentRoute)
            .execute()) {
            respStr = response.body();
        }
        log.info("{} 列表: {}", businessName, respStr);
        var jsonObject = JSONObject.parseObject(respStr);
        var jsonArray = jsonObject.getJSONArray("data");
        jsonArray.forEach(i -> {
            var type = JSONObject.parseObject(i.toString()).get("pledgetemplateType").toString();
            var version = JSONObject.parseObject(i.toString()).get("version").toString();
            String respStr1;
            try (var response = sgccRequest
                .getPostRequest("/px-psgcc-spotgoods-extranet/dayAheadTradeDeclare/insertConfirmation",
                    JSONObject.of(
                        "type", type,
                        "version", version,
                        "confirmationTime", date
                    ).toString(), loginInfo)
                .header("Currentroute", currentRoute)
                .execute()) {
                respStr1 = response.body();
            }
            log.info("{} 结果, type: {}, resp: {}", businessName, type, respStr1);
        });
    }

    /**
     * setLimit
     *
     * @param unitConfig unitConfig
     * @param timeConfig timeConfig
     * @param reportDay  reportDay
     * @param detailList detailList
     * @param loginInfo  loginInfo
     * @return boolean
     */
    public boolean setLimit(SystemUnitConfig unitConfig, SystemOpidTimeConfig timeConfig, LocalDateTime reportDay,
                            List<UserOpidConfigDetail> detailList, LoginInfo loginInfo) {
        var limitData = new JSONObject();
        if (queryLimit) {
            limitData = queryPowerLimit(unitConfig, detailList, timeConfig, reportDay, loginInfo);
            if (limitData == null) {
                log.info("{} 不存在申报限额", unitConfig.getUnitId());
                return false;
            }
        }
        var finalLimitData = limitData;
        detailList.forEach(detail -> {
            Double limitPower;
            if (queryLimit) {
                limitPower = calcLimitPower(reportDay.format(DATE_FORMAT), finalLimitData, detail);
            } else {
                limitPower = unitConfig.getLimitPower();
            }
            if (limitPower != null) {
                detail.setReportPower(Double.valueOf(detail.getPercent() * limitPower / 100).intValue());
                if (detail.getReportPower() == 0 && limitPower >= 1) {
                    // 折算后为零至少报 1
                    detail.setReportPower(1);
                }
            }
            detail.setPowerLimit(limitPower);
            configDetailRepo.updatePowerLimit(detail);
        });
        return true;
    }

    @Override
    public void updateReportPrice(List<UserOpidConfigDetail> detailList) {
        if (detailList == null || detailList.isEmpty()) {
            return;
        }
        log.info("更新自动策略的电价");
        var first = detailList.getFirst();
        if (first.getReportType() == 1) {
            for (UserOpidConfigDetail detail : detailList) {
                detail.setReportPrice(detail.getPrice().intValue());
            }
            configDetailRepo.saveAllAndFlush(detailList);
            return;
        }
        var unitId = first.getUnitId();
        var strategyDate = first.getStrategyDate();
        var profitAnalyses = dataProfitAnalysisRepo.findAllByParams(unitId, strategyDate, strategyDate);
        if (profitAnalyses.isEmpty()) {
            log.warn("未查询到省内日前电价：{}, {}", unitId, strategyDate);
            return;
        }
        var firstData = profitAnalyses.getFirst();
        var prices = JSONArray.parseArray(firstData.getIpDayAheadPrice(), Double.class);
        if (prices == null || prices.size() != 96) {
            log.warn("未查询到省内日前电价：{}, {}", unitId, strategyDate);
            return;
        }
        var today = LocalDate.now().format(DATE_FORMAT);
        for (UserOpidConfigDetail detail : detailList) {
            var start = LocalDateTime.parse(today + " " + detail.getStartTime(), DATE_TIME_HH_MM_FORMAT);
            var end = LocalDateTime.parse(today + " " + detail.getEndTime(), DATE_TIME_HH_MM_FORMAT);
            var startOfDay = LocalDateTime.parse(today + " 00:15", DATE_TIME_HH_MM_FORMAT);
            var i = (int) Duration.between(startOfDay, start).abs().toMinutes() / 15;
            var j = (int) Duration.between(startOfDay, end).abs().toMinutes() / 15;
            if (i == j) {
                if (prices.get(i) != null) {
                    detail.setPrice((double) prices.get(i).intValue());
                }
            } else {
                // i,j in [0,95]
                var avg = prices.subList(i, j + 1).stream().collect(Collectors.averagingDouble(Double::doubleValue));
                detail.setPrice((double) avg.intValue());
            }
            if (detail.getPrice() < 0) {
                detail.setPrice(0D);
            }
            detail.setReportPrice(detail.getPrice() != null ? detail.getPrice().intValue() : null);
        }
        configDetailRepo.saveAllAndFlush(detailList);
    }

    /**
     * execDeclare
     *
     * @param unitId      unitId
     * @param details     details
     * @param loginInfo   loginInfo
     * @param orderNumber orderNumber
     * @return boolean
     */
    public boolean execDeclare(String unitId, List<UserOpidConfigDetail> details,
                               LoginInfo loginInfo, String orderNumber) {
        var detailList = details.stream()
                                .filter(dsc -> dsc.getReportPower() != null
                                    && dsc.getReportPower() > 0
                                    && dsc.getReportPrice() != null
                                    && dsc.getReportPrice() >= 0
                                    && dsc.getReportPrice() <= 3000)
                                .toList();
        if (details.stream().allMatch(detail -> detail.getPowerLimit() < 1)) {
            log.info("{} 申报限额全部为零", unitId);
            return true;
        }
        if (detailList.isEmpty()) {
            log.error("申报数量为零 或 申报金额超出范围，无法申报");
            return false;
        }
        var firstDetail = detailList.getFirst();
        var unitConfigOptional = unitService.findFirstByUnitId(OPID, unitId);
        if (unitConfigOptional.isEmpty()) {
            return false;
        }
        var unitConfig = unitConfigOptional.get();
        var tradeDate = firstDetail.getStrategyDate();
        var caseId = tradeDate.replace("-", "") + orderNumber;
        var bidPowerInfoList = new JSONArray(detailList.size());
        for (int i = 0; i < detailList.size(); i++) {
            var detail = detailList.get(i);
            var bidPowerInfo = new JSONObject();
            bidPowerInfo.put("bandNo", firstDetail.getStrategyType() == 3 ? 0 : 1);
            bidPowerInfo.put("caseId", caseId);
            bidPowerInfo.put("caseType", "固定");
            bidPowerInfo.put("endTime", detail.getEndTime().substring(0, 5));
            bidPowerInfo.put("power", detail.getReportPower());
            bidPowerInfo.put("powerMaxLimit", null);
            bidPowerInfo.put("powerMinLimit", null);
            bidPowerInfo.put("price", detail.getReportPrice());
            bidPowerInfo.put("startTime", detail.getStartTime().substring(0, 5));
            bidPowerInfo.put("tag", 0);
            bidPowerInfo.put("tradeDate", tradeDate);
            bidPowerInfo.put("tradePart", Integer.parseInt(orderNumber) + "");
            bidPowerInfo.put("tradeUnitId", unitConfig.getUnitId());
            bidPowerInfo.put("tradeUnitName", unitConfig.getUnitName());
            bidPowerInfo.put("_XID", "row_" + i);
            bidPowerInfoList.add(bidPowerInfo);
        }
        var body = new JSONObject();
        body.put("bidType", firstDetail.getStrategyType());
        body.put("caseId", caseId); // 202310130009
        body.put("caseType", "固定");
        body.put("dispatchUnitId", unitConfig.getDispatchId());
        body.put("limitFlag", queryLimit ? "on" : "ce");
        body.put("role", "卖方");
        body.put("tradeDate", tradeDate); // 2023-10-13
        body.put("tradeUnitId", unitConfig.getUnitId());
        body.put("tradeUnitType", unitConfig.getUnitId());
        body.put("bidPowerInfoList", bidPowerInfoList);
        paramProcess(body, unitConfig);
        try (var response = sgccRequest
            .getPostRequest("/px-psgcc-spotgoods-extranet/dayInTradeDeclare/saveBidPowerInfoInner",
                body.toString(), loginInfo)
            .header("Currentroute", currentRoute).execute()) {
            log.info("{}, 申报成功: {}", unitConfig.getUnitId(), response);
            // response 转 json 并取 status
            return 0 == JSONObject.parseObject(response.body()).getInteger("status");
        }
    }

    /**
     * paramProcess
     *
     * @param body       body
     * @param unitConfig unitConfig
     */
    public void paramProcess(JSONObject body, SystemUnitConfig unitConfig) {
        body.put("tradeUnitType", SystemUnitConfig.getTypeName(unitConfig.getType()));
    }

    /**
     * queryPowerLimit
     *
     * @param unitConfig unitConfig
     * @param detailList detailList
     * @param timeConfig timeConfig
     * @param reportDay  reportDay
     * @param loginInfo  loginInfo
     * @return JSONObject
     */
    public JSONObject queryPowerLimit(SystemUnitConfig unitConfig, List<UserOpidConfigDetail> detailList,
                                      SystemOpidTimeConfig timeConfig,
                                      LocalDateTime reportDay, LoginInfo loginInfo) {
        String businessName = "获取省间日内限额";
        log.info("开始 {}, unitId: {}, reportDay: {}", businessName, unitConfig.getUnitId(), reportDay);
        String respStr = newSgccRequest
            .post("/px-psgcc-spotgoods-extranet/dayInTradeDeclare/queryPowerLimit",
                JSONObject.of(
                    "unitId", detailList.getFirst().getDispatchId(),
                    "caseId", timeConfig.getOrderNumber(),
                    "tradeDate", reportDay.format(DATE_FORMAT)
                ), ReqConfig.of(loginInfo, currentRoute));
        log.info("{} unitId: {}, 结果: {}", businessName, unitConfig.getUnitId(), respStr);
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var object = SgccResp.jsonData(sgccResp);
        if (object.isEmpty()) {
            log.info("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, object);
        return object;
    }

    @Async
    @Override
    public void sync(String unitId) {
        String strategyTime;
        try {
            Thread.sleep((long) (1000 * Math.random() * 50));
            strategyTime = HttpUtil.get(syncUrl, Map.of("unitId", unitId), 1000 * 60);
            if (strategyTime != null) {
                strategyTime = strategyTime.replace("\"", "");
            }
            var ignored = LocalDate.parse(Objects.requireNonNull(strategyTime), DATE_FORMAT);
        } catch (Exception e) {
            strategyTime = LocalDate.now().atTime(0, 0).format(DATE_TIME_FORMAT);
        }
        try {
            var details = configDetailRepo.findAllByUnitIdAndStrategyDateGreaterThanEqual(unitId, strategyTime);
            var jsonObject = new JSONObject();
            jsonObject.put("details", details);
            var encrypt = EncryptUtil.encrypt(jsonObject.toJSONString());
            try (HttpResponse execute = HttpUtil.createPost(syncUrl)
                                                .body(encrypt, "text/plain")
                                                .timeout(30000)
                                                .execute()) {
                var body = execute.body();
                log.info("{} sync {}", unitId, "1".equals(body));
            }
        } catch (HttpException e) {
            log.error("sync error", e);
        }
    }
}
