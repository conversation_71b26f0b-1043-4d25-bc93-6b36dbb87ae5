package cn.com.sgcc.job.service.opid;

import static cn.com.sgcc.constants.DateConsts.DATE_TIME_HH_MM_FORMAT;

import cn.com.sgcc.business.sgcc.opid.model.UserOpidConfigDetail;
import com.alibaba.fastjson2.JSONObject;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

public interface OpidReportJobService {

    /**
     * getSupportedProv
     *
     * @return List<String>
     */
    List<String> getSupportedProv();

    /**
     * doDeclare
     */
    void doDeclare();

    /**
     * spotNow
     *
     * @param force force
     */
    void doDeclareAll(boolean force);

    /**
     * setReportPrice
     *
     * @param detailList detailList
     */
    void updateReportPrice(List<UserOpidConfigDetail> detailList);

    /**
     * sync
     *
     * @param unitId unitId
     */
    void sync(String unitId);

    /**
     * calcLimitPower
     *
     * @param today     today
     * @param limitData limitData
     * @param dsc       dsc
     * @return Double
     */
    default Double calcLimitPower(String today, JSONObject limitData, UserOpidConfigDetail dsc) {
        LocalDateTime begin = LocalDateTime.parse(today + " " + dsc.getStartTime(), DATE_TIME_HH_MM_FORMAT);
        LocalDateTime end = LocalDateTime.parse(today + " " + dsc.getEndTime(), DATE_TIME_HH_MM_FORMAT);
        LocalDateTime start = LocalDateTime.parse(today + " 00:00", DATE_TIME_HH_MM_FORMAT);
        int jg1 = (int) Duration.between(start, end).abs().toMinutes() / 15;
        int jg2 = (int) Duration.between(begin, end).abs().toMinutes() / 15;
        double limitPower = 0D;
        boolean allNull = true;
        // 根据 jg2 循环
        for (int i = 0; i <= jg2; i++) {
            int idx = jg1 + i;
            // 如果idx < 10 则前面补 0
            String idxStr = (idx < 10) ? "0" + idx : idx + "";
            Double v = limitData.getDouble("h" + idxStr);
            if (v != null) {
                allNull = false;
                limitPower += v;
            }
        }
        if (allNull) {
            return null;
        }
        return limitPower;
    }
}
