package cn.com.sgcc.job.service.opbd;

import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.constants.ProvConsts;
import cn.com.sgcc.util.SgccResp;
import cn.com.sgcc.util.http.NewSgccRequest;
import cn.com.sgcc.util.http.ReqConfig;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OpbdReportJobServiceJibei extends OpbdReportJobServiceDefault {

    @Resource
    private NewSgccRequest newSgccRequest;

    @Value("${custom.opbd.range.maxDay}")
    int maxDay;

    @Override
    public List<String> getSupportedProv() {
        return List.of(ProvConsts.Prov.jibei.name());
    }

    @Override
    public boolean checkBeforeSchedule(String strategyDate, LocalDateTime declareDateTime) {
        var equal = declareDateTime.toLocalDate().isEqual(LocalDate.now());
        if (!equal) {
            log.info("不是当天申报，忽略此任务: {} -> {}", strategyDate, declareDateTime);
        }
        return equal;
    }

    @Override
    public List<String> getReportDays(LoginInfo loginInfo) {
        var days = queryReportableTime(loginInfo);
        if (days != null && days.size() > maxDay) {
            days = days.subList(0, maxDay);
        }
        return days;
    }

    /**
     * getLimitPowerForJibei
     *
     * @param unitConfig   unitConfig
     * @param strategyDate strategyDate
     * @param loginInfo    loginInfo
     * @return JSONObject
     */
    @Override
    public JSONObject getLimitPower(SystemUnitConfig unitConfig, String strategyDate, LoginInfo loginInfo) {
        String businessName = "获取省间日前限额";
        log.info("开始 {}, unitId: {}, date: {}", businessName, unitConfig.getUnitId(), strategyDate);
        var unitId = unitConfig.getUnitId();
        // 调用限额接口
        String respStr = newSgccRequest
                .post("/px-forum-jibei-out/forum/spotDisclosure/query96PointsBarThreeDataIntraFront",
                      JSONObject.of(
                              "businessTime", strategyDate,
                              "caseId", "",
                              "unit", unitConfig.getDispatchName()
                      ), ReqConfig.of(loginInfo, "/pxf-forum-jibei-out/potDisclosureintraFront"));
        log.info("{} 查询限额结果: {}", unitId, respStr);
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var array = SgccResp.arrayData(sgccResp);
        if (array.size() != 96) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, array);
        JSONObject limitData = new JSONObject();
        for (int i = 0; i < array.size(); i++) {
            limitData.put("h%02d".formatted(i + 1), array.get(i));
        }
        return limitData;
    }
}
