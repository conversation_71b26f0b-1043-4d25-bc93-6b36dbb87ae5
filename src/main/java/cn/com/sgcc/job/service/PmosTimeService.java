package cn.com.sgcc.job.service;

import cn.com.sgcc.business.sgcc.autologin.service.LoginService;
import cn.com.sgcc.constants.ProvConsts;
import cn.com.sgcc.util.SgccResp;
import cn.com.sgcc.util.http.NewSgccRequest;
import cn.com.sgcc.util.http.ReqConfig;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.LinkedList;
import java.util.concurrent.atomic.AtomicLong;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * PMOS服务器时间同步服务 (极简版 + 快速适应跳变)
 * 使用限长队列记录10分钟内延迟最小的测量值，直接用最小延迟值计算时间偏移。
 * 当检测到时间跳变时，清空队列并重新开始收集数据。
 * 丢弃极端延迟的测量。
 */
@Slf4j
@Service
public class PmosTimeService {

    @Resource
    private LoginService loginService;
    @Resource
    private NewSgccRequest newSgccRequest;

    // --- 配置项 ---

    @Value("${custom.pmos.provinceCode}")
    String provinceCode;

    // 丢弃测量结果的延迟阈值 (ms) - 延迟超过此值的测量将被忽略
    @Value("${custom.time-sync.latency.threshold.discard:500}")
    private long latencyThresholdDiscard;

    // 检测到当前测量偏移量与当前偏移量之间存在巨大跳变的阈值 (ms)
    @Value("${custom.time-sync.jump.adapt.threshold.millis:1000}") // 例如, 1 秒
    private long jumpAdaptThresholdMillis;

    // 队列中保存的最大记录数量
    @Value("${custom.time-sync.queue.max-size:20}")
    private int queueMaxSize;

    // 队列中记录的最大有效期（分钟）
    @Value("${custom.time-sync.queue.max-age-minutes:10}")
    private int queueMaxAgeMinutes;

    // --- 内部状态 ---

    // 当前使用的时间偏移量 (ms)，原子操作保证线程安全
    private final AtomicLong timeOffsetMillis = new AtomicLong(0L);
    // 存储最近测量结果的队列，包含时间戳、延迟和计算的偏移量
    private final LinkedList<MeasurementRecord> recentMeasurements = new LinkedList<>();

    /**
     * 测量记录类，存储单次测量的结果
     */
    private record MeasurementRecord(LocalDateTime timestamp, long latency, long offset) {
    }

    // --- 公共 API ---

    /**
     * 获取当前估计的PMOS服务器时间 (LocalDateTime)
     *
     * @return now
     */
    public LocalDateTime getCurrentPmosTime() {
        return LocalDateTime.now().plus(timeOffsetMillis.get(), ChronoUnit.MILLIS);
    }

    /**
     * 获取当前估计的PMOS服务器时间 (LocalDateTime)
     *
     * @return now
     */
    public long getPmosTimeDiff() {
        return timeOffsetMillis.get();
    }

    // --- 定时同步任务 ---

    /**
     * syncTimeWithPmosServer
     */
    @Async
    public void syncTimeWithPmosServer() {
        log.debug("启动简化版时间同步任务...");
        long latency = -1; // 初始化延迟
        try {
            var loginInfoOptional = loginService.getOne();
            // 1. 发送 HTTP POST 请求获取 PMOS 服务器时间
            String respStr;
            long timeStart = System.currentTimeMillis();
            if (loginInfoOptional.isPresent()) {
                respStr = newSgccRequest.post("/px-gateway/systemTime/getSystemTime", JSONObject.of(),
                    ReqConfig.of(loginInfoOptional.get(), "/dashboard"));
            } else {
                var origin = ProvConsts.Prov.valueOf(provinceCode).getOrigin();
                try (var response = HttpUtil.createPost(origin + "/px-gateway/systemTime/getSystemTime")
                                            .header("currentroute", "/outNet")
                                            .body(JSONObject.of("captchaType", "blockPuzzle").toString())
                                            .timeout(5000)
                                            .execute()) {
                    respStr = response.header("Date");
                }
            }
            long timeEnd = System.currentTimeMillis();
            latency = timeEnd - timeStart; // 计算本次请求延迟

            // 2. 如果延迟过高，则丢弃本次测量
            if (latency >= latencyThresholdDiscard) {
                log.warn("时间同步延迟 ({}ms) 超过丢弃阈值 ({}ms)，跳过本次更新。",
                    latency, latencyThresholdDiscard);
                return; // 不再继续处理
            }

            // 3. 解析 PMOS 返回的响应
            long pmosServerTimeMillis = -1; // 获取 PMOS 服务器时间戳
            if (loginInfoOptional.isPresent()) {
                JSONObject sgccResp = JSONObject.parseObject(respStr);
                if (SgccResp.ok(sgccResp)) { // 检查响应是否成功
                    pmosServerTimeMillis = SgccResp.longData(sgccResp);
                }
            } else {
                pmosServerTimeMillis = ZonedDateTime.parse(respStr, DateTimeFormatter.RFC_1123_DATE_TIME)
                                                    .toInstant()
                                                    .toEpochMilli();
            }

            if (pmosServerTimeMillis != -1) { // 检查响应是否成功
                // 4. 计算本次测量得到的原始时间偏移量
                // 估计请求到达服务器时的本地时间 = 请求开始时间 + 网络延迟的一半 (简化假设)
                long estimatedLocalTimeAtServer = timeStart + (latency / 2);
                long currentOffset = pmosServerTimeMillis - estimatedLocalTimeAtServer;

                // 5. 更新时间偏移量，内部会处理跳变适应
                updateTimeOffset(currentOffset, latency);

                log.info("时间同步成功。延迟: {}ms, 本次偏移: {}ms, 当前使用偏移: {}ms",
                    latency, currentOffset, timeOffsetMillis.get());

            } else {
                // PMOS API 返回非成功状态
                log.warn("PMOS 时间接口返回非成功响应: {}. 未更新偏移量。", respStr);
            }
        } catch (Exception e) {
            // 处理网络、解析等异常
            String latencyMsg = (latency != -1) ? " (延迟: " + latency + "ms)" : ""; // 如果已测量到延迟，则附加到日志
            log.error("时间同步失败{}: {}", latencyMsg, e.getMessage()); // 记录错误
        }
    }

    // --- 内部辅助方法 ---

    /**
     * 更新时间偏移量。
     * 将新的测量结果添加到队列中，并根据队列中延迟最小的记录更新时间偏移量。
     * 如果检测到时间跳变，则清空队列并重新开始。
     *
     * @param currentOffset  根据当前测量计算出的时间偏移量。
     * @param currentLatency 当前测量的网络延迟 (ms)。
     */
    private void updateTimeOffset(long currentOffset, long currentLatency) {
        LocalDateTime now = LocalDateTime.now();
        long previousOffset = timeOffsetMillis.get(); // 获取当前的偏移量
        long offsetDifference = Math.abs(currentOffset - previousOffset); // 计算新旧偏移量的绝对差值

        // 检测到跳变时，清空队列并直接使用当前值
        if (offsetDifference > jumpAdaptThresholdMillis) {
            log.warn("检测到时间偏移量剧烈变化 (差异 {}ms > 阈值 {}ms)。旧值: {}ms, 新测量值: {}ms。清空队列重新开始。",
                offsetDifference, jumpAdaptThresholdMillis, previousOffset, currentOffset);
            recentMeasurements.clear(); // 清空队列

            // 添加新记录到队列
            recentMeasurements.add(new MeasurementRecord(now, currentLatency, currentOffset));

            // 直接使用当前偏移量
            timeOffsetMillis.set(currentOffset);
            log.debug("首次同步或检测到跳变，直接使用当前偏移量: {}ms", currentOffset);
            return;
        }

        // 移除过期的记录（超过指定时间）
        removeExpiredRecords(now);

        // 添加新记录到队列
        recentMeasurements.add(new MeasurementRecord(now, currentLatency, currentOffset));

        // 如果队列超过最大大小，移除最旧的记录
        while (recentMeasurements.size() > queueMaxSize) {
            recentMeasurements.removeFirst();
        }

        // 找出队列中延迟最小的记录
        MeasurementRecord bestRecord = findMinLatencyRecord();

        if (bestRecord != null) {
            // 使用延迟最小的记录的偏移量
            timeOffsetMillis.set(bestRecord.offset());
            log.debug("更新时间偏移量。使用延迟最小的记录 (延迟={}ms) 的偏移量: {}ms",
                bestRecord.latency(), bestRecord.offset());
        }
    }

    /**
     * 移除队列中过期的记录（超过指定时间）
     *
     * @param now 当前时间
     */
    private void removeExpiredRecords(LocalDateTime now) {
        LocalDateTime cutoffTime = now.minusMinutes(queueMaxAgeMinutes);
        recentMeasurements.removeIf(record -> record.timestamp().isBefore(cutoffTime));
    }

    /**
     * 在队列中找出延迟最小的记录
     *
     * @return 延迟最小的记录，如果队列为空则返回null
     */
    private MeasurementRecord findMinLatencyRecord() {
        if (recentMeasurements.isEmpty()) {
            return null;
        }

        MeasurementRecord minRecord = recentMeasurements.getFirst();
        for (MeasurementRecord record : recentMeasurements) {
            if (record.latency() < minRecord.latency()) {
                minRecord = record;
            }
        }

        return minRecord;
    }
}
