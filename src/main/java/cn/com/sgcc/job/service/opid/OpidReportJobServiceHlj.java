package cn.com.sgcc.job.service.opid;

import static cn.com.sgcc.business.common.model.UserConfigEnum.HLJ_OPID_LAST_ALL_DECLARE;
import static cn.com.sgcc.business.common.model.UserConfigEnum.HLJ_OPID_TIME_CONFIG;
import static cn.com.sgcc.constants.DateConsts.DATE_FORMAT;
import static cn.com.sgcc.constants.DateConsts.DATE_TIME_FORMAT;
import static cn.com.sgcc.constants.DateConsts.TIME_FORMAT;
import static cn.com.sgcc.constants.RoleConstants.Type.OPID;

import cn.com.sgcc.business.common.service.UserConfigService;
import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.sgcc.opid.model.SystemOpidTimeConfig;
import cn.com.sgcc.business.sgcc.opid.model.UserOpidConfigDetail;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.constants.ProvConsts;
import cn.com.sgcc.util.SgccResp;
import cn.com.sgcc.util.http.ReqConfig;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OpidReportJobServiceHlj extends OpidReportJobServiceDefault {
    @Resource
    UserConfigService configService;
    @Resource
    TaskScheduler taskScheduler;

    String currentRoute = "/pxf-psgcc-spotgoods-extranet/day-trade";

    @Override
    public List<String> getSupportedProv() {
        return List.of(ProvConsts.Prov.hlj.name());
    }

    @Override
    @Async
    public void doDeclareAll(boolean force) {
        var timeConfig = configService.get(HLJ_OPID_TIME_CONFIG);
        if (!force && timeConfig == null) {
            return;
        }
        var declareTime = LocalDate.now().atTime(LocalTime.parse(timeConfig, TIME_FORMAT));
        if (!force && declareTime.isBefore(LocalDateTime.now())) {
            log.info("任务执行时间: {}已经过了当前时间，忽略此任务", declareTime.format(DATE_TIME_FORMAT));
            configService.set(HLJ_OPID_LAST_ALL_DECLARE, "%s_2".formatted(declareTime.format(DATE_FORMAT)));
            return;
        }
        log.info("新建D+1定时申报任务，任务执行时间: {}", declareTime.format(DATE_TIME_FORMAT));
        taskScheduler.schedule(() -> {
            try {
                declareAll();
                configService.set(HLJ_OPID_LAST_ALL_DECLARE, "%s_1".formatted(declareTime.format(DATE_FORMAT)));
            } catch (Exception e) {
                configService.set(HLJ_OPID_LAST_ALL_DECLARE, "%s_2".formatted(declareTime.format(DATE_FORMAT)));
            }
        }, declareTime.toInstant(ZoneOffset.ofHours(8)));
    }

    /**
     * declareAll
     */
    public void declareAll() {
        log.info("开始进行D+1申报");
        var now = LocalDateTime.now();
        var tradeTimeConfigs = timeConfigRepo.findAll();
        var tradeTimeMap = tradeTimeConfigs
            .stream()
            .collect(Collectors.toMap(SystemOpidTimeConfig::getTradeTimePart, Function.identity()));
        var unitMap = unitService.getUnitMap(OPID);
        var d1 = now.plusDays(1);
        var needStrategyConfigDetailList = configDetailRepo.findAllNotSuccess(d1.format(DATE_FORMAT), "00:00", "24:00")
                                                           .stream()
                                                           .filter(i -> i.getStatus() != 1
                                                                        && i.getPercent() != null
                                                                        && i.getPercent() != 0)
                                                           .toList();
        if (needStrategyConfigDetailList.isEmpty()) {
            log.info("没有需要执行的申报");
            return;
        }
        var loginMap = needStrategyConfigDetailList
            .stream()
            .map(UserOpidConfigDetail::getUnitId)
            .distinct()
            .parallel()
            .map(unitId -> loginService.login(unitId, false))
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(LoginInfo::getPlantId, Function.identity(), (o1, o2) -> o1));
        if (loginMap.isEmpty()) {
            log.info("没有登录成功的账号");
            return;
        }
        needStrategyConfigDetailList
            .stream()
            .collect(Collectors.groupingBy(UserOpidConfigDetail::getUnitId))
            .forEach((unitId, list) -> {
                var listMap = list.stream()
                                  .collect(Collectors.groupingBy(UserOpidConfigDetail::getTimePart));
                for (Map.Entry<Integer, List<UserOpidConfigDetail>> entry : listMap.entrySet()) {
                    var timePart = entry.getKey();
                    var detailList = entry.getValue();
                    var timeConfig = tradeTimeMap.get((long) (timePart + 1));
                    if (timeConfig == null) {
                        log.info("没有找到配置: {}", timePart);
                        continue;
                    }
                    var unitConfig = unitMap.get(unitId);
                    var loginInfo = loginMap.get(unitConfig.getPlantId());
                    var declareDate = queryDeclareDateWithinDays(unitConfig, timeConfig, loginInfo);
                    if (!"00:00:01".equals(declareDate.getString("bidStartTime"))) {
                        log.warn("{}, 无法执行申报, 开始申报限制时间已变更", unitConfig.getUnitId());
                        break;
                    }

                    agreeConfirmation(unitId, now.format(DATE_FORMAT), loginInfo);
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.error("异常中断", e);
                    }
                    if (!setLimit(unitConfig, timeConfig, d1, detailList, loginInfo)) {
                        return;
                    }
                    updateReportPrice(detailList);
                    if (execDeclare(unitId, detailList, loginInfo, timeConfig.getOrderNumber())) {
                        detailList.forEach(detail -> {
                            detail.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
                            detail.setStatus(1);
                        });
                        configDetailRepo.saveAllAndFlush(detailList);
                    }
                }
            });
    }

    /**
     * queryPowerLimit
     *
     * @param unitConfig unitConfig
     * @param detailList detailList
     * @param timeConfig timeConfig
     * @param reportDay  reportDay
     * @param loginInfo  loginInfo
     * @return JSONObject
     */
    @Override
    public JSONObject queryPowerLimit(SystemUnitConfig unitConfig, List<UserOpidConfigDetail> detailList,
                                      SystemOpidTimeConfig timeConfig,
                                      LocalDateTime reportDay, LoginInfo loginInfo) {
        String businessName = "获取省间日内限额";
        log.info("开始 {}, unitId: {}, reportDay: {}", businessName, unitConfig.getUnitId(), reportDay);
        String respStr = newSgccRequest
            .post("/px-psgcc-spotgoods-extranet/dayInTradeDeclare/queryPowerLimit",
                JSONObject.of(
                    "unitId", detailList.getFirst().getDispatchId(),
                    "caseId", timeConfig.getOrderNumber(),
                    "tradeDate", reportDay.format(DATE_FORMAT)
                ), ReqConfig.of(loginInfo, currentRoute));
        log.info("{} unitId: {}, 结果: {}", businessName, unitConfig.getUnitId(), respStr);
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var object = SgccResp.jsonData(sgccResp);
        if (object.isEmpty()) {
            log.info("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, object);
        return object;
    }

    /**
     * queryDeclareDateWithinDays
     *
     * @param unitConfig unitConfig
     * @param timeConfig timeConfig
     * @param loginInfo  loginInfo
     * @return JSONObject
     */
    public JSONObject queryDeclareDateWithinDays(SystemUnitConfig unitConfig, SystemOpidTimeConfig timeConfig,
                                                 LoginInfo loginInfo) {
        String businessName = "获取省间日内申报时间范围";
        log.info("开始 {}, unitId: {}, caseId: {}", businessName, unitConfig.getUnitId(), timeConfig.getOrderNumber());
        String respStr = newSgccRequest
            .post("/px-psgcc-spotgoods-extranet/dayInTradeDeclare/queryDeclareDateWithinDays",
                JSONObject.of(
                    "caseId", timeConfig.getOrderNumber()
                ), ReqConfig.of(loginInfo, currentRoute));
        log.info("{} unitId: {}, 结果: {}", businessName, unitConfig.getUnitId(), respStr);
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var object = SgccResp.jsonData(sgccResp);
        if (object.isEmpty()) {
            log.info("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, object);
        return object;
    }

    @Override
    public void agreeConfirmation(String unitId, String date, LoginInfo loginInfo) {
    }
}
