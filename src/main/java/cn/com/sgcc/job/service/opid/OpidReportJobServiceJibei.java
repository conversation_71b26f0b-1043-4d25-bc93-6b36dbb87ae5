package cn.com.sgcc.job.service.opid;

import static cn.com.sgcc.constants.DateConsts.DATE_FORMAT;

import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.sgcc.opid.model.SystemOpidTimeConfig;
import cn.com.sgcc.business.sgcc.opid.model.UserOpidConfigDetail;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.constants.ProvConsts;
import cn.com.sgcc.util.SgccResp;
import cn.com.sgcc.util.http.ReqConfig;
import com.alibaba.fastjson2.JSONObject;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OpidReportJobServiceJibei extends OpidReportJobServiceDefault {

    @Override
    public List<String> getSupportedProv() {
        return List.of(ProvConsts.Prov.jibei.name());
    }

    /**
     * getLimitPowerForJibei
     *
     * @param unitConfig unitConfig
     * @param detailList detailList
     * @param timeConfig timeConfig
     * @param reportDay  reportDay
     * @param loginInfo  loginInfo
     * @return JSONObject
     */
    @Override
    public JSONObject queryPowerLimit(SystemUnitConfig unitConfig, List<UserOpidConfigDetail> detailList,
                                      SystemOpidTimeConfig timeConfig, LocalDateTime reportDay, LoginInfo loginInfo) {
        var date = reportDay.format(DATE_FORMAT);
        var businessName = "获取省间日内限额";
        log.info("开始 {}, unitId: {}, date: {}", businessName, unitConfig.getUnitId(), date);
        var castIdList = getCastIdList(unitConfig, date, loginInfo);
        var orderNumber = timeConfig.getOrderNumber();
        if (castIdList == null) {
            return null;
        }
        var castId = date.replace("-", "") + orderNumber;
        if (!castIdList.contains(castId)) {
            log.error("{} 为空: 不存在限额数据", businessName);
            return null;
        }
        var respStr = newSgccRequest
                .post("/px-forum-jibei-out/forum/spotDisclosure/query96PointsBarThreeDataIntraDay",
                      JSONObject.of(
                              "businessTime", date,
                              "caseId", castId,
                              "unit", unitConfig.getDispatchName()
                      ), ReqConfig.of(loginInfo, "/pxf-forum-jibei-out/SpotDisclosureintraDay"));
        log.info("{} 查询限额结果: {}", unitConfig.getUnitId(), respStr);
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var array = SgccResp.arrayData(sgccResp);
        if (array.size() != 96) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, array);
        JSONObject limitData = new JSONObject();
        for (int i = 0; i < array.size(); i++) {
            limitData.put("h%02d".formatted(i + 1), array.get(i));
        }
        return limitData;
    }

    /**
     * getLimitPowerListForJibei
     *
     * @param unitConfig unitConfig
     * @param date       date
     * @param loginInfo  loginInfo
     * @return List<String>
     */
    private List<String> getCastIdList(SystemUnitConfig unitConfig, String date, LoginInfo loginInfo) {
        var businessName = "获取省间日内限额列表";
        log.info("开始 {}, unitId: {}, date: {}", businessName, unitConfig.getUnitId(), date);
        var respStr = newSgccRequest
                .post("/px-forum-jibei-out/forum/spotDisclosure/queryIntraDayUnitCaseIds",
                      JSONObject.of("businessTime", date),
                      ReqConfig.of(loginInfo, "/pxf-forum-jibei-out/SpotDisclosureintraDay"));
        log.info("{} 查询限额结果: {}", unitConfig.getUnitId(), respStr);
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var array = SgccResp.arrayData(sgccResp);
        if (array.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        return array.stream()
                    .map(it -> ((JSONObject) it).getString("caseId"))
                    .toList();
    }
}
