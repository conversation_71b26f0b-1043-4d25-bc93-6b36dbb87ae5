package cn.com.sgcc.job.service;

import static cn.com.sgcc.constants.DateConsts.DATE_TIME_FORMAT;
import static cn.com.sgcc.constants.DateConsts.SGCC_DATE_TIME_FORMAT_UTC;
import static cn.com.sgcc.constants.RoleConstants.Type.PROFIT_ANALYSIS;
import static cn.com.sgcc.util.TimeUtil.FIFTEEN_MINUTE_MAP_REV;

import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.sgcc.autologin.service.LoginService;
import cn.com.sgcc.business.sgcc.opbd.repository.UserOpbdConfigDetailRepo;
import cn.com.sgcc.business.sgcc.opid.repository.UserOpidConfigDetailRepo;
import cn.com.sgcc.business.sgcc.profitanalysis.model.DataProfitAnalysis;
import cn.com.sgcc.business.sgcc.profitanalysis.repository.DataProfitAnalysisRepo;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.constants.ProvConsts;
import cn.com.sgcc.util.SgccResp;
import cn.com.sgcc.util.http.NewSgccRequest;
import cn.com.sgcc.util.http.ReqConfig;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ProfitAnalysisJobService {

    @Resource
    UnitService unitService;
    @Resource
    LoginService loginService;
    @Resource
    NewSgccRequest newSgccRequest;
    @Resource
    UserOpidConfigDetailRepo opidConfigDetailRepo;
    @Resource
    UserOpbdConfigDetailRepo opbdConfigDetailRepo;
    @Resource
    DataProfitAnalysisRepo repo;

    @Value("${custom.pmos.provinceCode}")
    String provinceCode;

    /**
     * crawler
     *
     * @param unitId   unitId
     * @param date     date
     * @param onlyOpid onlyOpid
     */
    public void crawler(String unitId, String date, boolean onlyOpid) {
        var unitConfigOptional = unitService.findFirstByUnitId(PROFIT_ANALYSIS, unitId);
        if (unitConfigOptional.isEmpty()) {
            log.info("交易单元不存在: {}", unitId);
            return;
        }
        var unitConfig = unitConfigOptional.get();
        var loginInfo = loginService.login(unitId, false);
        log.info("开始爬取出清数据: {}, {}", unitId, date);
        var profitAnalysis = new DataProfitAnalysis();
        var ipDayAheadPriceArray = new JSONArray();
        var ipRealTimePriceArray = new JSONArray();
        var opbdReportPowerArray = new JSONArray();
        var opbdBidPowerArray = new JSONArray();
        var opbdDayAheadBidPriceArray = new JSONArray();
        var opidReportPowerArray = new JSONArray();
        var opidBidPowerArray = new JSONArray();
        var opidRealTimeBidPriceArray = new JSONArray();
        for (int i = 0; i < 96; i++) {
            ipDayAheadPriceArray.add(null);
            ipRealTimePriceArray.add(null);
            opbdReportPowerArray.add(null);
            opbdBidPowerArray.add(null);
            opbdDayAheadBidPriceArray.add(null);
            opidReportPowerArray.add(null);
            opidBidPowerArray.add(null);
            opidRealTimeBidPriceArray.add(null);
        }
        {
            // 省内
            var jsonObject = queryGenerateCurve(unitConfig, date, loginInfo, profitAnalysis);
            if (jsonObject != null) {
                var dayBeforeArray = jsonObject.getJSONArray("dayBefore");
                var realTimeArray = jsonObject.getJSONArray("realTime");
                if (dayBeforeArray != null && !dayBeforeArray.isEmpty()) {
                    dayBeforeArray.stream()
                                  .map(o -> (JSONObject) o)
                                  .forEach(o -> {
                                      var timeIndex = o.getInteger("timeIndex");
                                      // 黑龙江没有返回 timeIndex
                                      if (timeIndex == null) {
                                          var timeEndStr = o.getString("timeEnd");
                                          var timeEnd = LocalDateTime.parse(timeEndStr, SGCC_DATE_TIME_FORMAT_UTC);
                                          timeEnd = timeEnd.plusHours(8);
                                          timeIndex = timeEnd.getHour() * 4 + timeEnd.getMinute() / 15 - 1;
                                      }
                                      ipDayAheadPriceArray
                                              .set(timeIndex - 1, o.getDouble("marginPrice"));
                                  });
                }
                if (realTimeArray != null && !realTimeArray.isEmpty()) {
                    realTimeArray.stream()
                                 .map(o -> (JSONObject) o)
                                 .forEach(o -> ipRealTimePriceArray
                                         .set(o.getInteger("timeIndex") - 1, o.getDouble("marginPrice")));
                }
            }
        }
        if (!onlyOpid) {
            // 省间日前
            var allSucceed = opbdConfigDetailRepo.findAllSucceed(unitId, date);
            allSucceed.forEach(o -> opbdReportPowerArray
                    .set(FIFTEEN_MINUTE_MAP_REV.get(o.getStartTime()), o.getReportPower()));
            var array = queryElectricityAndPrice(unitConfig, date, loginInfo, "08", profitAnalysis);
            if (array != null) {
                array.stream().map(o -> (JSONObject) o).forEach(o -> {
                    opbdBidPowerArray.set(FIFTEEN_MINUTE_MAP_REV.get(o.getString("period")), o.getDouble("power"));
                    opbdDayAheadBidPriceArray
                            .set(FIFTEEN_MINUTE_MAP_REV.get(o.getString("period")), o.getDouble("price"));
                });
            }
        }
        if (!onlyOpid) {
            // 省间日内
            var allSucceed = opidConfigDetailRepo.findAllSucceed(unitId, date);
            allSucceed.forEach(o -> opidReportPowerArray
                    .set(FIFTEEN_MINUTE_MAP_REV.get(o.getStartTime()), o.getReportPower()));
            var array = queryElectricityAndPrice(unitConfig, date, loginInfo, "09", profitAnalysis);
            if (array != null) {
                array.stream().map(o -> (JSONObject) o).forEach(o -> {
                    opidBidPowerArray.set(FIFTEEN_MINUTE_MAP_REV.get(o.getString("period")), o.getDouble("power"));
                    opidRealTimeBidPriceArray
                            .set(FIFTEEN_MINUTE_MAP_REV.get(o.getString("period")), o.getDouble("price"));
                });
            }
        }
        profitAnalysis.setUnitId(unitId);
        profitAnalysis.setDate(date);
        profitAnalysis.setIpDayAheadPrice(ipDayAheadPriceArray.toString());
        profitAnalysis.setIpRealTimePrice(ipRealTimePriceArray.toString());
        profitAnalysis.setOpbdReportPower(opbdReportPowerArray.toString());
        profitAnalysis.setOpbdBidPower(opbdBidPowerArray.toString());
        profitAnalysis.setOpbdDayAheadBidPrice(opbdDayAheadBidPriceArray.toString());
        profitAnalysis.setOpidReportPower(opidReportPowerArray.toString());
        profitAnalysis.setOpidBidPower(opidBidPowerArray.toString());
        profitAnalysis.setOpidRealTimeBidPrice(opidRealTimeBidPriceArray.toString());
        profitAnalysis.setCreateTime(LocalDateTime.now().format(DATE_TIME_FORMAT));
        repo.saveAndFlush(profitAnalysis);
    }

    /**
     * queryGenerateCurve
     *
     * @param unitConfig     unitConfig
     * @param date           date
     * @param loginInfo      loginInfo
     * @param profitAnalysis profitAnalysis
     * @return JSONObject
     */
    private JSONObject queryGenerateCurve(SystemUnitConfig unitConfig, String date,
                                          LoginInfo loginInfo, DataProfitAnalysis profitAnalysis) {
        String businessName = "爬取省内现货交易结果";
        log.info("开始 {}, unitId: {}, date: {}", businessName, unitConfig.getUnitId(), date);
        var body = JSONObject.of(
                "appType", "1",
                "dayStr", date,
                "unitId", unitConfig.getGeneratorSetId()
        );
        if (ProvConsts.Prov.hlj.name().equals(provinceCode)) {
            // 黑龙江需要 040000 风电 050000 太阳能 020000 火电 010000 水电 900000 虚拟 030000 核电 200000 其它发电类型
            body.put("membersType", unitConfig.getType() == 0 ? "040000" : "050000");
        }
        var currentRoute = "/pxf-spotgoods-province-extranet"
                           + "/tradeResultSearchForCreateElectric/TradeResultSearchForCreateElectric";
        var respStr = newSgccRequest.post("/px-spotgoods-province/trade/queryGenerateCurve",
                                          body, ReqConfig.of(loginInfo, currentRoute));
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var jsonData = SgccResp.jsonData(sgccResp);
        if (jsonData.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        return jsonData;
    }

    /**
     * queryElectricityAndPrice
     *
     * @param unitConfig     unitConfig
     * @param date           date
     * @param loginInfo      loginInfo
     * @param tradeType      tradeType
     * @param profitAnalysis profitAnalysis
     * @return JSONArray
     */
    private JSONArray queryElectricityAndPrice(SystemUnitConfig unitConfig, String date, LoginInfo loginInfo,
                                               String tradeType, DataProfitAnalysis profitAnalysis) {
        String businessName = "爬取省间现货交易省内单元出清结果";
        log.info("开始 {}, unitId: {}, date: {}, tradeType: {}", businessName, unitConfig.getUnitId(), date, tradeType);
        String respStr = newSgccRequest.post(
                "/px-psgcc-spotgoods-extranet/SpotResultUnitClearPpController/queryElectricityAndPrice", JSONObject.of(
                        "dispatchUnitId", unitConfig.getDispatchId(),
                        "tradeDate", date,
                        "tradeType", tradeType,
                        "tradeUnitId", unitConfig.getUnitId()
                ), ReqConfig.of(loginInfo, "/pxf-psgcc-spotgoods-extranet/ProvinceInUnitClearingResult"));
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var array = SgccResp.arrayData(sgccResp);
        if (array.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, array);
        return array;
    }
}
