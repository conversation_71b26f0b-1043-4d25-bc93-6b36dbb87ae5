package cn.com.sgcc.job.service.opbd;

import static cn.com.sgcc.constants.RoleConstants.Type.OPBD;

import cn.com.sgcc.business.common.model.UserConfigEnum;
import cn.com.sgcc.business.common.service.UserConfigService;
import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.sgcc.autologin.service.LoginService;
import cn.com.sgcc.business.sgcc.opbd.model.UserOpbdConfig;
import cn.com.sgcc.business.sgcc.opbd.model.UserOpbdConfigDetail;
import cn.com.sgcc.business.sgcc.opbd.repository.UserOpbdConfigDetailRepo;
import cn.com.sgcc.business.sgcc.opbd.repository.UserOpbdConfigRepo;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.constants.DateConsts;
import cn.com.sgcc.util.SgccResp;
import cn.com.sgcc.util.TimeUtil;
import cn.com.sgcc.util.http.NewSgccRequest;
import cn.com.sgcc.util.http.ReqConfig;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * OpbdReportJobService
 */
@Slf4j
@Service
public class OpbdReportJobServiceDefault implements OpbdReportJobService {
    @Resource
    private TaskScheduler taskScheduler;
    @Resource
    private NewSgccRequest newSgccRequest;
    @Resource
    private UnitService unitService;
    @Resource
    private UserOpbdConfigRepo configRepo;
    @Resource
    private UserOpbdConfigDetailRepo configDetailRepo;
    @Resource
    private LoginService loginService;
    @Resource
    private UserConfigService configService;
    @Value("${custom.pmos.queryLimit}")
    boolean queryLimit;
    String currentRoute = "/pxf-psgcc-spotgoods-extranet/day-ahead-trade";

    @Override
    public List<String> getSupportedProv() {
        return List.of(OpbdReportJobContext.DEFAULT_TYPE);
    }

    @Async
    @Override
    public void detect() {
        var loginInfo = login(LocalDateTime.now().plusDays(1).format(DateConsts.DATE_FORMAT));
        if (loginInfo == null) {
            return;
        }
        List<String> days;
        days = getReportDays(loginInfo);
        if (days == null) {
            return;
        }
        days.forEach(strategyDate -> {
            var declareDateTime = queryDeclareDate(loginInfo, strategyDate);
            log.info("策略日期: {}, 申报时间: {}", strategyDate, declareDateTime);
            if (declareDateTime == null) {
                return;
            }
            if (!checkBeforeSchedule(strategyDate, declareDateTime)) {
                return;
            }
            log.info("新建定时申报任务，任务执行时间: {}", declareDateTime);
            taskScheduler.schedule(() -> report(strategyDate), declareDateTime.toInstant(ZoneOffset.ofHours(8)));
        });
    }

    /**
     * checkBeforeSchedule
     *
     * @param strategyDate    strategyDate
     * @param declareDateTime declareDateTime
     * @return boolean
     */
    public boolean checkBeforeSchedule(String strategyDate, LocalDateTime declareDateTime) {
        return true;
    }

    /**
     * login
     *
     * @param strategyDate strategyDate
     * @return LoginInfo
     */
    private LoginInfo login(String strategyDate) {
        log.info("获取登录信息");
        String unitId;
        var configList = configRepo.findAllByStrategyDate(strategyDate);
        if (!configList.isEmpty()) {
            unitId = configList.getFirst().getUnitId();
        } else {
            var unitList = unitService.getUnitList(OPBD);
            if (unitList.isEmpty()) {
                log.info("没有可以申报的交易单元");
                return null;
            }
            unitId = unitList.getFirst().getUnitId();
        }
        return loginService.login(unitId, false);
    }

    /**
     * queryDeclareDate
     *
     * @param loginInfo    loginInfo
     * @param strategyDate strategyDate
     * @return LocalDateTime
     */
    private LocalDateTime queryDeclareDate(LoginInfo loginInfo, String strategyDate) {
        log.info("获取申报开始结束时间");
        LocalDateTime declareDate = null;
        var respStr = newSgccRequest.post("/px-psgcc-spotgoods-extranet/dayAheadTradeDeclare/queryDeclareDate",
            JSONObject.of("tradeDate", strategyDate), ReqConfig.of(loginInfo, currentRoute));
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.info("获取申报时间范围失败, {}", respStr);
            return null;
        }
        var declareStartTimeStr = SgccResp.jsonData(sgccResp).getString("declareStartTime");
        var declareEndTimeStr = SgccResp.jsonData(sgccResp).getString("declareEndTime");
        if (declareEndTimeStr == null || declareEndTimeStr.length() < 19) {
            log.info("获取申报结束时间失败, {}", respStr);
            return null;
        }

        var declareEndTime = LocalDateTime.parse(declareEndTimeStr.substring(0, 19), DateConsts.DATE_TIME_FORMAT);
        if (declareStartTimeStr == null || declareStartTimeStr.length() < 19) {
            log.info("获取申报开始时间失败, {}", respStr);
        } else {
            var declareStartTime = LocalDateTime
                .parse(declareStartTimeStr.substring(0, 19), DateConsts.DATE_TIME_FORMAT);
            var duration = Duration.between(declareStartTime, declareEndTime);
            var durationMinutes = duration.toMinutes();
            var rangeEnd = Integer.parseInt(configService.get(UserConfigEnum.OPBD_RANGE_END));
            configService.set(UserConfigEnum.OPBD_RANGE_END_TIME, declareEndTime.format(DateConsts.TIME_FORMAT));
            if (durationMinutes < rangeEnd) {
                log.info("申报时间超出开始时间，取开始结束时间中间值");
                declareDate = declareStartTime.plusMinutes(durationMinutes / 2);
            } else {
                declareDate = declareEndTime.plusMinutes(-rangeEnd);
            }
        }
        return declareDate;
    }

    /**
     * report
     *
     * @param strategyDate strategyDate
     */
    private void report(String strategyDate) {
        log.info("开始申报: {}", strategyDate);
        var allConfigList = configRepo.findAllByStrategyDate(strategyDate);
        if (allConfigList.isEmpty()) {
            log.info("没有可以申报的交易单元");
        }
        var unitMap = unitService.getUnitMap(OPBD);
        var plantIds = allConfigList.stream()
                                    .map(UserOpbdConfig::getUnitId)
                                    .distinct()
                                    .filter(unitMap::containsKey)
                                    .map(unitMap::get)
                                    .map(SystemUnitConfig::getPlantId)
                                    .distinct()
                                    .toList();
        // 登录所有需要用到的账号
        plantIds.parallelStream().forEach(plantId -> loginService.loginByPlantId(plantId, false));
        var allNotSuccess = configDetailRepo.findAllNotSuccess(strategyDate);
        var configDetailMap = allNotSuccess.stream().collect(Collectors.groupingBy(UserOpbdConfigDetail::getUnitId));
        for (List<UserOpbdConfigDetail> configDetailList : configDetailMap.values()) {
            var detailMap = configDetailList.stream()
                                            .collect(Collectors.toMap(
                                                UserOpbdConfigDetail::getStartTime, Function.identity()));
            var unitId = configDetailList.getFirst().getUnitId();
            var firstByUnitId = unitService.findFirstByUnitId(OPBD, unitId);
            if (firstByUnitId.isEmpty()) {
                continue;
            }
            var systemUnitConfig = firstByUnitId.get();
            log.info("开始获取登录信息, unitId: {}", unitId);
            var loginInfo = loginService.login(unitId, false);
            JSONObject limitPower;
            if (queryLimit) {
                log.info("开始获取限额, unitId: {}", unitId);
                limitPower = getLimitPower(systemUnitConfig, strategyDate, loginInfo);
                if (limitPower == null) {
                    continue;
                }
            } else {
                limitPower = new JSONObject();
                for (int i = 1; i <= 96; i++) {
                    var key = "h%02d".formatted(i);
                    limitPower.put(key, systemUnitConfig.getLimitPower());
                }
            }
            setLimitPower(limitPower, detailMap);
            agreeConfirmation(strategyDate, loginInfo);
            configDetailList = configDetailList.stream()
                                               .filter(i -> i.getStatus() != 1
                                                            && i.getPercent() != null
                                                            && i.getPercent() != 0)
                                               .toList();
            var success = saveBidPowerInfo(unitMap.get(unitId), strategyDate, configDetailList, loginInfo);
            if (success) {
                configDetailRepo.saveAllAndFlush(configDetailList);
            }
        }
    }

    /**
     * getLimitPower
     *
     * @param unitConfig   unitConfig
     * @param strategyDate strategyDate
     * @param loginInfo    loginInfo
     * @return JSONObject
     */
    public JSONObject getLimitPower(SystemUnitConfig unitConfig, String strategyDate, LoginInfo loginInfo) {
        String businessName = "获取省间日前限额";
        log.info("开始 {}, unitId: {}, strategyDate: {}", businessName, unitConfig.getUnitId(), strategyDate);
        String respStr = newSgccRequest
            .post("/px-psgcc-spotgoods-extranet/dayAheadTradeDeclare/queryPowerLimit",
                JSONObject.of(
                    "tradeDate", strategyDate,
                    "unitId", unitConfig.getDispatchId()
                ), ReqConfig.of(loginInfo, currentRoute));
        log.info("{} unitId: {}, 结果: {}", businessName, unitConfig.getUnitId(), respStr);
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var object = SgccResp.jsonData(sgccResp);
        if (object.isEmpty()) {
            log.info("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, object);
        return object;
    }

    /**
     * 根据限额更新数据
     *
     * @param limitData limitData
     * @param detailMap detailMap
     */
    private void setLimitPower(JSONObject limitData, Map<String, UserOpbdConfigDetail> detailMap) {
        var list = new ArrayList<UserOpbdConfigDetail>();
        for (int i = 1; i <= 96; i++) {
            var detail = detailMap.get(TimeUtil.FIFTEEN_MINUTE_MAP.get(i - 1));
            if (detail != null) {
                var key = "h" + StringUtils.leftPad(String.valueOf(i), 2, "0");
                var limitVal = limitData.getDouble(key);
                if (limitVal == null) {
                    // 限额为空
                    detail.setStatus(2);
                } else if (limitVal < 1) {
                    // 限额为零
                    detail.setPowerLimit(limitVal);
                    detail.setReportPower(0);
                    detail.setReportPrice(detail.getPrice());
                    detail.setStatus(0);
                } else {
                    detail.setPowerLimit(limitVal);
                    var reportPower = BigDecimal.valueOf(detail.getPercent())
                                                .multiply(new BigDecimal(limitVal))
                                                .divide(new BigDecimal(100), 0, RoundingMode.DOWN)
                                                .intValue();
                    // 当限额大于 1 且计算出的申报值为 0 时修改申报值为 1
                    reportPower = detail.getPercent() == 0 ? 0 : reportPower > 0 ? reportPower : 1;
                    detail.setReportPower(reportPower);
                    detail.setReportPrice(detail.getPrice());
                }
                list.add(detail);
            }
        }
        if (!list.isEmpty()) {
            configDetailRepo.saveAllAndFlush(list);
        }
    }

    /**
     * agreeConfirmation
     *
     * @param strategyDate strategyDate
     * @param loginInfo    loginInfo
     */
    public void agreeConfirmation(String strategyDate, LoginInfo loginInfo) {
        var respStr = newSgccRequest
            .post("/px-psgcc-spotgoods-extranet/dayAheadTradeDeclare/getDeclarationConfirmation",
                JSONObject.of("confirmationTime", strategyDate), ReqConfig.of(loginInfo, currentRoute));
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.info("获取申报协议列表失败: {}", respStr);
            return;
        }
        var array = SgccResp.arrayData(sgccResp);
        for (Object o : array) {
            JSONObject json = (JSONObject) o;
            String type = json.getString("pledgetemplateType");
            String version = json.getString("version");
            var respStr2 = newSgccRequest
                .post("/px-psgcc-spotgoods-extranet/dayAheadTradeDeclare/insertConfirmation",
                    JSONObject.of("confirmationTime", LocalDate.now().format(DateConsts.DATE_FORMAT),
                        "type", type, "version", version), ReqConfig.of(loginInfo, currentRoute));
            log.info("同意协议结果: {}", respStr2);
        }
    }

    /**
     * getReportDays
     *
     * @param loginInfo loginInfo
     * @return List<String>
     */
    public List<String> getReportDays(LoginInfo loginInfo) {
        return queryPushDateNow(loginInfo);
    }

    /**
     * 当日可申报的日期
     *
     * @param loginInfo loginInfo
     * @return JSONArray
     */
    public List<String> queryReportableTime(LoginInfo loginInfo) {
        var businessName = "查询可申报日期信息";
        log.info("开始 {}, userName: {}", businessName, loginInfo.getUserName());
        var respStr = newSgccRequest.post("/px-psgcc-spotgoods-extranet/dayAheadTradeDeclare/queryReportableTime",
            JSONObject.of(), ReqConfig.of(loginInfo, currentRoute));
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var array = SgccResp.arrayData(sgccResp);
        if (array.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, array);
        return array.stream()
                    .map(it -> ((String) it).substring(0, 10))
                    .toList();
    }

    /**
     * queryPushDateNow
     *
     * @param loginInfo loginInfo
     * @return JSONArray
     */
    public List<String> queryPushDateNow(LoginInfo loginInfo) {
        String businessName = "查询可申报日期信息";
        log.info("开始 {}, userName: {}", businessName, loginInfo.getUserName());
        var respStr = newSgccRequest.post("/px-psgcc-spotgoods-extranet/dayAheadTradeDeclare/queryPushDateNow",
            JSONObject.of(), ReqConfig.of(loginInfo, currentRoute));
        var sgccResp = JSONObject.parseObject(respStr);
        if (SgccResp.fail(sgccResp)) {
            log.error("{} 失败: {}", businessName, respStr);
            return null;
        }
        var array = SgccResp.arrayData(sgccResp);
        if (array.isEmpty()) {
            log.error("{} 为空: {}", businessName, respStr);
            return null;
        }
        log.info("{} 结果: {}", businessName, array);
        return array.stream()
                    .map(it -> (JSONObject) it)
                    .filter(it -> "1".equals(it.getString("STATE")))
                    .map(it -> it.getString("DECLARE_TARGET_DATE"))
                    .toList();
    }

    /**
     * saveBidPowerInfo
     *
     * @param unitConfig       unitConfig
     * @param strategyDate     strategyDate
     * @param configDetailList configDetailList
     * @param loginInfo        loginInfo
     * @return boolean
     */
    private boolean saveBidPowerInfo(SystemUnitConfig unitConfig, String strategyDate,
                                     List<UserOpbdConfigDetail> configDetailList, LoginInfo loginInfo) {
        var map = configDetailList.stream().collect(Collectors.groupingBy(detail ->
            switch (detail.getReportPower()) {
                case 0 -> 0; // 不申报
                case null -> 2; // 没有限额
                default -> 1; // 正常申报
            }));
        var success = true;
        if (map.get(1) != null && !map.get(1).isEmpty()) {
            var strategyType = configDetailList.getFirst().getStrategyType();
            var bidPowerInfoList = new JSONArray();
            for (int i = 0; i < map.get(1).size(); i++) {
                UserOpbdConfigDetail detail = map.get(1).get(i);
                var bidPowerInfo = new JSONObject();
                bidPowerInfo.put("bandNo", strategyType == 4 ? 1 : 0);
                bidPowerInfo.put("endTime", detail.getEndTime());
                bidPowerInfo.put("index", i + 1);
                bidPowerInfo.put("power", detail.getReportPower());
                bidPowerInfo.put("powerMaxLimit", null);
                bidPowerInfo.put("powerMinLimit", null);
                bidPowerInfo.put("price", detail.getReportPrice());
                bidPowerInfo.put("startTime", detail.getStartTime());
                bidPowerInfo.put("tag", i);
                bidPowerInfo.put("tradeDate", strategyDate);
                bidPowerInfo.put("tradeUnitId", unitConfig.getUnitId());
                bidPowerInfo.put("tradeUnitName", unitConfig.getUnitName());
                bidPowerInfo.put("_XID", "row_" + (i + 1));
                bidPowerInfoList.add(bidPowerInfo);
            }
            var body = new JSONObject();
            body.put("bidPowerInfoList", bidPowerInfoList);
            body.put("bidStatus", "2");
            body.put("bidType", strategyType);
            body.put("dispatchUnitId", unitConfig.getDispatchId());
            body.put("limitFlag", queryLimit ? "on" : "ce");
            body.put("role", "卖方");
            body.put("tradeDate", strategyDate);
            body.put("tradeUnitId", unitConfig.getUnitId());
            paramProcess(body, unitConfig);
            var respStr = newSgccRequest.post("/px-psgcc-spotgoods-extranet/dayAheadTradeDeclare/saveBidPowerInfo",
                body, ReqConfig.of(loginInfo, currentRoute));
            log.info("申报结果: {}", respStr);
            var sgccResp = JSONObject.parseObject(respStr);
            success = SgccResp.ok(sgccResp);
        }
        if (success) {
            // 申报成功或者都不申报就更新状态
            map.forEach((key, value) -> value.forEach(detail -> detail.setStatus(key == 0 ? 1 : key)));
        }
        return success;
    }

    /**
     * paramProcess
     *
     * @param body       body
     * @param unitConfig unitConfig
     */
    public void paramProcess(JSONObject body, SystemUnitConfig unitConfig) {
    }
}
