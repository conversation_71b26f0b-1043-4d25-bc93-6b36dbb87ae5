package cn.com.sgcc.job;

import cn.com.sgcc.business.system.service.SystemService;
import cn.com.sgcc.job.service.PmosTimeService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 定时任务
 */
@Slf4j
@Component
public class CommonJob {

    @Resource
    PmosTimeService pmosTimeService;

    @Resource
    SystemService systemService;

    /**
     * syncTime
     */
    @PostConstruct
    @Scheduled(cron = "${custom.common.syncTimeCron}")
    public void syncTime() {
        pmosTimeService.syncTimeWithPmosServer();
    }

    /**
     * refreshRole
     */
    @Scheduled(cron = "0 10 3 * * ?")
    public void refreshRole() {
        systemService.refreshRole();
    }
}
