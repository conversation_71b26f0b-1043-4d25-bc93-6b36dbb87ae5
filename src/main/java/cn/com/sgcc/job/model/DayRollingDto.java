package cn.com.sgcc.job.model;

import cn.com.sgcc.business.sgcc.autologin.model.dto.LoginInfo;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingConfigDetail;
import cn.com.sgcc.business.sgcc.dayrolling.model.UserDayRollingTimeInfo;
import cn.com.sgcc.business.system.model.SystemUnitConfig;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 任务详情
 */
@Data
public class DayRollingDto {
    SystemUnitConfig unitConfig;
    UserDayRollingTimeInfo timeInfo;
    LocalDateTime declareStartTime;
    String marketId;
    String membersName;
    String membersType;
    LoginInfo loginInfo;
    List<UserDayRollingConfigDetail> details;
}
