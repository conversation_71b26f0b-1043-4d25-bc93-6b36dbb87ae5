package cn.com.sgcc.job;

import static cn.com.sgcc.business.common.model.UserConfigEnum.HLJ_OPID_LAST_ALL_DECLARE;
import static cn.com.sgcc.constants.DateConsts.DATE_FORMAT;

import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.business.common.service.UserConfigService;
import cn.com.sgcc.business.system.service.UnitService;
import cn.com.sgcc.config.ErrMsg;
import cn.com.sgcc.config.role.HasRole;
import cn.com.sgcc.config.role.RoleCondition;
import cn.com.sgcc.constants.RoleConstants;
import cn.com.sgcc.job.service.opid.OpidReportJobContext;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Conditional;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Conditional({RoleCondition.class})
@HasRole(RoleConstants.Type.OPID)
@Slf4j
@RestController
@CrossOrigin
@RequestMapping("/job/opid")
public class OpidReportJob {

    @Resource
    private UnitService unitService;
    @Resource
    private OpidReportJobContext context;
    @Resource
    private UserConfigService configService;

    @Value("${custom.pmos.provinceCode}")
    String provinceCode;

    /**
     * doDeclare
     */
    @Scheduled(cron = "${custom.opid.cron}")
    public void doDeclare() {
        if (!RoleCondition.hasRole(RoleConstants.Type.OPID)) {
            return;
        }
        context.getService(provinceCode).doDeclare();
    }

    /**
     * spotNow
     */
    @PostConstruct
    @Scheduled(cron = "${custom.opid.allCron}")
    public void spotNow() {
        if (!RoleCondition.hasRole(RoleConstants.Type.OPID)) {
            return;
        }
        var day = LocalDate.now().format(DATE_FORMAT);
        var lastDeclare = configService.get(HLJ_OPID_LAST_ALL_DECLARE);
        if (lastDeclare.startsWith(day) && !lastDeclare.endsWith("_0")) {
            log.info("全部申报任务今天已经执行过了: {}", lastDeclare);
            return;
        }
        configService.set(HLJ_OPID_LAST_ALL_DECLARE, "%s_0".formatted(day));
        doDeclareAll(false);
    }

    /**
     * doDeclareAll
     *
     * @param force force
     * @return Result<String>
     */
    @PostMapping("/spotNow")
    @ErrMsg("申报失败")
    public Result<String> doDeclareAll(Boolean force) {
        if (!RoleCondition.hasRole(RoleConstants.Type.OPID)) {
            return Result.fail("没有权限");
        }
        context.getService(provinceCode).doDeclareAll(force == null || force);
        return Result.ok("申报完成");
    }

    /**
     * sync
     */
    @Scheduled(cron = "${custom.opid.sync.cron}")
    public void sync() {
        if (!RoleCondition.hasRole(RoleConstants.Type.OPID)) {
            return;
        }
        var unitList = unitService.getUnitList(RoleConstants.Type.OPID);
        for (var unitConfig : unitList) {
            context.getService(provinceCode).sync(unitConfig.getUnitId());
        }
    }
}
