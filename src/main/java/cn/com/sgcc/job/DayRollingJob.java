package cn.com.sgcc.job;

import cn.com.sgcc.business.common.model.dto.Result;
import cn.com.sgcc.config.role.HasRole;
import cn.com.sgcc.config.role.RoleCondition;
import cn.com.sgcc.constants.RoleConstants;
import cn.com.sgcc.job.service.DayRollingJobService;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Conditional;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * DayRollingJob
 */
@Conditional({RoleCondition.class})
@HasRole(RoleConstants.Type.DAY_ROLLING)
@Slf4j
@Component
@CrossOrigin
@RestController
@RequestMapping("/job/dayRolling")
public class DayRollingJob {

    @Resource
    private DayRollingJobService dayRollingJobService;

    /**
     * refreshByUnitId
     *
     * @param param param
     * @return Result<String>
     */
    @GetMapping("/detectJobByUnitId")
    public Result<String> refreshByUnitId(@RequestParam String param) {
        try {
            JSONObject params = JSONObject.parse(param);
            dayRollingJobService.detectJob(false, true, params);
            return Result.ok();
        } catch (Exception e) {
            log.warn("任务出错", e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * job
     */
    @GetMapping("/detectJob")
    @Scheduled(cron = "${custom.dayRolling.detectCron}")
    public void job() {
        if (!RoleCondition.hasRole(RoleConstants.Type.DAY_ROLLING)) {
            return;
        }
        try {
            dayRollingJobService.detectJob(true, true, null);
        } catch (Exception e) {
            log.warn("任务出错", e);
        }
    }

    /**
     * job2
     */
    @GetMapping("/detectJob2")
    @Scheduled(cron = "${custom.dayRolling.detectCron2}")
    public void job2() {
        if (!RoleCondition.hasRole(RoleConstants.Type.DAY_ROLLING)) {
            return;
        }
        try {
            dayRollingJobService.detectJob(true, false, null);
        } catch (Exception e) {
            log.warn("任务出错", e);
        }
    }

    /**
     * detectJob
     */
    @PostConstruct
    public void detectJob() {
        if (!RoleCondition.hasRole(RoleConstants.Type.DAY_ROLLING)) {
            return;
        }
        try {
            dayRollingJobService.detectJob(false, true, null);
        } catch (Exception e) {
            log.warn("任务出错", e);
        }
    }

    /**
     * 每天凌晨清除缓存信息
     */
    @GetMapping("/clearJob")
    @Scheduled(cron = "0 0 0 * * ?")
    public void clearJob() {
        if (!RoleCondition.hasRole(RoleConstants.Type.DAY_ROLLING)) {
            return;
        }
        try {
            dayRollingJobService.clearJob();
        } catch (Exception e) {
            log.warn("任务出错", e);
        }
    }

    /**
     * refresh
     */
    @GetMapping("/queryDishDataJob")
    @Scheduled(cron = "${custom.dayRolling.queryDishDataCron}")
    public void refresh() {
        if (!RoleCondition.hasRole(RoleConstants.Type.DAY_ROLLING)) {
            return;
        }
        try {
            dayRollingJobService.queryDishDataJob();
        } catch (Exception e) {
            log.warn("任务出错", e);
        }
    }

}
