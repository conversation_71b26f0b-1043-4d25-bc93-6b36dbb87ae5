########## API

###
GET {{http_url}}/api/v1/unitList

###
GET {{http_url}}/api/v1/configList?
    param={%22provinceId%22:398,%22unitIdList%22:[%22a2c00671fabc40e99259580d353e165d%22],%22startDate%22:%222023-12-13%2011:11:30%22,%22endDate%22:%222023-12-13%2011:11:30%22}

###
GET {{http_url}}/api/v1/exportRecordList?
    param={%22provinceId%22:398,%22unitIdList%22:[%22a2c00671fabc40e99259580d353e165d%22],%22startDate%22:%222023-12-13%2011:11:30%22,%22endDate%22:%222023-12-13%2011:11:30%22}

###
PUT {{http_url}}/api/v1/strategyConfigDetailList
Content-Type: application/json

{
  "asd": "324",
  "strategyConfigDetailList": [
  ]
}

########## WS

### 日志
WEBSOCKET {{ws_url}}/ws/logs

### 日滚动
WEBSOCKET {{ws_url}}/ws/dayRolling

########## Jobs

###
GET {{http_url}}/job/opbd/job

###
GET {{http_url}}/job/dayRolling/detectJob

###
GET {{http_url}}/job/dayRolling/queryDishDataJob

###
GET {{http_url}}/job/dayRolling/clearJob

########## Tests

###
POST https://pmos.sx.sgcc.com.cn/px-psgcc-spotgoods-extranet/dayAheadTradeDeclare/getDeclarationConfirmation
Content-Type: application/json;charset=UTF-8
Origin: https://pmos.sx.sgcc.com.cn
Referer: https://pmos.sx.sgcc.com.cn/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
accept-language: zh-CN,zh;q=0.9
accept: application/json, text/plain, */*
clienttag: OUTNET_BROWSE
currentroute: /pxf-psgcc-spotgoods-extranet/day-trade
x-ticket: 066596dc98fc8cbdb71a327fc8b37d60851ff394be8573340f6a112b413b452b0e373522d2fc7494557d984fb1218a3f.348df75a853ad9cc4feaf269c1aee94d220dabf6

{
  "confirmationTime": "2024-01-04"
}

###

