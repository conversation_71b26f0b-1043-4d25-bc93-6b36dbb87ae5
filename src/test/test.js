const fs = require('fs').promises;
const path = require('path');

async function countJavaLines(dirPath) {
    let lineCount = 0;

    try {
        const files = await fs.readdir(dirPath, { withFileTypes: true });
        for (const file of files) {
            const filePath = path.join(dirPath, file.name);
            if (file.isDirectory()) {
                lineCount += await countJavaLines(filePath); // 递归统计子目录
            } else if (path.extname(file.name) === '.ts') {
                const data = await fs.readFile(filePath, 'utf8');
                const lines = data.split('\n').filter(Boolean);
                lineCount += lines.length;
                console.log(`${filePath} 包含${lines.length} 行。`);
            }
        }
    } catch (err) {
        console.error('处理文件时出错:', err);
    }

    return lineCount;
}

// 使用方法
const directoryPath = 'R:\\Files\\Workspace\\Sprixin\\auto-report\\';
countJavaLines(directoryPath).then(totalLines => {
    console.log(`总行数: ${totalLines}`);
}).catch(err => {
    console.error('统计行数出错:', err);
});
