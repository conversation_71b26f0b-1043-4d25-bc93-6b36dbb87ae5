package cn.com.sgcc.login;

import cn.com.sgcc.Application;
import cn.com.sgcc.business.sgcc.autologin.service.SgccLoginService;
import cn.com.sgcc.business.system.model.SystemAccountConfig;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CaptchaTest {
    @Resource
    SgccLoginService loginService;

    @Test
    public void test() {
        var systemAccountConfig = new SystemAccountConfig();
        systemAccountConfig.setUserName("aaa");
        systemAccountConfig.setUKeyCode("MIIxxxxxxxxx");
        systemAccountConfig.setPassWord("bbb");
        loginService.login(systemAccountConfig);
    }
}
