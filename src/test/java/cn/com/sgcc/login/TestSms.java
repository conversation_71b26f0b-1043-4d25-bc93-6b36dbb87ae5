package cn.com.sgcc.login;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Executors;
import java.util.regex.Pattern;

@Slf4j
public class TestSms {
    public static void main1(String[] args) {
        var x = """
                106575369559
                【江西电力】新一代电力交易平台登录短信验证码：728636，请尽快填写完成验证，为保障您的账户安全，请勿外泄。      
                              
                SubId：1
                2024-07-01 17:42:48
                13504038919'""";
        var pattern = Pattern.compile("验证码：(\\d+)");
        var matcher = pattern.matcher(x);
        if (matcher.find()) {
            System.out.println(matcher.group(1));
        }
    }
}
