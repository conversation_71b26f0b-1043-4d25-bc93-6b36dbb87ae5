package cn.com.sgcc.login;

import cn.com.sgcc.util.http.ReqConfig;
import com.alibaba.fastjson2.JSONObject;
import java.util.Map;
import javax.net.ssl.SSLContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.DefaultHttpRequestRetryStrategy;
import org.apache.hc.client5.http.impl.classic.BasicHttpClientResponseHandler;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactoryBuilder;
import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;

@Slf4j
public class HCTest {
    static int defaultHttpTimeout = 30000;
    static String originUrl = "https://pmos.sx.sgcc.com.cn";
    static CloseableHttpClient client;

    public static void main(String[] args) throws Exception {
        init();
        var post = post("/px-spotgoods-province/trade/queryGenerateCurve", JSONObject.of(
            "appType", "1",
            "dayStr", "204-08-04",
            "unitId", "PHBSXPPLKDXNYK02"
        ), ReqConfig.of(null, "/pxf-spotgoods-province-extranet/tradeResultSearchForCreateElectric/TradeResultSearchForCreateElectric"));
        System.out.println(post);
    }

    public static String post(String path, JSONObject body, ReqConfig reqConfig) {
        try {
            var httpPost = new HttpPost(originUrl + path);
            getLoginHeaders().forEach(httpPost::setHeader);
            if (reqConfig.getCurrentRoute() != null) {
                httpPost.setHeader("Currentroute", reqConfig.getCurrentRoute());
            }
            httpPost.setEntity(new StringEntity(body.toString(), ContentType.APPLICATION_JSON));
            return client.execute(httpPost, new BasicHttpClientResponseHandler());
        } catch (Exception e) {
            log.error("调用接口失败", e);
            return null;
        }
    }

    private static Map<String, String> getLoginHeaders() {
        return Map.of("Accept", "application/json, text/plain, */*",
            "Accept-Language", "zh-CN,zh;q=0.9",
            "Clienttag", "OUTNET_BROWSE",
            "Content-Type", "application/json;charset=UTF-8",
            "Currentroute", "/dashboard",
            "user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n",
            "X-Ticket", "066596dc98fc8cbdb71a327fc8b37d60d40bb2e240c583807461a5613364e23014d2e32c4bd4aaef140e9ffce8f4ff7d.da8ec21d155c2027dc3e9a9d10f4deb36d66ab89");
    }

    public static void init() {
        System.setProperty("java.net.preferIPv4Stack", "true");
        SSLContext sslContext = null;
        try {
            sslContext = SSLContextBuilder.create()
                                          .loadTrustMaterial(TrustAllStrategy.INSTANCE)
                                          .build();
        } catch (Exception e) {
            log.error("loadTrustMaterial失败", e);
        }
        var socketFactory = SSLConnectionSocketFactoryBuilder.create()
                                                             .setSslContext(sslContext)
                                                             .build();
        var connectionManager = PoolingHttpClientConnectionManagerBuilder.create()
                                                                         .setMaxConnTotal(256)
                                                                         .setSSLSocketFactory(socketFactory)
                                                                         .build();
        var requestConfig = RequestConfig.custom()
                                         .setConnectionRequestTimeout(Timeout.ofSeconds(defaultHttpTimeout))
                                         .setResponseTimeout(Timeout.ofSeconds(defaultHttpTimeout))
                                         .build();
        client = HttpClients.custom()
                            .setConnectionManager(connectionManager)
                            .setDefaultRequestConfig(requestConfig)
                            .evictIdleConnections(TimeValue.ofSeconds(5))
                            .setKeepAliveStrategy((response, context) -> TimeValue.ofSeconds(10))
                            .setRetryStrategy(DefaultHttpRequestRetryStrategy.INSTANCE)
                            .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                            .build();
    }
}
