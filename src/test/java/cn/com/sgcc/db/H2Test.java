package cn.com.sgcc.db;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.test.context.TestPropertySource;

/**
 * H2数据库独立测试类
 * 完全隔离业务代码，只测试数据库性能
 */
@Slf4j
@SpringBootTest(classes = H2Test.TestConfig.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:file:./spot.db;MODE=MySQL;AUTO_SERVER=TRUE;DATABASE_TO_LOWER=TRUE;CACHE_SIZE=65536",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.datasource.username=sa",
    "spring.datasource.password=",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.datasource.hikari.maximum-pool-size=10",
    "spring.jpa.show-sql=false",
    "spring.h2.console.enabled=true",
    "spring.h2.console.path=/h2",
    "logging.level.org.hibernate=WARN",
    "logging.level.org.springframework=WARN"
})
public class H2Test {

    @Autowired
    private CleanTestRepo repo;

    @BeforeEach
    public void setUp() {
        // 清理数据库，确保每个测试独立
        repo.deleteAll();
    }

    /**
     * 完全独立的测试配置类
     * 只扫描当前测试类，不会加载任何业务代码
     */
    @SpringBootApplication
    @ComponentScan(basePackageClasses = H2Test.class)  // 只扫描当前测试类
    @EntityScan(basePackageClasses = H2Test.class)     // 只扫描当前测试类的实体
    @EnableJpaRepositories(basePackageClasses = H2Test.class)  // 只启用当前测试的Repository
    public static class TestConfig {
        // 完全独立的配置类
    }

    /**
     * 测试专用实体类
     */
    @Data
    @Entity
    @Table(name = "clean_test_table")
    public static class CleanTestEntity {
        @Id
        @GeneratedValue(strategy = GenerationType.AUTO)
        private Long id;

        @Column(name = "test_name")
        private String testName;

        @Column(name = "test_value")
        private Integer testValue;

        @Column(name = "create_time")
        private LocalDateTime createTime;

        @Column(name = "thread_name")
        private String threadName;
    }

    @Test
    public void testBasicConnection() {
        log.info("H2数据库基础连接测试");

        CleanTestEntity entity = new CleanTestEntity();
        entity.setTestName("基础测试");
        entity.setTestValue(100);
        entity.setCreateTime(LocalDateTime.now());
        entity.setThreadName(Thread.currentThread().getName());

        repo.save(entity);
        long count = repo.count();
        log.info("基础测试通过，记录数: {}", count);

        assert count == 1;
    }

    @Test
    public void testConcurrentWrite() throws Exception {
        log.info("H2并发写入测试");

        int threads = 5;
        int recordsPerThread = 200;

        ExecutorService executor = Executors.newFixedThreadPool(threads);
        CountDownLatch latch = new CountDownLatch(threads);

        long start = System.currentTimeMillis();

        for (int i = 0; i < threads; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    List<CleanTestEntity> batch = new ArrayList<>();
                    for (int j = 0; j < recordsPerThread; j++) {
                        CleanTestEntity entity = new CleanTestEntity();
                        entity.setTestName("Thread-" + threadId + "-Record-" + j);
                        entity.setTestValue(j);
                        entity.setCreateTime(LocalDateTime.now());
                        entity.setThreadName("Thread-" + threadId);
                        batch.add(entity);
                    }
                    repo.saveAll(batch);
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        long duration = System.currentTimeMillis() - start;
        long count = repo.count();

        log.info("H2并发测试完成 - 线程数:{}, 每线程记录:{}, 总耗时:{}ms, 实际记录:{}, TPS:{}",
            threads, recordsPerThread, duration, count, count * 1000.0 / duration);

        assert count == threads * recordsPerThread;

        try {
            Thread.sleep(1000000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testBatchInsert() {
        log.info("H2批量插入测试");

        int batchSize = 100;
        int totalRecords = 1000;

        long start = System.currentTimeMillis();

        List<CleanTestEntity> allEntities = new ArrayList<>();
        for (int i = 0; i < totalRecords; i++) {
            CleanTestEntity entity = new CleanTestEntity();
            entity.setTestName("Batch-Record-" + i);
            entity.setTestValue(i);
            entity.setCreateTime(LocalDateTime.now());
            entity.setThreadName("BatchThread");
            allEntities.add(entity);
        }

        // 分批保存
        for (int i = 0; i < allEntities.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, allEntities.size());
            List<CleanTestEntity> batch = allEntities.subList(i, endIndex);
            repo.saveAll(batch);
        }

        long duration = System.currentTimeMillis() - start;
        long count = repo.count();

        log.info("H2批量插入测试完成 - 记录数:{}, 耗时:{}ms, TPS:{}",
            count, duration, count * 1000.0 / duration);

        assert count == totalRecords;
        try {
            Thread.sleep(1000000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
