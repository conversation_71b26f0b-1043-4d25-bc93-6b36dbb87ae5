package cn.com.sgcc.business.shandong.trade.service;

import cn.com.sgcc.business.shandong.trade.model.UserSdSpotConfig;
import cn.com.sgcc.business.shandong.trade.repository.UserSdSpotConfigRepo;
import cn.com.sgcc.business.shandong.trade.service.impl.SdSpotConfigServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 山东现货申报配置服务测试类
 */
@ExtendWith(MockitoExtension.class)
class SdSpotConfigServiceTest {

    @Mock
    private UserSdSpotConfigRepo configRepo;

    @InjectMocks
    private SdSpotConfigServiceImpl spotConfigService;

    private UserSdSpotConfig testConfig;

    @BeforeEach
    void setUp() {
        testConfig = new UserSdSpotConfig();
        testConfig.setAccount("test_account");
        testConfig.setStrategyDate("2024-01-01");
        testConfig.setTime("09:00");
        testConfig.setStrategyType(1);
        testConfig.setReportType(1);
        testConfig.setPower(100.0);
        testConfig.setReportPower(100.0);
        testConfig.setStatus(0);
    }

    @Test
    void testQueryConfigList() {
        // Given
        List<UserSdSpotConfig> expectedConfigs = Arrays.asList(testConfig);
        when(configRepo.findByAccountAndStrategyDate("test_account", "2024-01-01"))
                .thenReturn(expectedConfigs);

        // When
        List<UserSdSpotConfig> result = spotConfigService.queryConfigList("test_account", "2024-01-01");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test_account", result.get(0).getAccount());
        verify(configRepo).findByAccountAndStrategyDate("test_account", "2024-01-01");
    }

    @Test
    void testSaveConfigList() {
        // Given
        List<UserSdSpotConfig> configList = Arrays.asList(testConfig);
        when(configRepo.saveAll(anyList())).thenReturn(configList);

        // When
        boolean result = spotConfigService.saveConfigList("test_account", "2024-01-01", configList);

        // Then
        assertTrue(result);
        verify(configRepo).deleteByAccountAndStrategyDate("test_account", "2024-01-01");
        verify(configRepo).saveAll(anyList());
    }

    @Test
    void testUpdateReportStatus() {
        // Given
        when(configRepo.updateReportPowerAndStatus(anyString(), anyString(), anyString(), 
                anyDouble(), anyInt(), anyString())).thenReturn(1);

        // When & Then
        assertDoesNotThrow(() -> 
                spotConfigService.updateReportStatus("test_account", "2024-01-01", "09:00", 100.0, 1));
        
        verify(configRepo).updateReportPowerAndStatus(eq("test_account"), eq("2024-01-01"), 
                eq("09:00"), eq(100.0), eq(1), anyString());
    }

    @Test
    void testUpdateReportStatusNotFound() {
        // Given
        when(configRepo.updateReportPowerAndStatus(anyString(), anyString(), anyString(), 
                anyDouble(), anyInt(), anyString())).thenReturn(0);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> 
                spotConfigService.updateReportStatus("test_account", "2024-01-01", "09:00", 100.0, 1));
        
        assertEquals("更新失败，未找到对应记录", exception.getMessage());
    }

    @Test
    void testDeleteConfig() {
        // When
        spotConfigService.deleteConfig("test_account", "2024-01-01");

        // Then
        verify(configRepo).deleteByAccountAndStrategyDate("test_account", "2024-01-01");
    }
}
