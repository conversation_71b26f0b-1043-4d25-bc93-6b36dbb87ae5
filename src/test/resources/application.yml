server:
  port: 8081
  compression:
    enabled: true
logging:
  config: classpath:logback-spring.xml
  file:
    name: D:/spot/logs
spring:
  application:
    name: auto-report
  datasource:
    url: ***************************
    driver-class-name: org.sqlite.JDBC
  jpa:
    database-platform: org.hibernate.community.dialect.SQLiteDialect
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    open-in-view: true
    properties:
      hibernate:
        format_sql: false
  servlet:
    multipart:
      max-file-size: 52428800
  task:
    scheduling:
      pool:
        size: 128
      thread-name-prefix: task-
      shutdown:
        await-termination: false
      simple:
        concurrency-limit: -1
    execution:
      pool:
        core-size: 128
        max-size: 256
        queue-capacity: 128
      thread-name-prefix: async-
custom:
  role:
    text: "H4sIAAAAAAAA/xWQyREDQQgDU4LhDocz/xC8flIqQTfApdXMEGFVgznYOoCmCNMg2zLvHkz4uQ1ZC1zlkAZvwitN+2dNRqe5F8D5BF7Uyo7CzlxVyj5g6FuGbLww1j2r50JtKWjEzXbp47VTmJnbrH7SfcdBqvRoHqut6CFjmXo2baRKNM8HdxHoIJ31sg0p7TPwh5dRQ4SbaPi1PmSVgnvlTjLuCYiLc+c6pmQDCy7BItK1sAFZiPLggHy1cr8l8M3zGb8PfBiUAwp8ENkZRDWfP5bvbgsVbXvbZ5wT+8jqc9lsx7MP5uFy2dWZf99rD4tCuf0BLzVL754BAAA="
  pmos:
    httpWaitMilliSecond: 2000 # 每次请求等待间隔（毫秒，正整数）
    provinceCode: sx # 省份编码: [gs, hn, jibei, jx, sn, sx]
    queryLimit: true # 省间申报查询限额或者根据装机容量比例 {true: 查询限额, false: 使用装机容量}
    url:
      domain: 127.0.0.1:48000 # pmos.sx.sgcc.com.cn
      origin: http://127.0.0.1:48000 # https://pmos.sx.sgcc.com.cn
    login:
      httpWaitMilliSecond: 500 # 每次请求等待间隔（毫秒，正整数）
      blockPuzzle:
        retry: 5 # 解析验证码尝试次数，取值范围: [0, 10]，0 就关闭登录功能了
        maxPixelMatch: 30 # 像素匹配值，小于此值则视为成功
        image:
          width: 310
          height: 155
          jigsawWidth: 47
          showWidth: 330
        filter:
          r: 250
          g: 250
          b: 250
      retry: 1 # 登录尝试次数，取值范围: [0, 10]，0 就关闭自动登录功能了
  common:
    syncTimeCron: "40 49 * * * ?" # 每小时同步一次时间
  dayRolling:
    detectCron: "0 30 9 * * ?" # 每天 9:30 获取当天开始结束时间
    detectCron2: "0 30 16 * * ?" # 每天 16:30 获取第二天开始结束时间
    queryDishDataCron: "4/5 * * * * ?" # 盯盘数据每 5 秒钟刷新一次
    preparationTimeInSecond: 15 # 提前几秒准备登录
    waitTimeInMillSecond: 2100 # 抢报延迟几毫秒
    encryptParticipantId: # participantId 加密参数
      encryptionKey: "wInLjbNCemFkZKPhr2UQWs3T9Xuv4VBcx5AY1HzMS0p6to87ifDalRJqgGdOyE"
      encryptLevel: 12
  opbd:
    startCron: "0 30 9 * * ?" # 每天 9:30 获取当天开始结束时间
  opid:
    cron: "10 * * * * ?" # 每分钟检测
    range:
      start: 8 # 申报开始时间，根据交易中心限制的结束时间计算（单位：分钟）。例：18:10 分结束，10-8=2 则 18:02 分开始申报
      end: 2 # 申报结束时间，根据交易中心限制的结束时间计算（单位：分钟）。例：18:10 分结束，10-2=8 则 18:08 分结束申报
    sync:
      cron: 0 15 0/2 * * ?
      url: http://***************:8080/spotAutoReport/sync2
