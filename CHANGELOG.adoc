= CHANGELOG
:toc: right
:toclevels: 1
:nofooter:

== 2.0 (2025-7-19)

=== Updates

- 升级为 Java 24
- 升级为 Spring Boot 3.5.3
- 开启虚拟线程
- 优化 GraalVM Native 打包；移除 polyglot，加密改为原生（虚拟线程与polyglot冲突）
- 数据库改为 h2

== 1.10 (2025-7-11)

=== New Features

- 权限控制改为云端验证

=== Updates

- 高并发时数据库busy问题修复

== 1.9 (2025-04-18)

=== New Features

- 省间申报新增对黑龙江的支持
- 省间收益分析新增对黑龙江的支持
- 省间日内申报山西新增自动策略模式

=== Updates

- 各省适配改为策略模式实现，方便后期兼容多省份
- 前端窗口监听模式改为pinia订阅模式

== 1.8 (2025-01-20)

=== New Features

- 省间申报新增对冀北的支持

== 1.7 (2024-09-18)

=== New Features

- 新增日滚动交易记录爬取与展示功能

== 1.6 (2024-07-24)

=== New Features

- 新增新版登录无须UKey账号的适配
- 新增支持短信验证码登录

=== Updates

- 移除电网代购电功能
- 移除旧版登录逻辑
- 自动登录脚本适配新版登录

== 1.5 (2024-06-21)

=== New Features

- 新增江西保量保价新能源合同交易自动申报

== 1.4  (2024-06-20)

=== New Features

- 新增山西代购电自动申报

=== Updates

- 移除 Websocket，替换为 SSE 实现
- 无权限的功能自动任务不再执行

== 1.3 (2024-05-23)

=== New Features

- 新增山西策略收益分析功能
- 新增对湖南、甘肃、江西、陕西省份的支持

== 1.2 (2024-02-26)

=== New Features

- 新增省间日前自动申报
- 新增日滚动自动申报
- 新增自动登录功能
- 新增日志展示
- 新增权限验证方式

=== Updates

- 升级为 Java 21
- 升级为 Spring Boot 3.2.1
- 引入 Spring Data JPA
- 引入 Websocket
- 新增 GraalVM Native 打包
- 更改数据库表名的命名方式

== 1.1 (2023-12-19)

=== New Features

- 更改任务创建模式

=== Updates

- 新增 NaiveUI

== 1.0 (2023-10-17)

- 创建项目
- 省间日内自动申报
- 新增前端代码
